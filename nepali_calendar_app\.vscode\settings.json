{
  // Dar<PERSON> & Flutter Settings - Error Free
  "dart.flutterSdkPath": null,
  "dart.analysisServerFolding": false,
  "dart.previewFlutterUiGuides": false,
  "dart.previewFlutterUiGuidesCustomTracking": false,
  "dart.flutterOutline": false,
  "dart.closingLabels": false,
  "dart.lineLength": 120,
  "dart.enableSdkFormatter": true,
  "dart.analysisExcludedFolders": [
    "build",
    ".dart_tool",
    "android",
    "ios",
    "web",
    "windows",
    "linux",
    "macos"
  ],

  // Java Settings for Android
  "java.home": "C:\\Program Files\\Java\\jdk-17",
  "java.configuration.runtimes": [
    {
      "name": "JavaSE-17",
      "path": "C:\\Program Files\\Java\\jdk-17"
    }
  ],
  
  // Editor Performance
  "editor.semanticHighlighting.enabled": false,
  "editor.bracketPairColorization.enabled": false,
  "editor.guides.bracketPairs": false,
  "editor.minimap.enabled": false,
  "editor.codeLens": false,
  "editor.lightbulb.enabled": false,
  "editor.hover.delay": 1000,
  "editor.quickSuggestions": {
    "other": false,
    "comments": false,
    "strings": false
  },
  
  // File Watcher Optimization
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/android/**": true,
    "**/ios/**": true,
    "**/web/**": true,
    "**/windows/**": true,
    "**/linux/**": true,
    "**/macos/**": true
  },
  
  // Search Exclusions
  "search.exclude": {
    "**/build": true,
    "**/.dart_tool": true,
    "**/android": true,
    "**/ios": true,
    "**/web": true,
    "**/windows": true,
    "**/linux": true,
    "**/macos": true
  },
  
  // Git Performance
  "git.enabled": false,
  "git.autorefresh": false,
  
  // Extensions Performance
  "extensions.autoUpdate": false,
  "extensions.autoCheckUpdates": false,
  
  // Telemetry
  "telemetry.telemetryLevel": "off"
}
