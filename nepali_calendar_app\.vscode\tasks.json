{"version": "2.0.0", "tasks": [{"label": "Flutter: Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Flutter: Get Dependencies", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Flutter: Build APK (Debug)", "type": "shell", "command": "flutter", "args": ["build", "apk", "--debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Flutter: Run (Chrome)", "type": "shell", "command": "flutter", "args": ["run", "-d", "chrome"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Java Version Check", "type": "shell", "command": "java", "args": ["-version"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "<PERSON> G<PERSON><PERSON>", "type": "shell", "command": "powershell", "args": ["-Command", "Remove-Item -Recurse -Force \"$env:USERPROFILE\\.gradle\\caches\" -ErrorAction SilentlyContinue; Write-Host 'Gradle cache cleaned'"], "group": "build"}, {"label": "Flutter: Build APK (Java 17)", "type": "shell", "command": "flutter", "args": ["build", "apk", "--debug"], "group": "build", "options": {"env": {"JAVA_HOME": "C:\\Program Files\\Java\\jdk-17"}}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}