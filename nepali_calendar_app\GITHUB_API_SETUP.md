# 🚀 GitHub API Key Management Setup

## ⚡ Quick Setup (2 minutes)

### 1️⃣ Create GitHub Repository
1. Go to [github.com](https://github.com)
2. Click **"New"** repository
3. Name: `nepali-calendar-config`
4. Make it **Public**
5. Click **"Create repository"**

### 2️⃣ Add Configuration File
1. Click **"Add file"** → **"Create new file"**
2. File name: `api-config.json`
3. Add this content:

```json
{
  "vedic_api_key": "c3ebdd09-6bef-58cc-80c0-f48262259bfe",
  "api_base_url": "https://api.vedicastroapi.com/v3-json",
  "backup_key_1": "your-backup-key-here",
  "last_updated": "2024-01-15"
}
```

4. Click **"Commit new file"**

### 3️⃣ Get Raw URL
1. Click on your `api-config.json` file
2. Click **"Raw"** button
3. Copy the URL from address bar

**URL Format:**
```
https://raw.githubusercontent.com/YOUR_USERNAME/nepali-calendar-config/main/api-config.json
```

### 4️⃣ Update App Code
1. Open `lib/services/github_api_manager.dart`
2. Replace `YOUR_USERNAME` in `_configUrl` with your GitHub username:

```dart
static const String _configUrl = 'https://raw.githubusercontent.com/YOUR_USERNAME/nepali-calendar-config/main/api-config.json';
```

## 🔄 How to Update API Key

### When Your API Key Expires:
1. Go to your GitHub repository
2. Click on `api-config.json`
3. Click **"Edit"** (pencil icon)
4. Change the `vedic_api_key` value
5. Click **"Commit changes"**

**That's it!** Your app will automatically use the new key within 30 minutes.

## ✅ Benefits

- **⚡ Super Fast**: 2-minute setup
- **🆓 Free Forever**: GitHub is free
- **🔄 Instant Updates**: Edit and save
- **📱 No App Update**: Users don't need to update
- **🛡️ Automatic Fallback**: Uses local storage if GitHub is down
- **🧪 API Testing**: Automatically tests if keys work

## 🔧 Advanced Features

### Multiple Backup Keys:
```json
{
  "vedic_api_key": "primary-key",
  "backup_key_1": "backup-key-1",
  "backup_key_2": "backup-key-2"
}
```

### Different API Base URL:
```json
{
  "vedic_api_key": "your-key",
  "api_base_url": "https://different-api.com/v1"
}
```

## 🐛 Troubleshooting

### If API calls fail:
1. Check if your GitHub repository is **Public**
2. Verify the raw URL is correct
3. Make sure JSON format is valid
4. Check app logs for error messages

### Test your setup:
```dart
// Add this to test your configuration
GitHubApiManager.showSetupInstructions();
print(GitHubApiManager.getStatus());
```

## 📱 App Integration

The GitHub API manager is already integrated into:
- ✅ Mangal Dosh analysis
- 🔄 Manglik Dosh analysis (update needed)
- 🔄 Kaalsarp Dosh analysis (update needed)

## 🎯 Next Steps

1. Complete the setup above
2. Test with a new API key
3. Update other API calls to use GitHubApiManager
4. Enjoy automatic API key management!

---

**🎉 You now have the simplest possible API key management system!**
