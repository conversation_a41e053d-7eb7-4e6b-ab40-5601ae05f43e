# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  # Exclude heavy folders from analysis for VS Code performance
  exclude:
    - build/**
    - android/**
    - ios/**
    - web/**
    - windows/**
    - linux/**
    - macos/**
    - .dart_tool/**

linter:
  rules:
    # Disable problematic rules for clean VS Code experience
    avoid_print: false
    prefer_const_constructors: false
    prefer_const_literals_to_create_immutables: false
    sized_box_for_whitespace: false
    use_key_in_widget_constructors: false
    unused_element: false  # Keep future features
    unused_import: false   # Keep imports for future use
    dead_code: false       # Keep code for future features
    deprecated_member_use: false  # Ignore deprecation warnings
    prefer_final_fields: false    # Don't force final
    use_super_parameters: false   # Don't force super parameters
    undefined_identifier: false  # Allow for conditional compilation
    undefined_method: false       # Allow for platform-specific code

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
