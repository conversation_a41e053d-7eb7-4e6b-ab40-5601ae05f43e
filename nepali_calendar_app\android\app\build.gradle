plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace "nepali.patro"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    buildFeatures {
        buildConfig false
    }



    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId "nepali.patro"
        minSdk flutter.minSdkVersion
        targetSdk flutter.targetSdkVersion
        versionCode flutter.versionCode
        versionName flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Import the Firebase BoM
    // implementation platform('com.google.firebase:firebase-bom:33.16.0')

    // Firebase Analytics
    // implementation 'com.google.firebase:firebase-analytics'

    // Firebase Firestore
    // implementation 'com.google.firebase:firebase-firestore'
}
