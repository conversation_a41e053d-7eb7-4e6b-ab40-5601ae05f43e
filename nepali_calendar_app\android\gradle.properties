# Gradle JVM settings for performance (reduced for storage issues)
org.gradle.jvmargs=-Xmx1G -XX:MaxMetaspaceSize=512M -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# Force Gradle to use Java 17 (VS Code compatibility)
org.gradle.java.home=C:\\Program Files\\Java\\jdk-17

# Performance optimizations for VS Code
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Kotlin compiler optimizations
kotlin.code.style=official
kotlin.incremental=true

# Network optimization for storage issues
systemProp.http.connectionTimeout=60000
systemProp.http.socketTimeout=60000
systemProp.https.connectionTimeout=60000
systemProp.https.socketTimeout=60000

# Android optimization
android.enableR8.fullMode=true

# Fix for Android Gradle Plugin compatibility
# Future-proof settings to prevent build issues
android.suppressUnsupportedCompileSdk=34
android.nonTransitiveRClass=false
android.nonFinalResIds=false
