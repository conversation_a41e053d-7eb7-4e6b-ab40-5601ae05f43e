<lint-module
    format="1"
    dir="D:\neppalipatro\nepali_calendar_app\android\app"
    name=":app"
    type="APP"
    maven="android:app:"
    agpVersion="8.2.2"
    buildFolder="D:\neppalipatro\nepali_calendar_app\build\app"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
