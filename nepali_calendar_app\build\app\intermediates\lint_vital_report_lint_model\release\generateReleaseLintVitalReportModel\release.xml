<variant
    name="release"
    package="nepali.patro"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\neppalipatro\nepali_calendar_app\build\app\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="D:\neppalipatro\nepali_calendar_app\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\neppalipatro\nepali_calendar_app\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.2.2;C:\Users\<USER>\Desktop\flutter\src\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\neppalipatro\nepali_calendar_app\build\app\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\neppalipatro\nepali_calendar_app\build\app\intermediates\javac\release\classes;D:\neppalipatro\nepali_calendar_app\build\app\tmp\kotlin-classes\release;D:\neppalipatro\nepali_calendar_app\build\app\kotlinToolingMetadata;D:\neppalipatro\nepali_calendar_app\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      type="MAIN"
      applicationId="nepali.patro"
      generatedSourceFolders="D:\neppalipatro\nepali_calendar_app\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\neppalipatro\nepali_calendar_app\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\b57da101b744d79429044dd3b0c900c7\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
