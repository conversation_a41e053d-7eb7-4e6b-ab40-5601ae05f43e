1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="nepali.patro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Internet permission for ads -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:4:5-79
12-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:5:5-76
13-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:5:22-73
14    <!--
15         Required to query activities that can process text, see:
16         https://developer.android.com/training/package-visibility and
17         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
18
19         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
20    -->
21    <queries>
21-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:46:5-51:15
22        <intent>
22-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:47:9-50:18
23            <action android:name="android.intent.action.PROCESS_TEXT" />
23-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:48:13-72
23-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:48:21-70
24
25            <data android:mimeType="text/plain" />
25-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:49:13-50
25-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:49:19-48
26        </intent>
27    </queries>
28
29    <permission
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
30        android:name="nepali.patro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="nepali.patro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
34
35    <application
36        android:name="android.app.Application"
36-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:9:9-42
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
38        android:extractNativeLibs="true"
39        android:icon="@mipmap/ic_launcher"
39-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:10:9-43
40        android:label="nepali_calendar_app" >
40-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:8:9-44
41        <activity
41-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:11:9-32:20
42            android:name="nepali.patro.MainActivity"
42-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:12:13-41
43            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
43-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:17:13-163
44            android:exported="true"
44-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:13:13-36
45            android:hardwareAccelerated="true"
45-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:18:13-47
46            android:launchMode="singleTop"
46-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:14:13-43
47            android:taskAffinity=""
47-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:15:13-36
48            android:theme="@style/LaunchTheme"
48-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:16:13-47
49            android:windowSoftInputMode="adjustResize" >
49-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:19:13-55
50
51            <!--
52                 Specifies an Android theme to apply to this Activity as soon as
53                 the Android process has started. This theme is visible to the user
54                 while the Flutter UI initializes. After that, this theme continues
55                 to determine the Window background behind the Flutter UI.
56            -->
57            <meta-data
57-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:24:13-27:17
58                android:name="io.flutter.embedding.android.NormalTheme"
58-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:25:15-70
59                android:resource="@style/NormalTheme" />
59-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:26:15-52
60
61            <intent-filter>
61-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:28:13-31:29
62                <action android:name="android.intent.action.MAIN" />
62-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:29:17-68
62-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:29:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:30:17-76
64-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:30:27-74
65            </intent-filter>
66        </activity>
67        <!--
68             Don't delete the meta-data below.
69             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
70        -->
71        <meta-data
71-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:35:9-37:33
72            android:name="flutterEmbedding"
72-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:36:13-44
73            android:value="2" />
73-->D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:37:13-30
74        <!-- AdMob App ID (Test App ID) -->
75
76        <activity
76-->[:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:8:9-11:74
77            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
77-->[:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:9:13-74
78            android:exported="false"
78-->[:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:10:13-37
79            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
79-->[:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-71
80
81        <uses-library
81-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
82            android:name="androidx.window.extensions"
82-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
83            android:required="false" />
83-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
84        <uses-library
84-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
85            android:name="androidx.window.sidecar"
85-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
86            android:required="false" />
86-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
87
88        <provider
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
89            android:name="androidx.startup.InitializationProvider"
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
90            android:authorities="nepali.patro.androidx-startup"
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
91            android:exported="false" >
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
92            <meta-data
92-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
93-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
94                android:value="androidx.startup" />
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
95            <meta-data
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
97                android:value="androidx.startup" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
98        </provider>
99
100        <receiver
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
101            android:name="androidx.profileinstaller.ProfileInstallReceiver"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
102            android:directBootAware="false"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
103            android:enabled="true"
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
104            android:exported="true"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
105            android:permission="android.permission.DUMP" >
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
107                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
110                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
113                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
116                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
117            </intent-filter>
118        </receiver>
119    </application>
120
121</manifest>
