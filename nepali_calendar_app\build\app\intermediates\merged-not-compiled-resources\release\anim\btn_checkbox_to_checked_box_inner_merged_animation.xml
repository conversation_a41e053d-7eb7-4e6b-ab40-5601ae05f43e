<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<set xmlns:android="http://schemas.android.com/apk/res/android">
    <objectAnimator
            android:duration="166"
            android:propertyName="pathData"
            android:valueFrom="M -7.0,-7.0 l 14.0,0.0 c 0.0,0.0 0.0,0.0 0.0,0.0 l 0.0,14.0 c 0.0,0.0 0.0,0.0 0.0,0.0 l -14.0,0.0 c 0.0,0.0 0.0,0.0 0.0,0.0 l 0.0,-14.0 c 0.0,0.0 0.0,0.0 0.0,0.0 Z M 7.0,-9.0 c 0.0,0.0 -14.0,0.0 -14.0,0.0 c -1.**********,0.0 -2.0,0.********** -2.0,2.0 c 0.0,0.0 0.0,14.0 0.0,14.0 c 0.0,1.********** 0.**********,2.0 2.0,2.0 c 0.0,0.0 14.0,0.0 14.0,0.0 c 1.**********,0.0 2.0,-0.********** 2.0,-2.0 c 0.0,0.0 0.0,-14.0 0.0,-14.0 c 0.0,-1.********** -0.**********,-2.0 -2.0,-2.0 c 0.0,0.0 0.0,0.0 0.0,0.0 Z"
            android:valueTo="M 0.0,-0.05 l 0.0,0.0 c 0.02761423749,0.0 0.05,0.02238576251 0.05,0.05 l 0.0,0.0 c 0.0,0.02761423749 -0.02238576251,0.05 -0.05,0.05 l 0.0,0.0 c -0.02761423749,0.0 -0.05,-0.02238576251 -0.05,-0.05 l 0.0,0.0 c 0.0,-0.02761423749 0.02238576251,-0.05 0.05,-0.05 Z M 7.0,-9.0 c 0.0,0.0 -14.0,0.0 -14.0,0.0 c -1.**********,0.0 -2.0,0.********** -2.0,2.0 c 0.0,0.0 0.0,14.0 0.0,14.0 c 0.0,1.********** 0.**********,2.0 2.0,2.0 c 0.0,0.0 14.0,0.0 14.0,0.0 c 1.**********,0.0 2.0,-0.********** 2.0,-2.0 c 0.0,0.0 0.0,-14.0 0.0,-14.0 c 0.0,-1.********** -0.**********,-2.0 -2.0,-2.0 c 0.0,0.0 0.0,0.0 0.0,0.0 Z"
            android:valueType="pathType"
            android:interpolator="@interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1"/>
    <set android:ordering="sequentially">
        <objectAnimator
                android:duration="166"
                android:propertyName="fillAlpha"
                android:valueFrom="1.0"
                android:valueTo="1.0"
                android:interpolator="@android:interpolator/linear"/>
        <objectAnimator
                android:duration="33"
                android:propertyName="fillAlpha"
                android:valueFrom="1.0"
                android:valueTo="0.0"
                android:interpolator="@interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0"/>
    </set>
</set>
