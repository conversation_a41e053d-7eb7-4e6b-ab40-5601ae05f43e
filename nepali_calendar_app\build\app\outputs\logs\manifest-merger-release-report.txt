-- Merging decision tree log ---
application
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:7:5-40:19
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:7:5-40:19
MERGED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-12:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\11766febb41e2e4ca69c2fa222e91fec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\11766febb41e2e4ca69c2fa222e91fec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:8:9-44
	android:icon
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:10:9-43
	android:name
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:9:9-42
manifest
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:1:1-52:12
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:1:1-52:12
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:1:1-52:12
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:1:1-52:12
MERGED from [:audioplayers_android] D:\neppalipatro\nepali_calendar_app\build\audioplayers_android\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:connectivity_plus] D:\neppalipatro\nepali_calendar_app\build\connectivity_plus\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] D:\neppalipatro\nepali_calendar_app\build\path_provider_android\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] D:\neppalipatro\nepali_calendar_app\build\shared_preferences_android\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-14:12
MERGED from [:webview_flutter_android] D:\neppalipatro\nepali_calendar_app\build\webview_flutter_android\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\4a79e0f8b7ae8de9a3de54a629784bb9\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\af1cc6ca09010484bc2099e83a79df50\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bc7f1e0373ec4de2ed1fe579301e3a4\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c279ff4160961cb6026b007ac2b3e26\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bf598e9b72a7f1f47b48d311421bc0c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ddb6f116af13634073021d08dc96cc2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbc81ee2fd81c3e6c717d7a88cae23e\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4938b093760eae79c2bd1c6c78efcc52\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\564ecb7857f3deff7a641bbaa962df7c\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\178c1a17b2fbb1b765e41975f6aebbfc\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\d4a788eee2196536a88a80538393531e\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\752b369dd190003a98d4ddcc422e2d5c\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f19bec46893ac087c6aa9fab40ce0c2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9efad71779c4c331ff66e1d5ce9bd8ed\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\95d8211e4d7ae8ae37bdf0168b220632\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c96fba37ff992e143937e715db082cd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8e4fb06ef5ee1a663193f7926a74dc7\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb44ebb6e4a36599d85e5180f77f3c01\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c56f68c43d5095d0880f3540dcb17f5\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0676bfc61694b9d84ae1e85a63497530\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\73c061a7cecdcc96d85e601cba8ffb56\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e153f3c6651ec93485c56b387c0bbf5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3f3f142b0b9652438a946cc6c1c8964\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c73d321937e86235459c6d34df2c43b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\5ea4b483c2be9990062376c427efdcf3\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1dc5dd79a74fc81c110cb10ead2c25a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dcfc0bed31e34f3f2a25c92480684a5\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaa3d856e9fd245d1f77540c21104bf0\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df61ca1e8c7c58d166f9739d5768072\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aef8b987a1c32239d0034a44f473bd3\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\933c0692a1a090a0c6a9969b48f401a1\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0905ac793aaaadeea0d4cd0bc66f548\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\126252d498372bb69b628dddffe3b91b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\45584b3a56faa15c19959cde768172fe\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e35eec6184d090b3ec837bc01b08222d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c499e93a3a3a559cddd64463647db9b5\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\123e0a77d137e82ceb2021d6816ac756\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\561cd99562411b713da844b479f2e87f\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\5db6f5c66312167b0dac9ce32231d981\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\06c395eff6f61c3052beb29bd1942085\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\11766febb41e2e4ca69c2fa222e91fec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c3846f62ed5b4b67d8fbb2e5490875\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\efb00e0b9cdae5f620f328e2ab6e0de5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0aa21a3298e0d5de09777c2d3c3d0f5c\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\287192a1ae46c2bad3673fd6eb49053d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d07d6ad26fa7285dc55a4f27d24c380d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9a78c058b737f288941da7521080151\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8bc41973cd832e52da15338849e7516\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\3fa489be5becf2a0c7c8820a9e05f602\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:4:5-79
MERGED from [:connectivity_plus] D:\neppalipatro\nepali_calendar_app\build\connectivity_plus\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] D:\neppalipatro\nepali_calendar_app\build\connectivity_plus\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:5:5-76
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:5:22-73
queries
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:46:5-51:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:47:9-50:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:48:13-72
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:48:21-70
data
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:49:13-50
	android:mimeType
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:49:19-48
activity#nepali.patro.MainActivity
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:11:9-32:20
	android:launchMode
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:14:13-43
	android:hardwareAccelerated
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:18:13-47
	android:windowSoftInputMode
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:19:13-55
	android:exported
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:13:13-36
	android:configChanges
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:17:13-163
	android:theme
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:16:13-47
	android:taskAffinity
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:15:13-36
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:12:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:24:13-27:17
	android:resource
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:26:15-52
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:25:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:29:17-68
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:30:17-76
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:30:27-74
meta-data#flutterEmbedding
ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:35:9-37:33
	android:value
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:37:13-30
	android:name
		ADDED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml:36:13-44
uses-sdk
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
MERGED from [:audioplayers_android] D:\neppalipatro\nepali_calendar_app\build\audioplayers_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] D:\neppalipatro\nepali_calendar_app\build\audioplayers_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\neppalipatro\nepali_calendar_app\build\connectivity_plus\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\neppalipatro\nepali_calendar_app\build\connectivity_plus\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\neppalipatro\nepali_calendar_app\build\path_provider_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\neppalipatro\nepali_calendar_app\build\path_provider_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\neppalipatro\nepali_calendar_app\build\shared_preferences_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\neppalipatro\nepali_calendar_app\build\shared_preferences_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] D:\neppalipatro\nepali_calendar_app\build\webview_flutter_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] D:\neppalipatro\nepali_calendar_app\build\webview_flutter_android\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\4a79e0f8b7ae8de9a3de54a629784bb9\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\4a79e0f8b7ae8de9a3de54a629784bb9\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\af1cc6ca09010484bc2099e83a79df50\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\af1cc6ca09010484bc2099e83a79df50\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bc7f1e0373ec4de2ed1fe579301e3a4\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bc7f1e0373ec4de2ed1fe579301e3a4\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c279ff4160961cb6026b007ac2b3e26\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c279ff4160961cb6026b007ac2b3e26\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bf598e9b72a7f1f47b48d311421bc0c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7bf598e9b72a7f1f47b48d311421bc0c\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ddb6f116af13634073021d08dc96cc2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ddb6f116af13634073021d08dc96cc2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbc81ee2fd81c3e6c717d7a88cae23e\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cbc81ee2fd81c3e6c717d7a88cae23e\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4938b093760eae79c2bd1c6c78efcc52\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4938b093760eae79c2bd1c6c78efcc52\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\564ecb7857f3deff7a641bbaa962df7c\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\564ecb7857f3deff7a641bbaa962df7c\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\178c1a17b2fbb1b765e41975f6aebbfc\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\178c1a17b2fbb1b765e41975f6aebbfc\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\d4a788eee2196536a88a80538393531e\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\d4a788eee2196536a88a80538393531e\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\752b369dd190003a98d4ddcc422e2d5c\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\752b369dd190003a98d4ddcc422e2d5c\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f19bec46893ac087c6aa9fab40ce0c2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f19bec46893ac087c6aa9fab40ce0c2\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9efad71779c4c331ff66e1d5ce9bd8ed\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9efad71779c4c331ff66e1d5ce9bd8ed\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\95d8211e4d7ae8ae37bdf0168b220632\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\95d8211e4d7ae8ae37bdf0168b220632\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c96fba37ff992e143937e715db082cd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c96fba37ff992e143937e715db082cd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8e4fb06ef5ee1a663193f7926a74dc7\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8e4fb06ef5ee1a663193f7926a74dc7\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb44ebb6e4a36599d85e5180f77f3c01\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb44ebb6e4a36599d85e5180f77f3c01\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c56f68c43d5095d0880f3540dcb17f5\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4c56f68c43d5095d0880f3540dcb17f5\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0676bfc61694b9d84ae1e85a63497530\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0676bfc61694b9d84ae1e85a63497530\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\73c061a7cecdcc96d85e601cba8ffb56\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\73c061a7cecdcc96d85e601cba8ffb56\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e153f3c6651ec93485c56b387c0bbf5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e153f3c6651ec93485c56b387c0bbf5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3f3f142b0b9652438a946cc6c1c8964\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e3f3f142b0b9652438a946cc6c1c8964\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c73d321937e86235459c6d34df2c43b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0c73d321937e86235459c6d34df2c43b\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\5ea4b483c2be9990062376c427efdcf3\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\5ea4b483c2be9990062376c427efdcf3\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1dc5dd79a74fc81c110cb10ead2c25a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1dc5dd79a74fc81c110cb10ead2c25a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dcfc0bed31e34f3f2a25c92480684a5\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7dcfc0bed31e34f3f2a25c92480684a5\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaa3d856e9fd245d1f77540c21104bf0\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aaa3d856e9fd245d1f77540c21104bf0\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df61ca1e8c7c58d166f9739d5768072\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6df61ca1e8c7c58d166f9739d5768072\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aef8b987a1c32239d0034a44f473bd3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aef8b987a1c32239d0034a44f473bd3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\933c0692a1a090a0c6a9969b48f401a1\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\933c0692a1a090a0c6a9969b48f401a1\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0905ac793aaaadeea0d4cd0bc66f548\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\b0905ac793aaaadeea0d4cd0bc66f548\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\126252d498372bb69b628dddffe3b91b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\126252d498372bb69b628dddffe3b91b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\45584b3a56faa15c19959cde768172fe\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\45584b3a56faa15c19959cde768172fe\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e35eec6184d090b3ec837bc01b08222d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e35eec6184d090b3ec837bc01b08222d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c499e93a3a3a559cddd64463647db9b5\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c499e93a3a3a559cddd64463647db9b5\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\123e0a77d137e82ceb2021d6816ac756\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\123e0a77d137e82ceb2021d6816ac756\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\561cd99562411b713da844b479f2e87f\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\561cd99562411b713da844b479f2e87f\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\5db6f5c66312167b0dac9ce32231d981\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\5db6f5c66312167b0dac9ce32231d981\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\06c395eff6f61c3052beb29bd1942085\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\06c395eff6f61c3052beb29bd1942085\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\11766febb41e2e4ca69c2fa222e91fec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\11766febb41e2e4ca69c2fa222e91fec\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c3846f62ed5b4b67d8fbb2e5490875\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\64c3846f62ed5b4b67d8fbb2e5490875\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\efb00e0b9cdae5f620f328e2ab6e0de5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\efb00e0b9cdae5f620f328e2ab6e0de5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0aa21a3298e0d5de09777c2d3c3d0f5c\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0aa21a3298e0d5de09777c2d3c3d0f5c\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\287192a1ae46c2bad3673fd6eb49053d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\287192a1ae46c2bad3673fd6eb49053d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d07d6ad26fa7285dc55a4f27d24c380d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d07d6ad26fa7285dc55a4f27d24c380d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9a78c058b737f288941da7521080151\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9a78c058b737f288941da7521080151\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8bc41973cd832e52da15338849e7516\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\f8bc41973cd832e52da15338849e7516\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\3fa489be5becf2a0c7c8820a9e05f602\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\3fa489be5becf2a0c7c8820a9e05f602\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\neppalipatro\nepali_calendar_app\android\app\src\main\AndroidManifest.xml
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\neppalipatro\nepali_calendar_app\build\url_launcher_android\intermediates\merged_manifest\release\AndroidManifest.xml:9:13-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1eff404e73f21fc114191564e634b244\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6ee6c0ec69f62135bc7dee280ba2b7b2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\d4c6f8d0c838198130de62a72e7bcca6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#nepali.patro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#nepali.patro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4153a57180325c31ef7d2fb8f61948f9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6428431ea980094cc2109721b7440f3\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
