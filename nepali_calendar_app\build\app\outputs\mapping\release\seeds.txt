io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
io.flutter.view.TextureRegistry$GLTextureConsumer
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior
io.flutter.plugin.text.ProcessTextPlugin
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface
io.flutter.view.TextureRegistry$ImageTextureEntry
androidx.recyclerview.widget.RecyclerView
androidx.appcompat.widget.ActivityChooserView$InnerLayout
io.flutter.embedding.engine.FlutterOverlaySurface
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config
org.chromium.support_lib_boundary.WebMessageBoundaryInterface
androidx.recyclerview.widget.LinearLayoutManager
io.flutter.plugins.GeneratedPluginRegistrant
androidx.preference.PreferenceCategory
androidx.versionedparcelable.ParcelImpl
io.flutter.plugin.platform.SingleViewPresentation
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus
org.chromium.support_lib_boundary.WebViewPageBoundaryInterface
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface
io.flutter.embedding.engine.FlutterJNI
xyz.luan.audioplayers.AudioplayersPlugin
androidx.appcompat.widget.SwitchCompat
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface
androidx.preference.SeekBarPreference
android.support.v4.app.RemoteActionCompatParcelizer
androidx.window.extensions.core.util.function.Predicate
androidx.appcompat.widget.AlertDialogLayout
androidx.appcompat.view.menu.ActionMenuItemView
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface
androidx.preference.EditTextPreference
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
io.flutter.view.TextureRegistry$ImageConsumer
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface
androidx.preference.DropDownPreference
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface
androidx.appcompat.widget.SearchView
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface
androidx.annotation.Keep
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus
androidx.core.app.CoreComponentFactory
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface
androidx.window.extensions.core.util.function.Consumer
org.chromium.support_lib_boundary.WebViewStartUpConfigBoundaryInterface
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface
androidx.preference.PreferenceScreen
org.chromium.support_lib_boundary.WebStorageBoundaryInterface
io.flutter.plugins.pathprovider.PathProviderPlugin
androidx.preference.SwitchPreferenceCompat
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface
androidx.appcompat.widget.ActionMenuView
androidx.appcompat.widget.ButtonBarLayout
androidx.recyclerview.widget.GridLayoutManager
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface
org.chromium.support_lib_boundary.WebViewStartUpCallbackBoundaryInterface
androidx.appcompat.view.menu.ExpandedMenuView
androidx.lifecycle.ProcessLifecycleInitializer
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface
nepali.patro.MainActivity
androidx.preference.Preference
androidx.profileinstaller.ProfileInstallReceiver
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface
io.flutter.plugins.urllauncher.UrlLauncherPlugin
androidx.appcompat.widget.ActionBarContainer
androidx.appcompat.widget.ViewStubCompat
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.core.widget.NestedScrollView
kotlin.coroutines.jvm.internal.BaseContinuationImpl
androidx.window.extensions.core.util.function.Function
io.flutter.plugins.urllauncher.WebViewActivity
androidx.startup.InitializationProvider
androidx.appcompat.app.AlertController$RecycleListView
androidx.core.graphics.drawable.IconCompatParcelizer
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface
androidx.preference.internal.PreferenceImageView
androidx.appcompat.widget.DialogTitle
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.preference.UnPressableLinearLayout
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior
androidx.profileinstaller.ProfileInstallerInitializer
androidx.preference.ListPreference
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface
io.flutter.view.AccessibilityViewEmbedder
androidx.webkit.WebViewClientCompat
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface
androidx.core.graphics.drawable.IconCompat
io.flutter.view.TextureRegistry$SurfaceProducer
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Baseline
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
androidx.browser.browseractions.BrowserActionsFallbackMenuView
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$ConfigField
androidx.preference.SwitchPreference
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
androidx.core.app.RemoteActionCompat
org.chromium.support_lib_boundary.StaticsBoundaryInterface
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin
androidx.preference.TwoStatePreference
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants
androidx.versionedparcelable.CustomVersionedParcelable
androidx.preference.PreferenceGroup
io.flutter.view.FlutterCallbackInformation
androidx.preference.CheckBoxPreference
androidx.appcompat.view.menu.ListMenuItemView
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
androidx.appcompat.widget.ActionBarContextView
androidx.appcompat.widget.FitWindowsLinearLayout
org.chromium.support_lib_boundary.ProfileBoundaryInterface
androidx.preference.MultiSelectListPreference
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.appcompat.widget.ContentFrameLayout
androidx.appcompat.widget.Toolbar
kotlinx.coroutines.internal.StackTraceRecoveryKt
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface
androidx.core.app.RemoteActionCompatParcelizer
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.preference.DialogPreference
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int DISABLED
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
io.flutter.plugin.platform.SingleViewPresentation: int viewId
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: int baseline
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int DISABLED
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int FORCE_DARK_ONLY
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$ConfigField: int JAVASCRIPT_INTERFACE
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_BASE_PATH
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int DISABLED
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String MULTI_COOKIE_VALUE_SEPARATOR
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int WEB_SOURCE_AND_WEB_TRIGGER
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED_WITHOUT_APP_IDENTITY
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$ConfigField: int BASELINE
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
kotlinx.coroutines.DefaultExecutor: int debugStatus
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String MULTI_COOKIE_HEADER_NAME
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
kotlinx.coroutines.JobSupport: java.lang.Object _state
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
kotlinx.coroutines.channels.BufferedChannel: long receivers
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int BROWSER
io.flutter.embedding.engine.FlutterJNI: float displayHeight
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface: int NETWORK
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_WEB_TRIGGER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_SUFFIX
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_APP_TRIGGER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Baseline: int DEFAULT
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
kotlinx.coroutines.InvokeOnCancelling: int _invoked
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
kotlinx.coroutines.CancelledContinuation: int _resumed
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
kotlinx.coroutines.DispatchedCoroutine: int _decision
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_ARRAY_BUFFER
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface: int DUPLICATE
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int PREFER_MEDIA_QUERY_OVER_FORCE_DARK
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CACHE_DIRECTORY_BASE_PATH
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
io.flutter.embedding.engine.FlutterJNI: float displayDensity
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int APP
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
kotlinx.coroutines.CompletedExceptionally: int _handled
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int PRERENDER_ENABLED
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface: int GENERIC
io.flutter.embedding.engine.FlutterJNI: float displayWidth
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int NONE
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CONFIGURE_PARTITIONED_COOKIES
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
io.flutter.embedding.engine.FlutterOverlaySurface: int id
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int MEDIA_QUERY_ONLY
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_STRING
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSettings(android.webkit.WebSettings)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface: java.util.Map getAdditionalHeaders()
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: int getType()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getBackForwardCacheEnabled()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void showInterstitial(boolean)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebauthnSupport()
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onPageDOMContentLoadedEventFired(java.lang.reflect.InvocationHandler)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface: java.lang.String[] getSupportedFeatures()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getIncludeCookiesOnIntercept()
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getProfile()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebauthnSupport(int)
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewBuilder()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSpeculativeLoadingStatus(int)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebChromeClient(android.webkit.WebChromeClient)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void insertVisualStateCallback(long,java.lang.reflect.InvocationHandler)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.ParcelFileDescriptor openFile(android.content.ContentProvider,android.net.Uri)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setDefaultTrafficStatsTag(int)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler[] getPorts()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void saveState(android.os.Bundle,int,boolean)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean didCommitErrorPage()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertSafeBrowsingResponse(java.lang.reflect.InvocationHandler)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface: boolean terminate()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void setClearCachedDataIntervalMs(int)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void addWebMessageListener(java.lang.String,java.lang.String[],java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler,android.os.Handler)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.recyclerview.widget.RecyclerView: int getBaseline()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onPageCommitVisible(android.webkit.WebView,java.lang.String)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean didCommit()
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: boolean isMultiProcessEnabled()
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onPageLoadEventFired(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
io.flutter.plugins.webviewflutter.MixedContentMode: io.flutter.plugins.webviewflutter.MixedContentMode[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: void addJavascriptInterface(java.lang.Object,java.lang.String,java.util.List)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowFileAccess()
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.appcompat.widget.SearchView: int getPreferredWidth()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebViewClient getWebViewClient()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWillSuppressErrorPage(boolean)
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface: boolean isJavaScriptEnabled()
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
kotlin.random.Random: Random()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDark(int)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProfileStore()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: byte[] getAsArrayBuffer()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.GeolocationPermissions getGeoLocationPermissions()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProxyController()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebChromeClient getWebChromeClient()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setProfile(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
org.chromium.support_lib_boundary.WebViewStartUpCallbackBoundaryInterface: void onSuccess(java.lang.reflect.InvocationHandler)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
io.flutter.plugins.webviewflutter.OverScrollMode: io.flutter.plugins.webviewflutter.OverScrollMode valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler addDocumentStartJavaScript(java.lang.String,java.lang.String[])
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void setSpeculativeLoadingConfig(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onSafeBrowsingHit(android.webkit.WebView,android.webkit.WebResourceRequest,int,java.lang.reflect.InvocationHandler)
io.flutter.plugins.webviewflutter.SslErrorType: io.flutter.plugins.webviewflutter.SslErrorType valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertCookieManager(java.lang.Object)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: java.lang.String getUrl()
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebResourceError(java.lang.reflect.InvocationHandler)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessage(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
io.flutter.plugins.webviewflutter.MixedContentMode: io.flutter.plugins.webviewflutter.MixedContentMode valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeShouldDisableAHB()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
xyz.luan.audioplayers.ReleaseMode: xyz.luan.audioplayers.ReleaseMode[] values()
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getProfile(java.lang.String)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void onDragEnd(boolean)
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getDropDataProvider()
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: java.util.List getIgnoredQueryParameters()
org.chromium.support_lib_boundary.WebStorageBoundaryInterface: void deleteBrowsingData(java.util.concurrent.Executor,java.lang.Runnable)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: boolean shouldOverrideUrlLoading(android.webkit.WebView,android.webkit.WebResourceRequest)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRendererClient()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.String getData()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
io.flutter.embedding.engine.FlutterJNI: boolean ShouldDisableAHB()
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.appcompat.widget.SearchView: void setIconified(boolean)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: int getCacheMode()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void clearPrefetch(java.lang.String,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: void accept(java.lang.Object)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: java.lang.String getAsString()
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.view.View getView()
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebkitToCompatConverter()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface: void onComplete(long)
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel[] values()
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean isAlgorithmicDarkeningAllowed()
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onPageDeleted(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.ServiceWorkerController getServiceWorkerController()
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
androidx.lifecycle.ReportFragment: ReportFragment()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.util.List getAllProfileNames()
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
xyz.luan.audioplayers.PlayerMode: xyz.luan.audioplayers.PlayerMode[] values()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSafeBrowsingResponse(java.lang.Object)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setCacheMode(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getSpeculativeLoadingStatus()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: java.lang.String getName()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onNavigationRedirected(java.lang.reflect.InvocationHandler)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedHttpError(android.webkit.WebView,android.webkit.WebResourceRequest,android.webkit.WebResourceResponse)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setEnterpriseAuthenticationAppLinkPolicyEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface: java.lang.reflect.InvocationHandler getNoVarySearchData()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void prerenderUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,android.webkit.ValueCallback,android.webkit.ValueCallback)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isForward()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowContentAccess()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDarkBehavior()
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewNavigationClient(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
io.flutter.plugins.webviewflutter.OverScrollMode: io.flutter.plugins.webviewflutter.OverScrollMode[] values()
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDark()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertServiceWorkerSettings(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void clearProxyOverride(java.lang.Runnable,java.util.concurrent.Executor)
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceError(java.lang.Object)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: int getStatusCode()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.WebStorageBoundaryInterface: java.lang.String deleteBrowsingDataForSite(java.lang.String,java.util.concurrent.Executor,java.lang.Runnable)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.appcompat.widget.Toolbar: void setLogo(int)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface: void onFailure(int,java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererUnresponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setAudioMuted(boolean)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.CookieManager getCookieManager()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.webkit.WebChromeClient getWebChromeClient()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getStatics()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSafeBrowsingEnabled(boolean)
xyz.luan.audioplayers.PlayerMode: xyz.luan.audioplayers.PlayerMode valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface: java.lang.Long getMaxTimePerTaskInUiThreadMillis()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerWebSettings()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void removeWebMessageListener(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isBack()
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setIncludeCookiesOnIntercept(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setHasEnrolledInstrumentEnabled(boolean)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getSafeBrowsingEnabled()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: java.util.List getConsideredQueryParameters()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setBlockNetworkLoads(boolean)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: boolean getIgnoreDifferencesInParameters()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
xyz.luan.audioplayers.ReleaseMode: xyz.luan.audioplayers.ReleaseMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebViewMediaIntegrityApiDefaultStatus()
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin: WebViewFlutterPlugin()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface: void onPostMessage(android.webkit.WebView,java.lang.reflect.InvocationHandler,android.net.Uri,boolean,java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface: java.lang.Long getTotalTimeInUiThreadMillis()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.appcompat.widget.Toolbar: void setTitle(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setOffscreenPreRaster(boolean)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getUserAgentMetadataMap()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void backToSafety(boolean)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$900(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebStorage(java.lang.Object)
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: void start(int,java.util.Collection,int)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getEnterpriseAuthenticationAppLinkPolicyEnabled()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler[] createWebMessageChannel()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setPaymentRequestEnabled(boolean)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setDefaultTrafficStatsUid(int)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getWillSuppressErrorPage()
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.WebStorage getWebStorage()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: boolean onCreate()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.net.Uri cache(byte[],java.lang.String,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererResponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getIncludeCookiesOnIntercept()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceRequest(android.webkit.WebResourceRequest)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isRestore()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowFileAccess(boolean)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String[] getStreamTypes(android.net.Uri,java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void postMessage(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String[] getSupportedFeatures()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean wasInitiatedByPage()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAttributionBehavior(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
org.chromium.support_lib_boundary.WebViewStartUpConfigBoundaryInterface: java.util.concurrent.Executor getBackgroundExecutor()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isSameDocument()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: boolean isAudioMuted()
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler createWebView(android.webkit.WebView)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
io.flutter.plugins.webviewflutter.SslErrorType: io.flutter.plugins.webviewflutter.SslErrorType[] values()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface: void onMessage(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: void startUpWebView(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface: android.webkit.WebResourceResponse shouldInterceptRequest(android.webkit.WebResourceRequest)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRenderer()
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setUserAgentMetadataFromMap(java.util.Map)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin: ConnectivityPlugin()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
nepali.patro.MainActivity: MainActivity()
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isHistory()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: java.lang.reflect.InvocationHandler getPage()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getBlockNetworkLoads()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface: void remove()
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerController()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getHasEnrolledInstrumentEnabled()
androidx.appcompat.widget.SearchView: int getInputType()
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface: int getPrefetchTTLSeconds()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onNavigationStarted(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getPaymentRequestEnabled()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getTracingController()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getOrCreateProfile(java.lang.String)
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedError(android.webkit.WebView,android.webkit.WebResourceRequest,java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface: java.lang.Object getOrCreatePeer(java.util.concurrent.Callable)
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface: boolean isRedirect()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAlgorithmicDarkeningAllowed(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String getType(android.net.Uri)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: boolean deleteProfile(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertServiceWorkerSettings(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onNavigationCompleted(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: java.lang.CharSequence getDescription()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setIncludeCookiesOnIntercept(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebMessagePort(java.lang.reflect.InvocationHandler)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface: java.util.List getCookieInfo(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowContentAccess(boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void initSafeBrowsing(android.content.Context,android.webkit.ValueCallback)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface: int getMaxPrefetches()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebViewMediaIntegrityApiStatus(int,java.util.Map)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: android.net.Uri getSafeBrowsingPrivacyPolicyUrl()
androidx.startup.InitializationProvider: InitializationProvider()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode[] values()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewNavigationClient()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean isTracing()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: int getErrorCode()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.webkit.WebViewClientCompat: WebViewClientCompat()
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
xyz.luan.audioplayers.AudioplayersPlugin: AudioplayersPlugin()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getAttributionBehavior()
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebMessagePort(java.lang.Object)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler getMessagePayload()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void proceed(boolean)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onFirstContentfulPaint(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
kotlin.collections.AbstractList: AbstractList()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingWhitelist(java.util.List,android.webkit.ValueCallback)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getOffscreenPreRaster()
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: java.lang.String getVariationsHeader()
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: WebViewBuilderBoundaryInterface$Config()
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: void setServiceWorkerClient(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: boolean getVaryOnKeyOrder()
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor,boolean)
org.chromium.support_lib_boundary.WebViewStartUpConfigBoundaryInterface: boolean shouldRunUiThreadStartUpTasks()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setDisabledActionModeMenuItems(int)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingAllowlist(java.util.Set,android.webkit.ValueCallback)
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface: java.util.List getBlockingStartUpLocations()
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
io.flutter.plugins.webviewflutter.JavaScriptChannel: void postMessage(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface: void onSuccess()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void prerenderUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler,android.webkit.ValueCallback,android.webkit.ValueCallback)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebViewClient(android.webkit.WebViewClient)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler)
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface: android.webkit.WebView build(android.content.Context,java.util.function.Consumer)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isReload()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessageWithPayload(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean stop(java.io.OutputStream,java.util.concurrent.Executor)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: void accept(java.util.function.BiConsumer)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: void setInputType(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.Bundle call(java.lang.String,java.lang.String,android.os.Bundle)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewRendererClient(java.lang.reflect.InvocationHandler)
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDarkBehavior(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getWebViewMediaIntegrityApiOverrideRules()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.database.Cursor query(android.net.Uri,java.lang.String[],java.lang.String,java.lang.String[],java.lang.String)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void close()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getDisabledActionModeMenuItems()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setBackForwardCacheEnabled(boolean)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void postMessageToMainFrame(java.lang.reflect.InvocationHandler,android.net.Uri)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface: int getMaxPrerenders()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
