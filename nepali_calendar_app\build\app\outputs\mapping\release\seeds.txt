androidx.startup.InitializationProvider
androidx.webkit.WebViewClientCompat
androidx.preference.Preference
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.appcompat.widget.ActionBarContextView
org.chromium.support_lib_boundary.WebViewStartUpCallbackBoundaryInterface
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.profileinstaller.ProfileInstallerInitializer
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.appcompat.widget.AlertDialogLayout
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants$ProcessGlobalConfigMapKey
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface
androidx.appcompat.app.AlertController$RecycleListView
androidx.preference.internal.PreferenceImageView
androidx.appcompat.widget.ActionBarContainer
org.chromium.support_lib_boundary.WebViewStartUpConfigBoundaryInterface
androidx.appcompat.widget.ContentFrameLayout
androidx.preference.ListPreference
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface
androidx.browser.browseractions.BrowserActionsFallbackMenuView
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface
io.flutter.embedding.engine.FlutterJNI
androidx.window.extensions.core.util.function.Function
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
org.chromium.support_lib_boundary.WebMessageBoundaryInterface
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus
androidx.preference.DialogPreference
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.profileinstaller.ProfileInstallReceiver
androidx.preference.UnPressableLinearLayout
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
kotlin.coroutines.jvm.internal.BaseContinuationImpl
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface
androidx.versionedparcelable.ParcelImpl
androidx.appcompat.widget.ButtonBarLayout
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface
androidx.appcompat.view.menu.ExpandedMenuView
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface
kotlinx.coroutines.internal.StackTraceRecoveryKt
io.flutter.plugin.text.ProcessTextPlugin
io.flutter.view.TextureRegistry$ImageTextureEntry
androidx.appcompat.widget.SwitchCompat
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface
androidx.appcompat.view.menu.ListMenuItemView
androidx.preference.PreferenceGroup
androidx.appcompat.widget.ActionBarOverlayLayout
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface
androidx.preference.EditTextPreference
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface
kotlinx.coroutines.android.AndroidDispatcherFactory
androidx.appcompat.widget.DialogTitle
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.appcompat.widget.SearchView
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface
io.flutter.view.FlutterCallbackInformation
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface
androidx.appcompat.widget.Toolbar
io.flutter.view.TextureRegistry$SurfaceTextureEntry
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface
androidx.preference.SwitchPreferenceCompat
androidx.appcompat.widget.ViewStubCompat
androidx.core.graphics.drawable.IconCompatParcelizer
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface
androidx.core.graphics.drawable.IconCompat
io.flutter.view.AccessibilityViewEmbedder
androidx.recyclerview.widget.RecyclerView
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$ConfigField
androidx.annotation.Keep
org.chromium.support_lib_boundary.ProfileBoundaryInterface
androidx.preference.TwoStatePreference
io.flutter.plugins.urllauncher.WebViewActivity
org.chromium.support_lib_boundary.WebStorageBoundaryInterface
androidx.preference.SeekBarPreference
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Baseline
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface
androidx.appcompat.widget.FitWindowsFrameLayout
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config
android.support.v4.app.RemoteActionCompatParcelizer
io.flutter.plugins.urllauncher.UrlLauncherPlugin
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface
androidx.preference.PreferenceCategory
io.flutter.plugin.platform.SingleViewPresentation
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants
androidx.preference.SwitchPreference
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface
nepali.patro.MainActivity
androidx.core.app.RemoteActionCompat
org.chromium.support_lib_boundary.StaticsBoundaryInterface
androidx.core.widget.NestedScrollView
xyz.luan.audioplayers.AudioplayersPlugin
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
io.flutter.plugins.pathprovider.PathProviderPlugin
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus
io.flutter.embedding.engine.FlutterOverlaySurface
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface
androidx.versionedparcelable.CustomVersionedParcelable
androidx.window.extensions.core.util.function.Predicate
io.flutter.view.TextureRegistry$ImageConsumer
androidx.preference.MultiSelectListPreference
org.chromium.support_lib_boundary.WebViewPageBoundaryInterface
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface
androidx.core.app.CoreComponentFactory
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior
androidx.recyclerview.widget.LinearLayoutManager
androidx.recyclerview.widget.GridLayoutManager
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.appcompat.widget.ActionMenuView
androidx.lifecycle.ProcessLifecycleInitializer
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface
androidx.core.app.RemoteActionCompatParcelizer
androidx.appcompat.view.menu.ActionMenuItemView
androidx.preference.DropDownPreference
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface
io.flutter.view.TextureRegistry$GLTextureConsumer
androidx.preference.CheckBoxPreference
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface
androidx.appcompat.widget.FitWindowsLinearLayout
io.flutter.view.TextureRegistry$SurfaceProducer
io.flutter.plugins.GeneratedPluginRegistrant
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
androidx.window.extensions.core.util.function.Consumer
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface
androidx.preference.PreferenceScreen
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
kotlinx.coroutines.InvokeOnCancelling: int _invoked
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int FORCE_DARK_ONLY
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED_WITHOUT_APP_IDENTITY
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int DISABLED
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int APP
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int WEB_SOURCE_AND_WEB_TRIGGER
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: int baseline
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_WEB_TRIGGER
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
io.flutter.embedding.engine.FlutterJNI: float displayDensity
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
kotlinx.coroutines.DefaultExecutor: int debugStatus
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int NONE
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$ConfigField: int JAVASCRIPT_INTERFACE
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int ENABLED
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CONFIGURE_PARTITIONED_COOKIES
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_ARRAY_BUFFER
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface$WebMessagePayloadType: int TYPE_STRING
kotlinx.coroutines.CancelledContinuation: int _resumed
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$AttributionBehavior: int APP_SOURCE_AND_APP_TRIGGER
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
kotlinx.coroutines.DispatchedCoroutine: int _decision
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
kotlinx.coroutines.CompletedExceptionally: int _handled
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_SUFFIX
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
io.flutter.embedding.engine.FlutterJNI: float displayHeight
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebViewMediaIntegrityApiStatus: int DISABLED
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface: int GENERIC
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String MULTI_COOKIE_VALUE_SEPARATOR
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String DATA_DIRECTORY_BASE_PATH
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int PRERENDER_ENABLED
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int MEDIA_QUERY_ONLY
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$WebauthnSupport: int BROWSER
kotlinx.coroutines.JobSupport: java.lang.Object _state
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$ConfigField: int BASELINE
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String MULTI_COOKIE_HEADER_NAME
io.flutter.embedding.engine.FlutterOverlaySurface: int id
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: float displayWidth
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface: int DUPLICATE
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface$PrefetchExceptionTypeBoundaryInterface: int NETWORK
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
kotlinx.coroutines.channels.BufferedChannel: long receivers
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
io.flutter.plugin.platform.SingleViewPresentation: int viewId
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
org.chromium.support_lib_boundary.ProcessGlobalConfigConstants: java.lang.String CACHE_DIRECTORY_BASE_PATH
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$ForceDarkBehavior: int PREFER_MEDIA_QUERY_OVER_FORCE_DARK
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Baseline: int DEFAULT
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface$SpeculativeLoadingStatus: int DISABLED
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String[] getStreamTypes(android.net.Uri,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onPageDeleted(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setHasEnrolledInstrumentEnabled(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebChromeClient getWebChromeClient()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: java.util.List getIgnoredQueryParameters()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSafeBrowsingResponse(java.lang.Object)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getProfile(java.lang.String)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebViewClient(android.webkit.WebViewClient)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getEnterpriseAuthenticationAppLinkPolicyEnabled()
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface: boolean isJavaScriptEnabled()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererResponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onPageLoadEventFired(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getPaymentRequestEnabled()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface: int getMaxPrefetches()
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getSafeBrowsingEnabled()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProxyController()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getUserAgentMetadataMap()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebViewMediaIntegrityApiStatus(int,java.util.Map)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
org.chromium.support_lib_boundary.ScriptHandlerBoundaryInterface: void remove()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: java.lang.String getType(android.net.Uri)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setUserAgentMetadataFromMap(java.util.Map)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessageWithPayload(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: java.lang.CharSequence getDescription()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel[] values()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getAttributionBehavior()
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
org.chromium.support_lib_boundary.WebMessageListenerBoundaryInterface: void onPostMessage(android.webkit.WebView,java.lang.reflect.InvocationHandler,android.net.Uri,boolean,java.lang.reflect.InvocationHandler)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: java.util.List getConsideredQueryParameters()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.view.View getView()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
io.flutter.plugins.webviewflutter.WebViewFlutterPlugin: WebViewFlutterPlugin()
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getBackForwardCacheEnabled()
org.chromium.support_lib_boundary.WebViewStartUpConfigBoundaryInterface: boolean shouldRunUiThreadStartUpTasks()
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertSafeBrowsingResponse(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.appcompat.widget.SearchView: void setIconified(boolean)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: byte[] getAsArrayBuffer()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingAllowlist(java.util.Set,android.webkit.ValueCallback)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface: void onFailure(int,java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: java.lang.reflect.InvocationHandler getPage()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewNavigationClient(java.lang.reflect.InvocationHandler)
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getOffscreenPreRaster()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getIncludeCookiesOnIntercept()
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.CookieManager getCookieManager()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void clearPrefetch(java.lang.String,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.String[] getSupportedFeatures()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setAudioMuted(boolean)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: void accept(java.lang.Object)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getDisabledActionModeMenuItems()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
org.chromium.support_lib_boundary.JsReplyProxyBoundaryInterface: void postMessage(java.lang.String)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: boolean isMultiProcessEnabled()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: void setServiceWorkerClient(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean wasInitiatedByPage()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
org.chromium.support_lib_boundary.WebViewRendererBoundaryInterface: boolean terminate()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isHistory()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setDefaultTrafficStatsTag(int)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
kotlin.random.Random: Random()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onSafeBrowsingHit(android.webkit.WebView,android.webkit.WebResourceRequest,int,java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: void addJavascriptInterface(java.lang.Object,java.lang.String,java.util.List)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setBackForwardCacheEnabled(boolean)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.net.Uri cache(byte[],java.lang.String,java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onPageDOMContentLoadedEventFired(java.lang.reflect.InvocationHandler)
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebauthnSupport()
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedError(android.webkit.WebView,android.webkit.WebResourceRequest,java.lang.reflect.InvocationHandler)
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface: java.lang.reflect.InvocationHandler getNoVarySearchData()
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void showInterstitial(boolean)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode[] values()
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
xyz.luan.audioplayers.AudioplayersPlugin: AudioplayersPlugin()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler createWebView(android.webkit.WebView)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean isAlgorithmicDarkeningAllowed()
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isSameDocument()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setIncludeCookiesOnIntercept(boolean)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowFileAccess(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertSettings(android.webkit.WebSettings)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getDropDataProvider()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onReceivedHttpError(android.webkit.WebView,android.webkit.WebResourceRequest,android.webkit.WebResourceResponse)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWebauthnSupport(int)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: android.webkit.WebChromeClient getWebChromeClient()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: java.lang.String getVariationsHeader()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: int getType()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: int getBaseline()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.lang.reflect.InvocationHandler getOrCreateProfile(java.lang.String)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getProfile()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isForward()
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getBlockNetworkLoads()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void prefetchUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
org.chromium.support_lib_boundary.WebViewCookieManagerBoundaryInterface: java.util.List getCookieInfo(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setEnterpriseAuthenticationAppLinkPolicyEnabled(boolean)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getTracingController()
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
io.flutter.plugins.webviewflutter.JavaScriptChannel: void postMessage(java.lang.String)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void onDragEnd(boolean)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: void onPageCommitVisible(android.webkit.WebView,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$900(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSafeBrowsingEnabled(boolean)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void saveState(android.os.Bundle,int,boolean)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
androidx.appcompat.widget.SearchView: int getImeOptions()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void addWebMessageListener(java.lang.String,java.lang.String[],java.lang.reflect.InvocationHandler)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
io.flutter.plugins.webviewflutter.OverScrollMode: io.flutter.plugins.webviewflutter.OverScrollMode valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
org.chromium.support_lib_boundary.WebViewRendererClientBoundaryInterface: void onRendererUnresponsive(android.webkit.WebView,java.lang.reflect.InvocationHandler)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebStorage(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
xyz.luan.audioplayers.PlayerMode: xyz.luan.audioplayers.PlayerMode[] values()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setIncludeCookiesOnIntercept(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: java.util.List getAllProfileNames()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void prerenderUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,java.lang.reflect.InvocationHandler,android.webkit.ValueCallback,android.webkit.ValueCallback)
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.WebViewStartUpCallbackBoundaryInterface: void onSuccess(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onNavigationCompleted(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setCacheMode(int)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: boolean isAudioMuted()
io.flutter.embedding.android.FlutterView: void setVisibility(int)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
org.chromium.support_lib_boundary.WebStorageBoundaryInterface: java.lang.String deleteBrowsingDataForSite(java.lang.String,java.util.concurrent.Executor,java.lang.Runnable)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isBack()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.embedding.engine.FlutterJNI: boolean nativeShouldDisableAHB()
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setWillSuppressErrorPage(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.GeolocationPermissions getGeoLocationPermissions()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setSpeculativeLoadingStatus(int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRendererClient()
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setPaymentRequestEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void backToSafety(boolean)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.ServiceWorkerController getServiceWorkerController()
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
io.flutter.plugins.webviewflutter.SslErrorType: io.flutter.plugins.webviewflutter.SslErrorType[] values()
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
org.chromium.support_lib_boundary.ServiceWorkerClientBoundaryInterface: android.webkit.WebResourceResponse shouldInterceptRequest(android.webkit.WebResourceRequest)
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getIncludeCookiesOnIntercept()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowFileAccess()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
io.flutter.plugins.webviewflutter.FileChooserMode: io.flutter.plugins.webviewflutter.FileChooserMode valueOf(java.lang.String)
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getProfileStore()
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface: android.webkit.WebView build(android.content.Context,java.util.function.Consumer)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
io.flutter.plugins.webviewflutter.ConsoleMessageLevel: io.flutter.plugins.webviewflutter.ConsoleMessageLevel valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
io.flutter.embedding.engine.FlutterJNI: boolean ShouldDisableAHB()
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: android.net.Uri getSafeBrowsingPrivacyPolicyUrl()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.webkit.WebViewClientCompat: WebViewClientCompat()
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: void start(int,java.util.Collection,int)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void setWebMessageCallback(java.lang.reflect.InvocationHandler,android.os.Handler)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: boolean onCreate()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
org.chromium.support_lib_boundary.WebMessagePayloadBoundaryInterface: java.lang.String getAsString()
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
org.chromium.support_lib_boundary.SpeculativeLoadingParametersBoundaryInterface: java.util.Map getAdditionalHeaders()
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void insertVisualStateCallback(long,java.lang.reflect.InvocationHandler)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
org.chromium.support_lib_boundary.FeatureFlagHolderBoundaryInterface: java.lang.String[] getSupportedFeatures()
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void prerenderUrl(java.lang.String,android.os.CancellationSignal,java.util.concurrent.Executor,android.webkit.ValueCallback,android.webkit.ValueCallback)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: void accept(java.util.function.BiConsumer)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setDisabledActionModeMenuItems(int)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: android.webkit.WebViewClient getWebViewClient()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface: int getPrefetchTTLSeconds()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler addDocumentStartJavaScript(java.lang.String,java.lang.String[])
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler[] createWebMessageChannel()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: int getCacheMode()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: void setClearCachedDataIntervalMs(int)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler getMessagePayload()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void clearProxyOverride(java.lang.Runnable,java.util.concurrent.Executor)
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebMessagePort(java.lang.Object)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.Bundle call(java.lang.String,java.lang.String,android.os.Bundle)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewRenderer()
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getWillSuppressErrorPage()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
org.chromium.support_lib_boundary.WebViewStartUpConfigBoundaryInterface: java.util.concurrent.Executor getBackgroundExecutor()
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
org.chromium.support_lib_boundary.SpeculativeLoadingConfigBoundaryInterface: int getMaxPrerenders()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
io.flutter.plugins.webviewflutter.WebViewProxyApi$WebViewPlatformView: void setWebChromeClient(android.webkit.WebChromeClient)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: boolean getIgnoreDifferencesInParameters()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertCookieManager(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void removeWebMessageListener(java.lang.String)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebkitToCompatConverter()
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: boolean getHasEnrolledInstrumentEnabled()
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface: java.lang.Long getTotalTimeInUiThreadMillis()
xyz.luan.audioplayers.ReleaseMode: xyz.luan.audioplayers.ReleaseMode valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
org.chromium.support_lib_boundary.IsomorphicObjectBoundaryInterface: java.lang.Object getOrCreatePeer(java.util.concurrent.Callable)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAttributionBehavior(int)
org.chromium.support_lib_boundary.NoVarySearchDataBoundaryInterface: boolean getVaryOnKeyOrder()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setOffscreenPreRaster(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
xyz.luan.audioplayers.ReleaseMode: xyz.luan.audioplayers.ReleaseMode[] values()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setDefaultTrafficStatsUid(int)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebResourceErrorBoundaryInterface: int getErrorCode()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: int getStatusCode()
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerController()
org.chromium.support_lib_boundary.VisualStateCallbackBoundaryInterface: void onComplete(long)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
io.flutter.plugins.webviewflutter.SslErrorType: io.flutter.plugins.webviewflutter.SslErrorType valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
org.chromium.support_lib_boundary.WebStorageBoundaryInterface: void deleteBrowsingData(java.util.concurrent.Executor,java.lang.Runnable)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: void setSpeculativeLoadingConfig(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isReload()
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
org.chromium.support_lib_boundary.ProfileStoreBoundaryInterface: boolean deleteProfile(java.lang.String)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
org.chromium.support_lib_boundary.WebViewClientBoundaryInterface: boolean shouldOverrideUrlLoading(android.webkit.WebView,android.webkit.WebResourceRequest)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
org.chromium.support_lib_boundary.ServiceWorkerControllerBoundaryInterface: java.lang.reflect.InvocationHandler getServiceWorkerWebSettings()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertServiceWorkerSettings(java.lang.Object)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setWebViewRendererClient(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceRequest(android.webkit.WebResourceRequest)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.reflect.InvocationHandler convertWebResourceError(java.lang.Object)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void postMessage(java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onNavigationRedirected(java.lang.reflect.InvocationHandler)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
org.chromium.support_lib_boundary.WebMessagePortBoundaryInterface: void close()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.appcompat.widget.SearchView: void setInputType(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
xyz.luan.audioplayers.PlayerMode: xyz.luan.audioplayers.PlayerMode valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void postMessageToMainFrame(java.lang.reflect.InvocationHandler,android.net.Uri)
org.chromium.support_lib_boundary.SafeBrowsingResponseBoundaryInterface: void proceed(boolean)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebResourceError(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean didCommitErrorPage()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.database.Cursor query(android.net.Uri,java.lang.String[],java.lang.String,java.lang.String[],java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertWebMessagePort(java.lang.reflect.InvocationHandler)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getSpeculativeLoadingStatus()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: java.lang.String getName()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.reflect.InvocationHandler[] getPorts()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean isTracing()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: void startUpWebView(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
org.chromium.support_lib_boundary.WebViewBuilderBoundaryInterface$Config: WebViewBuilderBoundaryInterface$Config()
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDark(int)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewNavigationClient()
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getWebViewMediaIntegrityApiDefaultStatus()
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getWebViewBuilder()
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
androidx.lifecycle.ReportFragment: ReportFragment()
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDark()
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.startup.InitializationProvider: InitializationProvider()
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
io.flutter.plugins.webviewflutter.OverScrollMode: io.flutter.plugins.webviewflutter.OverScrollMode[] values()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void initSafeBrowsing(android.content.Context,android.webkit.ValueCallback)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean didCommit()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
org.chromium.support_lib_boundary.WebkitToCompatConverterBoundaryInterface: java.lang.Object convertServiceWorkerSettings(java.lang.reflect.InvocationHandler)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
org.chromium.support_lib_boundary.WebResourceRequestBoundaryInterface: boolean isRedirect()
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setRequestedWithHeaderOriginAllowList(java.util.Set)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setAllowContentAccess(boolean)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
org.chromium.support_lib_boundary.DropDataContentProviderBoundaryInterface: android.os.ParcelFileDescriptor openFile(android.content.ContentProvider,android.net.Uri)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
org.chromium.support_lib_boundary.PrefetchOperationCallbackBoundaryInterface: void onSuccess()
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
nepali.patro.MainActivity: MainActivity()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
org.chromium.support_lib_boundary.WebMessageBoundaryInterface: java.lang.String getData()
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: void setBlockNetworkLoads(boolean)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
androidx.appcompat.widget.Toolbar: void setTitle(int)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: boolean isRestore()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
org.chromium.support_lib_boundary.TracingControllerBoundaryInterface: boolean stop(java.io.OutputStream,java.util.concurrent.Executor)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setAlgorithmicDarkeningAllowed(boolean)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: int getForceDarkBehavior()
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onNavigationStarted(java.lang.reflect.InvocationHandler)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
org.chromium.support_lib_boundary.WebMessageCallbackBoundaryInterface: void onMessage(java.lang.reflect.InvocationHandler,java.lang.reflect.InvocationHandler)
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
org.chromium.support_lib_boundary.ProfileBoundaryInterface: android.webkit.WebStorage getWebStorage()
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin: ConnectivityPlugin()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Map getWebViewMediaIntegrityApiOverrideRules()
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface: java.util.List getBlockingStartUpLocations()
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
androidx.appcompat.widget.SearchView: int getInputType()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
org.chromium.support_lib_boundary.WebViewProviderFactoryBoundaryInterface: java.lang.reflect.InvocationHandler getStatics()
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
org.chromium.support_lib_boundary.WebViewProviderBoundaryInterface: void setProfile(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
io.flutter.plugins.webviewflutter.MixedContentMode: io.flutter.plugins.webviewflutter.MixedContentMode[] values()
org.chromium.support_lib_boundary.WebViewStartUpResultBoundaryInterface: java.lang.Long getMaxTimePerTaskInUiThreadMillis()
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
org.chromium.support_lib_boundary.ServiceWorkerWebSettingsBoundaryInterface: boolean getAllowContentAccess()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
kotlin.collections.AbstractList: AbstractList()
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
org.chromium.support_lib_boundary.ProxyControllerBoundaryInterface: void setProxyOverride(java.lang.String[][],java.lang.String[],java.lang.Runnable,java.util.concurrent.Executor,boolean)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: java.util.Set getRequestedWithHeaderOriginAllowList()
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.plugins.webviewflutter.MixedContentMode: io.flutter.plugins.webviewflutter.MixedContentMode valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
org.chromium.support_lib_boundary.StaticsBoundaryInterface: void setSafeBrowsingWhitelist(java.util.List,android.webkit.ValueCallback)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
org.chromium.support_lib_boundary.WebViewNavigationClientBoundaryInterface: void onFirstContentfulPaint(java.lang.reflect.InvocationHandler)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
org.chromium.support_lib_boundary.WebViewNavigationBoundaryInterface: java.lang.String getUrl()
org.chromium.support_lib_boundary.WebSettingsBoundaryInterface: void setForceDarkBehavior(int)
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
