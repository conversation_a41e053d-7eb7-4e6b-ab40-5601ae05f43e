@echo off
echo ==========================================
echo RELEASE APK BUILD + STORAGE FIX
echo ==========================================

echo Step 1: Stopping all processes...
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1
taskkill /f /im flutter.exe >nul 2>&1
echo ✅ Processes stopped

echo Step 2: Cleaning Flutter...
flutter clean
echo ✅ Flutter cleaned

echo Step 3: COMPLETE storage cleanup...
if exist "%USERPROFILE%\.gradle" (
    echo Removing entire .gradle directory...
    rmdir /s /q "%USERPROFILE%\.gradle"
    echo ✅ Gradle cache removed
)

if exist "%TEMP%\gradle*" (
    rmdir /s /q "%TEMP%\gradle*" >nul 2>&1
)

if exist "%TEMP%\flutter*" (
    rmdir /s /q "%TEMP%\flutter*" >nul 2>&1
)

if exist "%TEMP%\dart*" (
    rmdir /s /q "%TEMP%\dart*" >nul 2>&1
)

echo ✅ Temp files cleaned

echo Step 4: Setting optimized environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set GRADLE_OPTS=-Xmx1G -XX:MaxMetaspaceSize=512M -Dfile.encoding=UTF-8
set FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
set GRADLE_USER_HOME=%USERPROFILE%\.gradle
set PUB_HOSTED_URL=https://pub.flutter-io.cn
set FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
echo ✅ Environment optimized

echo Step 5: Getting dependencies...
flutter pub get
echo ✅ Dependencies downloaded

echo Step 6: Building RELEASE APK (optimized)...
echo Using Chinese mirrors for faster download...
flutter build apk --release --target-platform android-arm64 --shrink
echo ✅ Release APK built

echo Step 7: Checking APK location...
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ SUCCESS! APK created at: build\app\outputs\flutter-apk\app-release.apk
    dir "build\app\outputs\flutter-apk\app-release.apk"
) else (
    echo ❌ APK not found, checking alternative locations...
    dir "build\app\outputs\flutter-apk\" /b
)

echo ==========================================
echo ✅ RELEASE BUILD COMPLETE!
echo ==========================================
pause
