@echo off
echo ==========================================
echo COMPLETE GRADLE CACHE FIX
echo ==========================================

echo Step 1: Stopping all processes...
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1
taskkill /f /im flutter.exe >nul 2>&1

echo Step 2: Cleaning Flutter...
flutter clean

echo Step 3: Removing ENTIRE Gradle cache...
if exist "%USERPROFILE%\.gradle" (
    echo Removing .gradle directory...
    rmdir /s /q "%USERPROFILE%\.gradle"
    echo ✅ Gradle cache completely removed
)

echo Step 4: Cleaning temp files...
if exist "%TEMP%\gradle*" (
    rmdir /s /q "%TEMP%\gradle*"
)
if exist "%TEMP%\flutter*" (
    rmdir /s /q "%TEMP%\flutter*"
)

echo Step 5: Setting environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set GRADLE_OPTS=-Xmx2G -XX:MaxMetaspaceSize=1G

echo Step 6: Getting dependencies...
flutter pub get

echo Step 7: Building APK (ARM64 only)...
flutter build apk --debug --target-platform android-arm64

echo ==========================================
echo ✅ COMPLETE FIX DONE!
echo ==========================================
pause
