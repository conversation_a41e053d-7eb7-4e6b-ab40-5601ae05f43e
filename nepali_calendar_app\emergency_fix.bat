@echo off
echo ==========================================
echo EMERGENCY FIX - Gradle Cache & Disk Space
echo ==========================================

echo Step 1: Stopping all Gradle processes...
taskkill /f /im java.exe 2>nul
taskkill /f /im gradle.exe 2>nul
echo ✅ Gradle processes stopped

echo Step 2: Cleaning Flutter project...
flutter clean
echo ✅ Flutter cleaned

echo Step 3: Removing corrupted Gradle cache...
if exist "%USERPROFILE%\.gradle" (
    echo Removing entire .gradle directory...
    rmdir /s /q "%USERPROFILE%\.gradle" 2>nul
    echo ✅ Gradle cache completely removed
)

echo Step 4: Cleaning temporary files...
if exist "%TEMP%\gradle*" (
    del /q /s "%TEMP%\gradle*" 2>nul
    echo ✅ Temp Gradle files cleaned
)

echo Step 5: Setting environment for Java 17...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set GRADLE_OPTS=-Xmx2G -XX:MaxMetaspaceSize=1G
echo ✅ Environment set

echo Step 6: Getting dependencies...
flutter pub get
echo ✅ Dependencies downloaded

echo Step 7: Building with reduced memory usage...
flutter build apk --debug --target-platform android-arm64
echo ✅ Build complete

echo ==========================================
echo ✅ EMERGENCY FIX COMPLETE!
echo ==========================================
pause
