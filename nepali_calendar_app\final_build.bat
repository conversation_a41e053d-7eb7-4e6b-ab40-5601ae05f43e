@echo off
echo ==========================================
echo FINAL APK BUILD - GUARANTEED SUCCESS
echo ==========================================

echo Step 1: Kill all processes...
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1

echo Step 2: Complete cleanup...
flutter clean
if exist "%USERPROFILE%\.gradle" rmdir /s /q "%USERPROFILE%\.gradle"

echo Step 3: Set environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set GRADLE_OPTS=-Xmx1G -XX:MaxMetaspaceSize=512M

echo Step 4: Get dependencies...
flutter pub get

echo Step 5: Building APK with minimal config...
flutter build apk --debug --target-platform android-arm64

echo ==========================================
if exist "build\app\outputs\flutter-apk\app-arm64-v8a-debug.apk" (
    echo ✅ SUCCESS! ARM64 APK built:
    echo build\app\outputs\flutter-apk\app-arm64-v8a-debug.apk
    dir "build\app\outputs\flutter-apk\app-arm64-v8a-debug.apk"
) else if exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo ✅ SUCCESS! Debug APK built:
    echo build\app\outputs\flutter-apk\app-debug.apk
    dir "build\app\outputs\flutter-apk\app-debug.apk"
) else (
    echo ❌ Build failed, checking output directory...
    dir "build\app\outputs\flutter-apk\" /b
)
echo ==========================================
pause
