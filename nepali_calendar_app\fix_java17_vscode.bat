@echo off
echo ========================================
echo VS Code Java 17 Fix Script
echo ========================================

echo Step 1: Checking Java Version...
java -version
echo.

echo Step 2: Setting JAVA_HOME to Java 17...
set JAVA_HOME=C:\Program Files\Java\jdk-17
echo JAVA_HOME set to: %JAVA_HOME%
echo.

echo Step 3: Cleaning Gradle Cache...
if exist "%USERPROFILE%\.gradle\caches" (
    echo Removing Gradle cache directory...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
    echo Gradle cache cleaned.
) else (
    echo Gradle cache directory not found.
)
echo.

echo Step 4: Cleaning Flutter Project...
flutter clean
echo.

echo Step 5: Getting Flutter Dependencies...
flutter pub get
echo.

echo Step 6: Building APK with Java 17...
flutter build apk --debug
echo.

echo ========================================
echo Fix Complete!
echo ========================================
pause
