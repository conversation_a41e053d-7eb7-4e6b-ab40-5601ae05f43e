class NepaliDistrict {
  final String name;
  final String nameNepali;
  final String province;
  final double latitude;
  final double longitude;

  const NepaliDistrict({
    required this.name,
    required this.nameN<PERSON><PERSON>,
    required this.province,
    required this.latitude,
    required this.longitude,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'nameNepali': nameNepali,
      'province': province,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory NepaliDistrict.fromJson(Map<String, dynamic> json) {
    return NepaliDistrict(
      name: json['name'],
      nameNepali: json['nameNepali'],
      province: json['province'],
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }
}

class NepaliDistrictsData {
  static const List<NepaliDistrict> districts = [
    // Province 1 (Koshi Pradesh)
    NepaliDistrict(name: 'B<PERSON><PERSON><PERSON>', nameNepali: 'भोजपुर', province: 'कोशी प्रदेश', latitude: 27.1667, longitude: 87.0500),
    NepaliDistrict(name: '<PERSON><PERSON><PERSON><PERSON>', nameNepali: 'धनकुटा', province: 'कोशी प्रदेश', latitude: 26.9833, longitude: 87.3333),
    NepaliDistrict(name: 'Ilam', nameNepali: 'इलाम', province: 'कोशी प्रदेश', latitude: 26.9000, longitude: 87.9333),
    NepaliDistrict(name: 'Jhapa', nameNepali: 'झापा', province: 'कोशी प्रदेश', latitude: 26.5500, longitude: 87.9000),
    NepaliDistrict(name: 'Khotang', nameNepali: 'खोटाङ', province: 'कोशी प्रदेश', latitude: 27.0333, longitude: 86.8000),
    NepaliDistrict(name: 'Morang', nameNepali: 'मोरङ', province: 'कोशी प्रदेश', latitude: 26.6500, longitude: 87.4833),
    NepaliDistrict(name: 'Okhaldhunga', nameNepali: 'ओखलढुङ्गा', province: 'कोशी प्रदेश', latitude: 27.3167, longitude: 86.5000),
    NepaliDistrict(name: 'Panchthar', nameNepali: 'पाँचथर', province: 'कोशी प्रदेश', latitude: 27.1333, longitude: 87.8667),
    NepaliDistrict(name: 'Sankhuwasabha', nameNepali: 'संखुवासभा', province: 'कोशी प्रदेश', latitude: 27.6333, longitude: 87.1833),
    NepaliDistrict(name: 'Solukhumbu', nameNepali: 'सोलुखुम्बु', province: 'कोशी प्रदेश', latitude: 27.6167, longitude: 86.7167),
    NepaliDistrict(name: 'Sunsari', nameNepali: 'सुनसरी', province: 'कोशी प्रदेश', latitude: 26.6167, longitude: 87.1833),
    NepaliDistrict(name: 'Taplejung', nameNepali: 'ताप्लेजुङ', province: 'कोशी प्रदेश', latitude: 27.3500, longitude: 87.6667),
    NepaliDistrict(name: 'Terhathum', nameNepali: 'तेह्रथुम', province: 'कोशी प्रदेश', latitude: 27.1167, longitude: 87.1167),
    NepaliDistrict(name: 'Udayapur', nameNepali: 'उदयपुर', province: 'कोशी प्रदेश', latitude: 26.8500, longitude: 86.5500),

    // Province 2 (Madhesh Pradesh)
    NepaliDistrict(name: 'Bara', nameNepali: 'बारा', province: 'मधेश प्रदेश', latitude: 27.0000, longitude: 84.9167),
    NepaliDistrict(name: 'Dhanusha', nameNepali: 'धनुषा', province: 'मधेश प्रदेश', latitude: 26.7500, longitude: 85.9500),
    NepaliDistrict(name: 'Mahottari', nameNepali: 'महोत्तरी', province: 'मधेश प्रदेश', latitude: 26.8333, longitude: 85.7500),
    NepaliDistrict(name: 'Parsa', nameNepali: 'पर्सा', province: 'मधेश प्रदेश', latitude: 27.0500, longitude: 84.9333),
    NepaliDistrict(name: 'Rautahat', nameNepali: 'रौतहट', province: 'मधेश प्रदेश', latitude: 27.0167, longitude: 85.3833),
    NepaliDistrict(name: 'Saptari', nameNepali: 'सप्तरी', province: 'मधेश प्रदेश', latitude: 26.6167, longitude: 86.9167),
    NepaliDistrict(name: 'Sarlahi', nameNepali: 'सर्लाही', province: 'मधेश प्रदेश', latitude: 27.0000, longitude: 85.5500),
    NepaliDistrict(name: 'Siraha', nameNepali: 'सिराहा', province: 'मधेश प्रदेश', latitude: 26.6500, longitude: 86.2167),

    // Bagmati Pradesh
    NepaliDistrict(name: 'Bhaktapur', nameNepali: 'भक्तपुर', province: 'बागमती प्रदेश', latitude: 27.6710, longitude: 85.4298),
    NepaliDistrict(name: 'Chitwan', nameNepali: 'चितवन', province: 'बागमती प्रदेश', latitude: 27.5833, longitude: 84.5000),
    NepaliDistrict(name: 'Dhading', nameNepali: 'धादिङ', province: 'बागमती प्रदेश', latitude: 27.8667, longitude: 84.9167),
    NepaliDistrict(name: 'Dolakha', nameNepali: 'दोलखा', province: 'बागमती प्रदेश', latitude: 27.6667, longitude: 86.1667),
    NepaliDistrict(name: 'Kathmandu', nameNepali: 'काठमाडौं', province: 'बागमती प्रदेश', latitude: 27.7172, longitude: 85.3240),
    NepaliDistrict(name: 'Kavrepalanchok', nameNepali: 'काभ्रेपलाञ्चोक', province: 'बागमती प्रदेश', latitude: 27.5500, longitude: 85.5667),
    NepaliDistrict(name: 'Lalitpur', nameNepali: 'ललितपुर', province: 'बागमती प्रदेश', latitude: 27.6588, longitude: 85.3247),
    NepaliDistrict(name: 'Makwanpur', nameNepali: 'मकवानपुर', province: 'बागमती प्रदेश', latitude: 27.4333, longitude: 85.0333),
    NepaliDistrict(name: 'Nuwakot', nameNepali: 'नुवाकोट', province: 'बागमती प्रदेश', latitude: 27.9167, longitude: 85.1667),
    NepaliDistrict(name: 'Ramechhap', nameNepali: 'रामेछाप', province: 'बागमती प्रदेश', latitude: 27.3333, longitude: 86.0833),
    NepaliDistrict(name: 'Rasuwa', nameNepali: 'रसुवा', province: 'बागमती प्रदेश', latitude: 28.1167, longitude: 85.3333),
    NepaliDistrict(name: 'Sindhuli', nameNepali: 'सिन्धुली', province: 'बागमती प्रदेश', latitude: 27.2500, longitude: 85.9667),
    NepaliDistrict(name: 'Sindhupalchok', nameNepali: 'सिन्धुपाल्चोक', province: 'बागमती प्रदेश', latitude: 27.9500, longitude: 85.6833),

    // Gandaki Pradesh
    NepaliDistrict(name: 'Baglung', nameNepali: 'बागलुङ', province: 'गण्डकी प्रदेश', latitude: 28.2667, longitude: 83.5833),
    NepaliDistrict(name: 'Gorkha', nameNepali: 'गोरखा', province: 'गण्डकी प्रदेश', latitude: 28.0000, longitude: 84.6333),
    NepaliDistrict(name: 'Kaski', nameNepali: 'कास्की', province: 'गण्डकी प्रदेश', latitude: 28.2096, longitude: 83.9856),
    NepaliDistrict(name: 'Lamjung', nameNepali: 'लमजुङ', province: 'गण्डकी प्रदेश', latitude: 28.2333, longitude: 84.3833),
    NepaliDistrict(name: 'Manang', nameNepali: 'मनाङ', province: 'गण्डकी प्रदेश', latitude: 28.6667, longitude: 84.0167),
    NepaliDistrict(name: 'Mustang', nameNepali: 'मुस्ताङ', province: 'गण्डकी प्रदेश', latitude: 28.7833, longitude: 83.7333),
    NepaliDistrict(name: 'Myagdi', nameNepali: 'म्याग्दी', province: 'गण्डकी प्रदेश', latitude: 28.6000, longitude: 83.5667),
    NepaliDistrict(name: 'Nawalpur', nameNepali: 'नवलपुर', province: 'गण्डकी प्रदेश', latitude: 27.6333, longitude: 84.1000),
    NepaliDistrict(name: 'Parbat', nameNepali: 'पर्वत', province: 'गण्डकी प्रदेश', latitude: 28.2333, longitude: 83.6833),
    NepaliDistrict(name: 'Syangja', nameNepali: 'स्याङ्जा', province: 'गण्डकी प्रदेश', latitude: 28.0833, longitude: 83.8667),
    NepaliDistrict(name: 'Tanahun', nameNepali: 'तनहुँ', province: 'गण्डकी प्रदेश', latitude: 27.9167, longitude: 84.2500),

    // Lumbini Pradesh
    NepaliDistrict(name: 'Arghakhanchi', nameNepali: 'अर्घाखाँची', province: 'लुम्बिनी प्रदेश', latitude: 27.9500, longitude: 83.1167),
    NepaliDistrict(name: 'Banke', nameNepali: 'बाँके', province: 'लुम्बिनी प्रदेश', latitude: 28.1500, longitude: 81.6167),
    NepaliDistrict(name: 'Bardiya', nameNepali: 'बर्दिया', province: 'लुम्बिनी प्रदेश', latitude: 28.3333, longitude: 81.3333),
    NepaliDistrict(name: 'Dang', nameNepali: 'दाङ', province: 'लुम्बिनी प्रदेश', latitude: 28.0333, longitude: 82.3000),
    NepaliDistrict(name: 'Gulmi', nameNepali: 'गुल्मी', province: 'लुम्बिनी प्रदेश', latitude: 28.0833, longitude: 83.2833),
    NepaliDistrict(name: 'Kapilvastu', nameNepali: 'कपिलवस्तु', province: 'लुम्बिनी प्रदेश', latitude: 27.5667, longitude: 83.0500),
    NepaliDistrict(name: 'Nawalparasi East', nameNepali: 'नवलपरासी पूर्व', province: 'लुम्बिनी प्रदेश', latitude: 27.6333, longitude: 84.1000),
    NepaliDistrict(name: 'Nawalparasi West', nameNepali: 'नवलपरासी पश्चिम', province: 'लुम्बिनी प्रदेश', latitude: 27.6167, longitude: 83.4500),
    NepaliDistrict(name: 'Palpa', nameNepali: 'पाल्पा', province: 'लुम्बिनी प्रदेश', latitude: 27.8667, longitude: 83.5500),
    NepaliDistrict(name: 'Pyuthan', nameNepali: 'प्युठान', province: 'लुम्बिनी प्रदेश', latitude: 28.1000, longitude: 82.8167),
    NepaliDistrict(name: 'Rolpa', nameNepali: 'रोल्पा', province: 'लुम्बिनी प्रदेश', latitude: 28.2833, longitude: 82.6333),
    NepaliDistrict(name: 'Rupandehi', nameNepali: 'रुपन्देही', province: 'लुम्बिनी प्रदेश', latitude: 27.4833, longitude: 83.4667),

    // Karnali Pradesh
    NepaliDistrict(name: 'Dailekh', nameNepali: 'दैलेख', province: 'कर्णाली प्रदेश', latitude: 28.8500, longitude: 81.7167),
    NepaliDistrict(name: 'Dolpa', nameNepali: 'डोल्पा', province: 'कर्णाली प्रदेश', latitude: 29.0000, longitude: 82.8333),
    NepaliDistrict(name: 'Humla', nameNepali: 'हुम्ला', province: 'कर्णाली प्रदेश', latitude: 30.1167, longitude: 81.5000),
    NepaliDistrict(name: 'Jajarkot', nameNepali: 'जाजरकोट', province: 'कर्णाली प्रदेश', latitude: 28.7000, longitude: 82.1833),
    NepaliDistrict(name: 'Jumla', nameNepali: 'जुम्ला', province: 'कर्णाली प्रदेश', latitude: 29.2833, longitude: 82.1833),
    NepaliDistrict(name: 'Kalikot', nameNepali: 'कालिकोट', province: 'कर्णाली प्रदेश', latitude: 29.1000, longitude: 81.2167),
    NepaliDistrict(name: 'Mugu', nameNepali: 'मुगु', province: 'कर्णाली प्रदेश', latitude: 29.6833, longitude: 82.1667),
    NepaliDistrict(name: 'Rukum West', nameNepali: 'रुकुम पश्चिम', province: 'कर्णाली प्रदेश', latitude: 28.6167, longitude: 82.2833),
    NepaliDistrict(name: 'Salyan', nameNepali: 'सल्यान', province: 'कर्णाली प्रदेश', latitude: 28.3833, longitude: 82.1667),
    NepaliDistrict(name: 'Surkhet', nameNepali: 'सुर्खेत', province: 'कर्णाली प्रदेश', latitude: 28.6000, longitude: 81.6167),

    // Sudurpashchim Pradesh
    NepaliDistrict(name: 'Achham', nameNepali: 'अछाम', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.0500, longitude: 81.2167),
    NepaliDistrict(name: 'Baitadi', nameNepali: 'बैतडी', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.5333, longitude: 80.4667),
    NepaliDistrict(name: 'Bajhang', nameNepali: 'बझाङ', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.5333, longitude: 81.2000),
    NepaliDistrict(name: 'Bajura', nameNepali: 'बाजुरा', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.5500, longitude: 81.6667),
    NepaliDistrict(name: 'Dadeldhura', nameNepali: 'डडेल्धुरा', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.3000, longitude: 80.5833),
    NepaliDistrict(name: 'Darchula', nameNepali: 'दार्चुला', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.8500, longitude: 80.5500),
    NepaliDistrict(name: 'Doti', nameNepali: 'डोटी', province: 'सुदूरपश्चिम प्रदेश', latitude: 29.2667, longitude: 80.9833),
    NepaliDistrict(name: 'Kailali', nameNepali: 'कैलाली', province: 'सुदूरपश्चिम प्रदेश', latitude: 28.7500, longitude: 80.7500),
    NepaliDistrict(name: 'Kanchanpur', nameNepali: 'कञ्चनपुर', province: 'सुदूरपश्चिम प्रदेश', latitude: 28.8333, longitude: 80.1667),
  ];

  static List<NepaliDistrict> getDistrictsByProvince(String province) {
    return districts.where((district) => district.province == province).toList();
  }

  static List<String> getAllProvinces() {
    return districts.map((district) => district.province).toSet().toList();
  }

  static NepaliDistrict? getDistrictByName(String name) {
    try {
      return districts.firstWhere((district) => 
        district.name.toLowerCase() == name.toLowerCase() ||
        district.nameNepali == name
      );
    } catch (e) {
      return null;
    }
  }
}
