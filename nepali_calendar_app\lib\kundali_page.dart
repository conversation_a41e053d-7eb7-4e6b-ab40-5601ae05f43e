import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'models/user_data.dart';
import 'services/user_data_service.dart';
import 'data/nepali_districts.dart';
import 'pages/add_user_page.dart';
import 'pages/user_list_page.dart';
import 'pages/kundali_features_page.dart';

class KundaliPage extends StatefulWidget {
  const KundaliPage({super.key});

  @override
  State<KundaliPage> createState() => _KundaliPageState();
}

class _KundaliPageState extends State<KundaliPage> {
  List<UserData> _userDataList = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() => _isLoading = true);

    try {
      final userDataList = await UserDataService.loadUserDataList();

      setState(() {
        _userDataList = userDataList;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading user data: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'कुण्डली सेवा',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            onPressed: () => _navigateToAddUser(),
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingWidget() : _buildMainContent(),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(color: Colors.amber),
    );
  }

  Widget _buildMainContent() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
        ),
      ),
      child: Column(
        children: [
          _buildHeaderCard(),
          const SizedBox(height: 20),
          if (_userDataList.isEmpty)
            Expanded(child: _buildEmptyState())
          else
            Expanded(child: _buildSimpleUserList()),
        ],
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4CAF50).withOpacity(0.1),
            const Color(0xFF2E7D32).withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          const Icon(
            Icons.auto_awesome,
            size: 40,
            color: Color(0xFF2E7D32),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'सबै प्रयोगकर्ता',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                Text(
                  'कुल: ${_userDataList.length} जना',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton.icon(
            onPressed: () => _navigateToAddUser(),
            icon: const Icon(Icons.add, size: 18),
            label: const Text('नयाँ'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.person_add_outlined,
            size: 80,
            color: const Color(0xFF4CAF50).withOpacity(0.7),
          ),
          const SizedBox(height: 20),
          const Text(
            'कुनै प्रयोगकर्ता डेटा छैन',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'कुण्डली सेवा प्रयोग गर्न पहिले आफ्नो जन्म विवरण थप्नुहोस्',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF388E3C),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: () => _navigateToAddUser(),
            icon: const Icon(Icons.add),
            label: const Text('नयाँ प्रयोगकर्ता थप्नुहोस्'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }





  Widget _buildSimpleUserList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _userDataList.length,
      itemBuilder: (context, index) {
        final user = _userDataList[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF4CAF50).withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: CircleAvatar(
              radius: 25,
              backgroundColor: const Color(0xFF4CAF50),
              child: Text(
                user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            title: Text(
              user.name,
              style: const TextStyle(
                color: Color(0xFF1B5E20),
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '${user.gender} • ${user.ageInYears} वर्ष',
                  style: const TextStyle(
                    color: Color(0xFF388E3C),
                    fontSize: 14,
                  ),
                ),
                Text(
                  user.district, // Removed duplicate birthPlace
                  style: const TextStyle(
                    color: Color(0xFF66BB6A),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF4CAF50),
              size: 16,
            ),
            onTap: () => _navigateToKundaliFeatures(user),
          ),
        );
      },
    );
  }

  void _navigateToKundaliFeatures(UserData user) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KundaliFeaturesPage(user: user),
      ),
    );

    if (result == true) {
      _loadUserData(); // Refresh user list if data was modified
    }
  }

  void _navigateToAddUser() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddUserPage()),
    );

    if (result == true) {
      _loadUserData();
    }
  }
}
