import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'nepali_calendar_page.dart';
import 'kundali_page.dart';
import 'pages/sahit_page.dart';
import 'pages/date_converter_page.dart';
import 'pages/forex_page.dart';
import 'pages/simple_vegetable_page.dart';
import 'pages/share_market_page.dart';
import 'pages/fm_radio_page.dart';
import 'pages/panchang_page.dart';
import 'pages/news_page.dart';
import 'pages/rasifal_page.dart';
import 'pages/bhagavad_gita_video_page.dart';
import 'pages/hindu_mantra_audio_page.dart';
import 'pages/gold_silver_premium_page.dart';
import 'pages/games_page.dart';
import 'pages/gau_khane_katha_page.dart';
import 'pages/interest_rate_page.dart';
import 'pages/dream_interpretation_page.dart';
import 'pages/weather_page.dart';
import 'pages/unit_conversion_page.dart';
import 'pages/mobile_price_page.dart';

import 'services/github_api_manager.dart';


void main() async {
  // Initialize Flutter binding
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GitHub API manager
  await GitHubApiManager.initialize();

  runApp(const NepaliCalendarApp());
}

class NepaliCalendarApp extends StatelessWidget {
  const NepaliCalendarApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'नेपाली पात्रो',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        fontFamily: 'NotoSansDevanagari',
        textTheme: const TextTheme().apply(
          fontFamily: 'NotoSansDevanagari',
        ),
      ),
      home: const HomePage(),
      debugShowCheckedModeBanner: false,
    );
  }


}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late NepaliDateTime nepaliDate;
  late DateTime englishDate;
  late String currentTime;
  late Timer _timer;
  bool _isDarkMode = false;
  int _selectedIndex = 0;
  bool _isNepaliLanguage = true; // Default to Nepali

  // Festival data for current date
  Map<String, String> currentFestival = {};

  @override
  void initState() {
    super.initState();
    _updateDateTime();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }



  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) { // Check if widget is still mounted to prevent memory leaks
        _updateDateTime();
      }
    });
  }



  void _updateDateTime() {
    setState(() {
      englishDate = DateTime.now();
      nepaliDate = englishDate.toNepaliDateTime();
      // Simple time format without intl dependency
      final hour = englishDate.hour > 12 ? englishDate.hour - 12 : englishDate.hour == 0 ? 12 : englishDate.hour;
      final minute = englishDate.minute.toString().padLeft(2, '0');
      final period = englishDate.hour >= 12 ? 'PM' : 'AM';
      currentTime = '$hour:$minute $period';

      // Update current festival data
      _updateCurrentFestival();
    });
  }

  void _updateCurrentFestival() {
    // Get festival for current date
    final dayKey = nepaliDate.day.toString();
    final monthFestivals = _getFestivalsForMonth(nepaliDate.year, nepaliDate.month);

    if (monthFestivals.containsKey(dayKey)) {
      final festivals = monthFestivals[dayKey]!;
      if (festivals.isNotEmpty) {
        currentFestival = {
          'title': festivals.first.title,
          'description': festivals.first.description,
        };
      } else {
        currentFestival = {};
      }
    } else {
      currentFestival = {};
    }
  }



  @override
  Widget build(BuildContext context) {
    // Set navigation bar color to dark so white buttons are visible
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.black87, // Dark navigation bar
      systemNavigationBarIconBrightness: Brightness.light, // Light icons
    ));

    return Scaffold(
      backgroundColor: _isDarkMode ? Colors.grey[900] : Colors.grey[100],
      appBar: AppBar(
        backgroundColor: const Color(0xFF3F51B5),
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: const Text(
          'नेपाली पात्रो',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // Share app functionality
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              _showSettingsDialog();
            },
          ),
        ],

      ),
      drawer: _buildDrawer(),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 80.0), // Extra padding for navigation bar
            child: Column(
              children: [
            // Top 3 Function Cards
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(child: _buildTopCard(
                    _isNepaliLanguage ? 'नेपाली पात्रो' : 'Nepali Calendar',
                    Icons.calendar_today,
                    Colors.red,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const NepaliCalendarPage()),
                      );
                    },
                  )),
                  const SizedBox(width: 10),
                  Expanded(child: _buildTopCard(
                    _isNepaliLanguage ? 'राशिफल' : 'Horoscope',
                    Icons.star,
                    Colors.orange,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => RasifalPage()),
                      );
                    },
                  )),
                  const SizedBox(width: 10),
                  Expanded(child: _buildTopCard(
                    _isNepaliLanguage ? 'समाचार' : 'News',
                    Icons.article,
                    Colors.green,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const NewsPage()),
                      );
                    },
                  )),
                ],
              ),
            ),

            // Very Small Gap Above Date/Time
            const SizedBox(height: 6),

            // Date and Time Display - Bigger Width & Letters
            Container(
              width: double.infinity,
              margin: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [const Color(0xFF1A237E), const Color(0xFF3F51B5)],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.indigo.withOpacity(0.3),
                    blurRadius: 15,
                    spreadRadius: 2,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Nepali Date - BIGGER TEXT
                  Text(
                    '${_getNepaliDayName()}, ${_convertToNepaliNumerals(nepaliDate.day.toString())} ${_getNepaliMonthName()}, ${_convertToNepaliNumerals(nepaliDate.year.toString())}',
                    style: const TextStyle(
                      fontSize: 26, // INCREASED from 22 to 26
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  // Current Tithi and Festival Details - BIGGER TEXT
                  Text(
                    _buildTithiAndFestivalText(),
                    style: TextStyle(
                      fontSize: 20, // INCREASED from 16 to 20
                      fontWeight: FontWeight.w600,
                      color: Colors.yellow[300]!,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 4, // INCREASED from 3 to 4 lines
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // English Date - BIGGER TEXT
                  Text(
                    _getEnglishDateString(englishDate),
                    style: const TextStyle(
                      fontSize: 22, // INCREASED from 18 to 22
                      color: Colors.white70,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 6),
                  // Current Time - BIGGER TEXT
                  Text(
                    currentTime,
                    style: const TextStyle(
                      fontSize: 24, // INCREASED from 20 to 24
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 0.8,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Professional Features Grid
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: _buildProfessionalFeatures(),
            ),

            // No bottom gap - SafeArea handles navigation protection
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF0D47A1), // Dark Blue
              const Color(0xFF1565C0), // Medium Dark Blue
              const Color(0xFF0277BD), // Deep Blue
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF0D47A1).withOpacity(0.6),
              blurRadius: 25,
              spreadRadius: 4,
              offset: const Offset(0, -10),
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.4),
              blurRadius: 15,
              spreadRadius: 2,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: Colors.white70, // Lighter white for all
          unselectedItemColor: Colors.white70, // Lighter white for all
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: Colors.white70, // Lighter bold text
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold, // Bold for all text
            color: Colors.white70, // Lighter bold text
          ),
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });

            // Navigate to calendar page when Patro is tapped
            if (index == 1) { // Patro button
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const NepaliCalendarPage()),
              );
            }
          },
          items: [
            BottomNavigationBarItem(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: _selectedIndex == 0 ? BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF42A5F5), // Medium Blue
                      const Color(0xFF1E88E5), // Darker Blue
                      const Color(0xFF1565C0), // Dark Blue
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF42A5F5).withOpacity(0.4),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ) : null,
                child: Icon(
                  Icons.home,
                  size: 26,
                  color: Colors.white70, // Lighter color for all states
                ),
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: _selectedIndex == 1 ? BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF42A5F5), // Medium Blue
                      const Color(0xFF1E88E5), // Darker Blue
                      const Color(0xFF1565C0), // Dark Blue
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF42A5F5).withOpacity(0.4),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ) : null,
                child: Icon(
                  Icons.calendar_today,
                  size: 26,
                  color: Colors.white70, // Lighter color for all states
                ),
              ),
              label: 'Patro',
            ),
            BottomNavigationBarItem(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: _selectedIndex == 2 ? BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF42A5F5), // Medium Blue
                      const Color(0xFF1E88E5), // Darker Blue
                      const Color(0xFF1565C0), // Dark Blue
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF42A5F5).withOpacity(0.4),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ) : null,
                child: Icon(
                  Icons.star,
                  size: 26,
                  color: Colors.white70, // Lighter color for all states
                ),
              ),
              label: 'Horoscope',
            ),
            BottomNavigationBarItem(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: _selectedIndex == 3 ? BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF42A5F5), // Medium Blue
                      const Color(0xFF1E88E5), // Darker Blue
                      const Color(0xFF1565C0), // Dark Blue
                    ],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF42A5F5).withOpacity(0.4),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ) : null,
                child: Icon(
                  Icons.games,
                  size: 26,
                  color: Colors.white70, // Lighter color for all states
                ),
              ),
              label: 'Play Game',
            ),
          ],
        ),
      ),
    );
  }





  Widget _buildTopCard(String title, IconData icon, Color color, {VoidCallback? onTap}) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(15),
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 36),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }





  Widget _buildProfessionalFeatureCard(String title, IconData icon, Color color) {
    return Container(
      height: 110,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(15),
        onTap: () {
          // Navigate based on feature title
          if (title == 'Sahit' || title == 'साहित') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SahitPage()),
            );
          } else if (title == 'Date Converter' || title == 'मिति रूपान्तरण') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const DateConverterPage()),
            );
          } else if (title == 'Foreign Exchange' || title == 'बिदेशी बिनिमय दर') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ForexPage()),
            );
          } else if (title == 'Vegetable Rate' || title == 'तरकारी बजारभाउ') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SimpleVegetablePage()),
            );
          } else if (title == 'Share Market' || title == 'सेयर बजार') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ShareMarketPage()),
            );
          } else if (title == 'FM Radio' || title == 'एफ-एम रेडियो') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const FMRadioPage()),
            );
          } else if (title == 'Horoscope' || title == 'राशिफल') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const RasifalPage()),
            );
          } else if (title == 'Nepali Panchang' || title == 'नेपाली पंचांग') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const PanchangPage()),
            );
          } else if (title == 'Bhagavad Gita' || title == 'श्रीमद भगवद गीता') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const BhagavadGitaVideoPage()),
            );
          } else if (title == 'Hindu Mantra Audio' || title == 'हिन्दू मंत्र अडियो') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const HinduMantraAudioPage()),
            );
          } else if (title == 'Gold/Silver Rate' || title == 'सुन-चादी बजारभाउ') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GoldSilverPremiumPage()),
            );
          } else if (title == 'Games' || title == 'खेलहरू') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GamesPage()),
            );
          } else if (title == 'Gau Khane Katha' || title == 'गाउखाने कथा') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GauKhaneKathaPage()),
            );
          } else if (title == 'Bank Interest Rate' || title == 'ब्याजदर') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const InterestRatePage()),
            );
          } else if (title == 'Dream Interpretation' || title == 'सपनाको फल') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const DreamInterpretationPage()),
            );
          } else if (title == 'Weather' || title == 'मौसम') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const WeatherPage()),
            );
          } else if (title == 'Unit Conversion' || title == 'एकाइ रूपान्तरण') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const UnitConversionPage()),
            );
          } else if (title == 'Price in Nepal' || title == 'नेपालमा मूल्य') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const MobilePricePage()),
            );
          }

          // Other feature navigations can be added here
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 36),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Settings Dialog
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                _isNepaliLanguage ? 'सेटिङ्गहरू' : 'Settings',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Language Setting
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF8E24AA).withOpacity(0.1),
                          const Color(0xFFE91E63).withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFF8E24AA).withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.language,
                          color: const Color(0xFF8E24AA),
                          size: 28,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _isNepaliLanguage ? 'भाषा' : 'Language',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _isNepaliLanguage
                                  ? 'एप्लिकेसनको भाषा परिवर्तन गर्नुहोस्'
                                  : 'Change app language',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Switch(
                          value: _isNepaliLanguage,
                          onChanged: (value) {
                            setDialogState(() {
                              _isNepaliLanguage = value;
                            });
                            setState(() {
                              _isNepaliLanguage = value;
                            });
                          },
                          activeColor: const Color(0xFF8E24AA),
                          inactiveThumbColor: Colors.grey,
                          inactiveTrackColor: Colors.grey[300],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Language Labels
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'English',
                        style: TextStyle(
                          color: !_isNepaliLanguage ? const Color(0xFF8E24AA) : Colors.grey,
                          fontWeight: !_isNepaliLanguage ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      Text(
                        'नेपाली',
                        style: TextStyle(
                          color: _isNepaliLanguage ? const Color(0xFF8E24AA) : Colors.grey,
                          fontWeight: _isNepaliLanguage ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    _isNepaliLanguage ? 'बन्द गर्नुहोस्' : 'Close',
                    style: const TextStyle(
                      color: Color(0xFF8E24AA),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildDrawer() {
    // Complete features list - all 25 features with performance optimizations
    final List<Map<String, dynamic>> drawerFeatures = [
      {'titleEnglish': 'Nepali Calendar', 'titleNepali': 'नेपाली पात्रो', 'icon': Icons.calendar_today, 'color': Colors.indigo},
      {'titleEnglish': 'Nepali Panchang', 'titleNepali': 'नेपाली पंचांग', 'icon': Icons.schedule, 'color': Colors.deepOrange},
      {'titleEnglish': 'Kundali', 'titleNepali': 'कुन्डली', 'icon': Icons.star_border, 'color': Colors.amber[800]},
      {'titleEnglish': 'Horoscope', 'titleNepali': 'राशिफल', 'icon': Icons.psychology, 'color': Colors.pink},
      {'titleEnglish': 'Hindu Mantra Audio', 'titleNepali': 'हिन्दू मंत्र अडियो', 'icon': Icons.music_note, 'color': Colors.red[700]},
      {'titleEnglish': 'Sahit', 'titleNepali': 'साहित', 'icon': Icons.menu_book, 'color': Colors.brown},
      {'titleEnglish': 'News', 'titleNepali': 'समाचार', 'icon': Icons.newspaper, 'color': Colors.blue[800]},
      {'titleEnglish': 'FM Radio', 'titleNepali': 'एफ-एम रेडियो', 'icon': Icons.radio, 'color': Colors.purple[700]},
      {'titleEnglish': 'Bhagavad Gita', 'titleNepali': 'श्रीमद भगवद गीता', 'icon': Icons.auto_stories, 'color': Colors.orange[800]},
      {'titleEnglish': 'Date Converter', 'titleNepali': 'मिति रूपान्तरण', 'icon': Icons.date_range, 'color': Colors.teal[700]},
      {'titleEnglish': 'Foreign Exchange', 'titleNepali': 'बिदेशी बिनिमय दर', 'icon': Icons.currency_exchange, 'color': Colors.green[700]},
      {'titleEnglish': 'Gold/Silver Rate', 'titleNepali': 'सुन-चादी बजारभाउ', 'icon': Icons.monetization_on, 'color': Colors.yellow[800]},
      {'titleEnglish': 'Vegetable Rate', 'titleNepali': 'तरकारी बजारभाउ', 'icon': Icons.local_grocery_store, 'color': Colors.lightGreen},
      {'titleEnglish': 'Games', 'titleNepali': 'खेलहरू', 'icon': Icons.games, 'color': Colors.blue[700]},
      {'titleEnglish': 'Share Market', 'titleNepali': 'सेयर बजार', 'icon': Icons.trending_up, 'color': Colors.green[800]},
      {'titleEnglish': 'Weather', 'titleNepali': 'मौसम', 'icon': Icons.wb_sunny, 'color': Colors.orange},

      {'titleEnglish': 'Dictionary', 'titleNepali': 'शब्दकोश', 'icon': Icons.translate, 'color': Colors.indigo[700]},
      {'titleEnglish': 'Bank Interest Rate', 'titleNepali': 'ब्याजदर', 'icon': Icons.account_balance, 'color': Colors.cyan[700]},
      {'titleEnglish': 'Nepali Map', 'titleNepali': 'बैंक', 'icon': Icons.map, 'color': Colors.red[600]},
      {'titleEnglish': 'Dream Interpretation', 'titleNepali': 'सपनाको फल', 'icon': Icons.bedtime, 'color': Colors.purple[600]},
      {'titleEnglish': 'Unit Conversion', 'titleNepali': 'एकाइ रूपान्तरण', 'icon': Icons.calculate, 'color': Colors.teal[600]},
      {'titleEnglish': 'Price in Nepal', 'titleNepali': 'नेपालमा मूल्य', 'icon': Icons.smartphone, 'color': Colors.blue[600]},
      {'titleEnglish': 'Pooja Bidhi', 'titleNepali': 'पूजा विधि', 'icon': Icons.temple_hindu, 'color': Colors.orange[700]},
      {'titleEnglish': 'Gau Khane Katha', 'titleNepali': 'गाउखाने कथा', 'icon': Icons.quiz, 'color': Colors.pink[600]},
      {'titleEnglish': 'Funny Video', 'titleNepali': 'रमाइलो भिडियो', 'icon': Icons.video_library, 'color': Colors.red[500]},
    ];

    return Drawer(
      child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF8E24AA), // Purple
                Color(0xFFAD1457), // Deep Pink
                Color(0xFFE91E63), // Pink
              ],
            ),
          ),
          child: Column(
          children: [
            // Drawer Header - Minimal space for top bar
            Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8), // Much smaller padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        radius: 35,
                        backgroundColor: Colors.white,
                        child: Icon(
                          Icons.calendar_today,
                          size: 35,
                          color: Color(0xFF8E24AA), // Purple to match new theme
                        ),
                      ),
                      const Spacer(),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isNepaliLanguage ? 'नेपाली पात्रो' : 'Nepali Calendar',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _isNepaliLanguage ? 'सबै सुविधाहरू' : 'All Features',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(color: Colors.white30, thickness: 1),
            // Features List - Very minimal gap to start functions
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 2),
                itemCount: drawerFeatures.length,
                physics: const BouncingScrollPhysics(), // Smoother scrolling
                cacheExtent: 100, // Optimize rendering
                itemBuilder: (context, index) {
                  final feature = drawerFeatures[index];
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 1),
                    child: Material(
                      color: Colors.white.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(10),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(10),
                        onTap: () {
                          HapticFeedback.lightImpact();
                          Navigator.pop(context);

                          // Navigate to specific pages based on feature
                          if (index == 0) { // Nepali Calendar
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const NepaliCalendarPage()),
                            );
                          } else if (index == 1) { // Nepali Panchang
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const PanchangPage()),
                            );
                          } else if (index == 2) { // Kundali
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const KundaliPage()),
                            );
                          } else if (index == 3) { // Rasifal
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const RasifalPage()),
                            );
                          } else if (index == 4) { // Hindu Mantra Audio
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const HinduMantraAudioPage()),
                            );
                          } else if (index == 6) { // News
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const NewsPage()),
                            );
                          } else if (index == 8) { // Bhagavad Gita
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const BhagavadGitaVideoPage()),
                            );
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          child: Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: feature['color'],
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  feature['icon'],
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _isNepaliLanguage ? feature['titleNepali'] : feature['titleEnglish'],
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              const Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.white60,
                                size: 12,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Divider(color: Colors.white30, thickness: 1),

                  ListTile(
                    leading: const Icon(Icons.info, color: Colors.white),
                    title: const Text(
                      'हाम्रो बारेमा',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    onTap: () {
                      // About section - no action needed
                    },
                  ),
                ],
              ),
            ),
            // Bottom padding to prevent overlap with device navigation
            const SizedBox(height: 24), // Minimal bottom padding
          ],
        ),
      ),
    );
  }

  String _getNepaliDayName() {
    List<String> nepaliDays = [
      'आइतबार', 'सोमबार', 'मंगलबार', 'बुधबार', 'बिहिबार', 'शुक्रबार', 'शनिबार'
    ];
    return nepaliDays[nepaliDate.weekday - 1];
  }

  String _getNepaliMonthName() {
    List<String> nepaliMonths = [
      'बैशाख', 'जेठ', 'असार', 'श्रावण', 'भाद्र', 'आश्विन',
      'कार्तिक', 'मंसिर', 'पौष', 'माघ', 'फाल्गुन', 'चैत्र'
    ];
    return nepaliMonths[nepaliDate.month - 1];
  }



  String _convertToNepaliNumerals(String englishNumber) {
    const Map<String, String> numeralMap = {
      '0': '०',
      '1': '१',
      '2': '२',
      '3': '३',
      '4': '४',
      '5': '५',
      '6': '६',
      '7': '७',
      '8': '८',
      '9': '९',
    };

    String result = englishNumber;
    numeralMap.forEach((english, nepali) {
      result = result.replaceAll(english, nepali);
    });
    return result;
  }

  String _getEnglishDateString(DateTime date) {
    const List<String> weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const List<String> months = ['January', 'February', 'March', 'April', 'May', 'June',
                                'July', 'August', 'September', 'October', 'November', 'December'];

    final weekday = weekdays[date.weekday - 1];
    final month = months[date.month - 1];
    final day = date.day.toString().padLeft(2, '0');
    final year = date.year;

    return '$weekday, $month $day, $year';
  }

  Widget _buildProfessionalFeatures() {
    // Features list - removed Nepali Calendar, Kundali, News (as they're in top cards)
    final List<Map<String, dynamic>> features = [
      {'titleEnglish': 'Nepali Panchang', 'titleNepali': 'नेपाली पंचांग', 'icon': Icons.schedule, 'color': Colors.deepOrange},
      {'titleEnglish': 'Horoscope', 'titleNepali': 'राशिफल', 'icon': Icons.psychology, 'color': Colors.pink},
      {'titleEnglish': 'Hindu Mantra Audio', 'titleNepali': 'हिन्दू मंत्र अडियो', 'icon': Icons.music_note, 'color': Colors.red[700]},
      {'titleEnglish': 'Sahit', 'titleNepali': 'साहित', 'icon': Icons.menu_book, 'color': Colors.brown},
      {'titleEnglish': 'FM Radio', 'titleNepali': 'एफ-एम रेडियो', 'icon': Icons.radio, 'color': Colors.purple[700]},
      {'titleEnglish': 'Bhagavad Gita', 'titleNepali': 'श्रीमद भगवद गीता', 'icon': Icons.auto_stories, 'color': Colors.orange[800]},
      {'titleEnglish': 'Date Converter', 'titleNepali': 'मिति रूपान्तरण', 'icon': Icons.date_range, 'color': Colors.teal[700]},
      {'titleEnglish': 'Foreign Exchange', 'titleNepali': 'बिदेशी बिनिमय दर', 'icon': Icons.currency_exchange, 'color': Colors.green[700]},
      {'titleEnglish': 'Gold/Silver Rate', 'titleNepali': 'सुन-चादी बजारभाउ', 'icon': Icons.monetization_on, 'color': Colors.yellow[800]},
      {'titleEnglish': 'Vegetable Rate', 'titleNepali': 'तरकारी बजारभाउ', 'icon': Icons.local_grocery_store, 'color': Colors.lightGreen},
      {'titleEnglish': 'Games', 'titleNepali': 'खेलहरू', 'icon': Icons.games, 'color': Colors.blue[700]},
      {'titleEnglish': 'Share Market', 'titleNepali': 'सेयर बजार', 'icon': Icons.trending_up, 'color': Colors.green[800]},
      {'titleEnglish': 'Weather', 'titleNepali': 'मौसम', 'icon': Icons.wb_sunny, 'color': Colors.orange},

      {'titleEnglish': 'Dictionary', 'titleNepali': 'शब्दकोश', 'icon': Icons.translate, 'color': Colors.indigo[700]},
      {'titleEnglish': 'Bank Interest Rate', 'titleNepali': 'ब्याजदर', 'icon': Icons.account_balance, 'color': Colors.cyan[700]},
      {'titleEnglish': 'Nepali Map', 'titleNepali': 'बैंक', 'icon': Icons.map, 'color': Colors.red[600]},
      {'titleEnglish': 'Dream Interpretation', 'titleNepali': 'सपनाको फल', 'icon': Icons.bedtime, 'color': Colors.purple[600]},
      {'titleEnglish': 'Unit Conversion', 'titleNepali': 'एकाइ रूपान्तरण', 'icon': Icons.calculate, 'color': Colors.teal[600]},
      {'titleEnglish': 'Price in Nepal', 'titleNepali': 'नेपालमा मूल्य', 'icon': Icons.smartphone, 'color': Colors.blue[600]},
      {'titleEnglish': 'Pooja Bidhi', 'titleNepali': 'पूजा विधि', 'icon': Icons.temple_hindu, 'color': Colors.orange[700]},
      {'titleEnglish': 'Gau Khane Katha', 'titleNepali': 'गाउखाने कथा', 'icon': Icons.quiz, 'color': Colors.pink[600]},
      {'titleEnglish': 'Funny Video', 'titleNepali': 'रमाइलो भिडियो', 'icon': Icons.video_library, 'color': Colors.red[500]},
    ];

    return Column(
      children: [
        for (int i = 0; i < features.length; i += 2)
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Row(
              children: [
                for (int j = i; j < i + 2 && j < features.length; j++) ...[
                  Expanded(
                    child: _buildProfessionalFeatureCard(
                      _isNepaliLanguage ? features[j]['titleNepali'] : features[j]['titleEnglish'],
                      features[j]['icon'],
                      features[j]['color'],
                    ),
                  ),
                  if (j < i + 1 && j < features.length - 1) const SizedBox(width: 6),
                ],
                // Add empty expanded widget if the row is not complete
                if ((i + 2) > features.length)
                  const Expanded(child: SizedBox()),
              ],
            ),
          ),
      ],
    );
  }

  // Get Tithi for specific date
  String _getTithiForDate(int year, int month, int day) {
    if (year == 2082) {
      final monthTithis = _getTithisForMonth(year, month);
      return monthTithis[day.toString()] ?? '';
    }
    return '';
  }

  // Get Tithi data for specific month
  Map<String, String> _getTithisForMonth(int year, int month) {
    if (year == 2082) {
      switch (month) {
        case 1: // Baishakh 2082 (बैशाख २०८२)
          return {
            '1': 'प्रतिपदा', '2': 'द्वितीया', '3': 'तृतीया', '4': 'चतुर्थी', '5': 'पञ्चमी',
            '6': 'षष्ठी', '7': 'सप्तमी', '8': 'अष्टमी', '9': 'नवमी', '10': 'दशमी',
            '11': 'एकादशी', '12': 'द्वादशी', '13': 'त्रयोदशी', '14': 'चतुर्दशी', '15': 'पूर्णिमा',
            '16': 'प्रतिपदा', '17': 'द्वितीया', '18': 'तृतीया', '19': 'चतुर्थी', '20': 'पञ्चमी',
            '21': 'षष्ठी', '22': 'सप्तमी', '23': 'अष्टमी', '24': 'नवमी', '25': 'दशमी',
            '26': 'एकादशी', '27': 'द्वादशी', '28': 'त्रयोदशी', '29': 'चतुर्दशी', '30': 'औंसी', '31': 'प्रतिपदा', '32': 'द्वितीया'
          };
        case 2: // Jestha 2082 (जेष्ठ २०८२)
          return {
            '1': 'तृतीया', '2': 'चतुर्थी', '3': 'पञ्चमी', '4': 'षष्ठी', '5': 'सप्तमी',
            '6': 'अष्टमी', '7': 'नवमी', '8': 'दशमी', '9': 'एकादशी', '10': 'द्वादशी',
            '11': 'त्रयोदशी', '12': 'चतुर्दशी', '13': 'पूर्णिमा', '14': 'प्रतिपदा', '15': 'द्वितीया',
            '16': 'तृतीया', '17': 'चतुर्थी', '18': 'पञ्चमी', '19': 'षष्ठी', '20': 'सप्तमी',
            '21': 'अष्टमी', '22': 'नवमी', '23': 'दशमी', '24': 'एकादशी', '25': 'द्वादशी',
            '26': 'त्रयोदशी', '27': 'चतुर्दशी', '28': 'औंसी', '29': 'प्रतिपदा', '30': 'द्वितीया', '31': 'तृतीया', '32': 'चतुर्थी'
          };
        case 3: // Ashar 2082 (असार २०८२)
          return {
            '1': 'चतुर्थी', '2': 'पञ्चमी', '3': 'षष्ठी', '4': 'सप्तमी', '5': 'अष्टमी',
            '6': 'नवमी', '7': 'एकादशी', '8': 'द्वादशी', '9': 'त्रयोदशी', '10': 'चतुर्दशी',
            '11': 'औंसी', '12': 'प्रतिपदा', '13': 'द्वितीया', '14': 'तृतीया', '15': 'चतुर्थी',
            '16': 'पञ्चमी', '17': 'षष्ठी', '18': 'सप्तमी', '19': 'अष्टमी', '20': 'नवमी',
            '21': 'दशमी', '22': 'एकादशी', '23': 'द्वादशी', '24': 'त्रयोदशी', '25': 'चतुर्दशी',
            '26': 'पूर्णिमा', '27': 'प्रतिपदा', '28': 'द्वितीया', '29': 'तृतीया', '30': 'चतुर्थी', '31': 'पञ्चमी', '32': 'षष्ठी'
          };
        case 4: // Shrawan 2082 (श्रावण २०८२)
          return {
            '1': 'सप्तमी', '2': 'अष्टमी', '3': 'नवमी', '4': 'दशमी', '5': 'एकादशी',
            '6': 'द्वादशी', '7': 'त्रयोदशी', '8': 'औंसी', '9': 'प्रतिपदा', '10': 'द्वितीया',
            '11': 'तृतीया', '12': 'चतुर्थी', '13': 'पञ्चमी', '14': 'षष्ठी', '15': 'सप्तमी',
            '16': 'अष्टमी', '17': 'अष्टमी', '18': 'नवमी', '19': 'दशमी', '20': 'एकादशी',
            '21': 'द्वादशी', '22': 'त्रयोदशी', '23': 'चतुर्दशी', '24': 'पूर्णिमा', '25': 'प्रतिपदा',
            '26': 'द्वितीया', '27': 'तृतीया', '28': 'चतुर्थी', '29': 'पञ्चमी', '30': 'सप्तमी', '31': 'अष्टमी', '32': 'नवमी'
          };
        case 5: // Bhadra 2082 (भाद्र २०८२)
          return {
            '1': 'दशमी', '2': 'एकादशी', '3': 'द्वादशी', '4': 'त्रयोदशी', '5': 'चतुर्दशी',
            '6': 'औंसी', '7': 'प्रतिपदा', '8': 'द्वितीया', '9': 'तृतीया', '10': 'चतुर्थी',
            '11': 'पञ्चमी', '12': 'षष्ठी', '13': 'सप्तमी', '14': 'अष्टमी', '15': 'नवमी',
            '16': 'दशमी', '17': 'एकादशी', '18': 'द्वादशी', '19': 'त्रयोदशी', '20': 'चतुर्दशी',
            '21': 'पूर्णिमा', '22': 'प्रतिपदा', '23': 'द्वितीया', '24': 'तृतीया', '25': 'चतुर्थी',
            '26': 'पञ्चमी', '27': 'षष्ठी', '28': 'सप्तमी', '29': 'अष्टमी', '30': 'नवमी', '31': 'दशमी'
          };
        case 6: // Ashwin 2082 (आश्विन २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'प्रतिपदा', '7': 'द्वितीया', '8': 'तृतीया', '9': 'चतुर्थी', '10': 'चतुर्थी',
            '11': 'पञ्चमी', '12': 'षष्ठी', '13': 'सप्तमी', '14': 'अष्टमी', '15': 'नवमी',
            '16': 'दशमी', '17': 'एकादशी', '18': 'द्वादशी', '19': 'त्रयोदशी', '20': 'चतुर्दशी',
            '21': 'पूर्णिमा', '22': 'द्वितीया', '23': 'तृतीया', '24': 'चतुर्थी', '25': 'पञ्चमी',
            '26': 'षष्ठी', '27': 'सप्तमी', '28': 'अष्टमी', '29': 'नवमी', '30': 'दशमी', '31': 'एकादशी'
          };
        case 7: // Kartik 2082 (कार्तिक २०८२)
          return {
            '1': 'द्वादशी', '2': 'त्रयोदशी', '3': 'चतुर्दशी', '4': 'औंसी', '5': 'प्रतिपदा',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'एकादशी'
          };
        case 8: // Mangsir 2082 (मंसिर २०८२)
          return {
            '1': 'द्वादशी', '2': 'त्रयोदशी', '3': 'चतुर्दशी', '4': 'औंसी', '5': 'प्रतिपदा',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी'
          };
        case 9: // Poush 2082 (पौष २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'प्रतिपदा', '7': 'द्वितीया', '8': 'तृतीया', '9': 'चतुर्थी', '10': 'पञ्चमी',
            '11': 'षष्ठी', '12': 'सप्तमी', '13': 'अष्टमी', '14': 'नवमी', '15': 'दशमी',
            '16': 'एकादशी', '17': 'द्वादशी', '18': 'त्रयोदशी', '19': 'चतुर्दशी', '20': 'पूर्णिमा',
            '21': 'प्रतिपदा', '22': 'द्वितीया', '23': 'तृतीया', '24': 'चतुर्थी', '25': 'पञ्चमी',
            '26': 'षष्ठी', '27': 'सप्तमी', '28': 'अष्टमी', '29': 'नवमी', '30': 'दशमी'
          };
        case 10: // Magh 2082 (माघ २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'प्रतिपदा', '7': 'द्वितीया', '8': 'तृतीया', '9': 'चतुर्थी', '10': 'पञ्चमी',
            '11': 'षष्ठी', '12': 'सप्तमी', '13': 'अष्टमी', '14': 'नवमी', '15': 'दशमी',
            '16': 'एकादशी', '17': 'द्वादशी', '18': 'त्रयोदशी', '19': 'चतुर्दशी', '20': 'पूर्णिमा',
            '21': 'प्रतिपदा', '22': 'द्वितीया', '23': 'तृतीया', '24': 'चतुर्थी', '25': 'पञ्चमी',
            '26': 'षष्ठी', '27': 'सप्तमी', '28': 'अष्टमी', '29': 'नवमी', '30': 'दशमी'
          };
        case 11: // Falgun 2082 (फाल्गुन २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'प्रतिपदा', '7': 'द्वितीया', '8': 'तृतीया', '9': 'चतुर्थी', '10': 'पञ्चमी',
            '11': 'षष्ठी', '12': 'सप्तमी', '13': 'अष्टमी', '14': 'नवमी', '15': 'दशमी',
            '16': 'एकादशी', '17': 'द्वादशी', '18': 'त्रयोदशी', '19': 'चतुर्दशी', '20': 'पूर्णिमा',
            '21': 'प्रतिपदा', '22': 'द्वितीया', '23': 'तृतीया', '24': 'चतुर्थी', '25': 'पञ्चमी',
            '26': 'षष्ठी', '27': 'सप्तमी', '28': 'अष्टमी', '29': 'नवमी', '30': 'दशमी'
          };
        case 12: // Chaitra 2082 (चैत्र २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'एकादशी'
          };
        default:
          return {};
      }
    }
    return {};
  }

  // Build combined Tithi and Festival text with comma separator
  String _buildTithiAndFestivalText() {
    final tithi = _getTithiForDate(nepaliDate.year, nepaliDate.month, nepaliDate.day);
    final dayKey = nepaliDate.day.toString();
    final monthFestivals = _getFestivalsForMonth(nepaliDate.year, nepaliDate.month);

    String result = tithi;

    if (monthFestivals.containsKey(dayKey)) {
      final festivals = monthFestivals[dayKey]!;
      if (festivals.isNotEmpty) {
        final festival = festivals.first;
        result += ', ${festival.title}';
        if (festival.description.isNotEmpty) {
          result += ' - ${festival.description}';
        }
      }
    }

    return result;
  }

  // Get festivals for specific month (correct data from calendar page)
  Map<String, List<CalendarEvent>> _getFestivalsForMonth(int year, int month) {
    if (year == 2082) {
      switch (month) {
        case 1: // Baisakh 2082 (बैशाख २०८२)
          return {
            '1': [CalendarEvent(title: 'नववर्ष २०८२ आरम्भ', description: 'ललितपुर रातो मच्छिन्द्रनाथ स्नान (बुँगद्यः न्हंवः), भक्तपुर विश्वध्वजपातन (विस्काजात्रा), खायू सँल्हू', type: 'festival')],
            '2': [CalendarEvent(title: 'सिरूवा पावनी पर्व', description: 'सिरूवा पर्व मनाउने झापा, मोरङ, सुनसरी, सिराहा र सप्तरी जिल्लाहरुमा बिदा', type: 'festival')],
            '3': [CalendarEvent(title: 'भक्तपुर ब्रम्हायणी यात्रा', description: 'स्वामी शशीधर जयन्ती', type: 'festival')],
            '7': [CalendarEvent(title: 'रविसप्तमी', description: 'अष्टमीव्रत', type: 'festival')],
            '8': [CalendarEvent(title: 'गोरखकाली पूजा', description: 'छन्द दिवस', type: 'festival')],
            '11': [CalendarEvent(title: 'बरूथिनी एकादशी', description: 'लोकतन्त्र दिवस', type: 'festival')],
            '13': [CalendarEvent(title: 'माताति चःह्रे', description: 'राष्ट्रिय फोटो पत्रकारिता दिवस', type: 'festival')],
            '14': [CalendarEvent(title: 'मातातीर्थ औंसी', description: 'आमाको मुख हेर्ने', type: 'festival')],
            '15': [CalendarEvent(title: 'ललितपुर मच्छिन्द्रनाथ रथारोहण', description: '', type: 'festival')],
            '16': [CalendarEvent(title: 'परशुराम जयन्ती', description: '', type: 'festival')],
            '17': [CalendarEvent(title: 'अक्षय तृतीया', description: 'शिवपार्वती विवाह', type: 'festival')],
            '18': [CalendarEvent(title: 'विश्व श्रमिक दिवस', description: 'ललितपुर रातो मच्छिन्द्रनाथ रथ यात्रा आरम्भ', type: 'festival')],
            '19': [CalendarEvent(title: 'आद्यगुरु शङ्कराचार्य जयन्ती', description: '', type: 'festival')],
            '20': [CalendarEvent(title: 'राष्ट्रिय सूचना तथा सञ्चार प्रविधि दिवस', description: '', type: 'festival')],
            '22': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '23': [CalendarEvent(title: 'सीता जयन्ती', description: '', type: 'festival')],
            '24': [CalendarEvent(title: 'किराँत समाजसुधार दिवस', description: 'राष्ट्रिय पत्रकारिता दिवस', type: 'festival')],
            '25': [CalendarEvent(title: 'मोहिनी एकादशी', description: '', type: 'festival')],
            '26': [CalendarEvent(title: 'राष्ट्रिय कानून दिवस', description: '', type: 'festival')],
            '28': [CalendarEvent(title: 'नृसिंह जयन्ती', description: 'स्याङ्जा लसर्घा आलमदेवी पूजा', type: 'festival')],
            '29': [CalendarEvent(title: 'गौतमबुद्ध जयन्ती', description: 'स्वाँया पुन्हिः, चण्डी पूर्णिमा, उभौली पर्व, ललितपुर गोटीखेल बैतरणी धाम स्नान, गोरखनाथ जयन्ती, वैशाख स्नान समाप्ति', type: 'festival')],
          };
        case 2: // Jestha 2082 (जेष्ठ २०८२)
          return {
            '6': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '9': [CalendarEvent(title: 'अपरा एकादशी', description: '', type: 'festival')],
            '11': [CalendarEvent(title: 'सिथिःचःह्रे', description: '', type: 'festival')],
            '13': [CalendarEvent(title: 'शनि जयन्ती', description: '', type: 'festival')],
            '14': [CalendarEvent(title: 'गोसाइँकुण्ड स्नान आरम्भ', description: '', type: 'festival')],
            '15': [CalendarEvent(title: 'गणतन्त्र दिवस', description: '', type: 'festival')],
            '18': [CalendarEvent(title: 'कुमार षष्ठी', description: 'सिथीः नख, भक्तपुर चण्डी भगवती यात्रा', type: 'festival')],
            '19': [CalendarEvent(title: 'कुमार यात्रा', description: '', type: 'festival')],
            '20': [CalendarEvent(title: 'वायु अष्टमी', description: 'गोरखकाली पूजा', type: 'festival')],
            '21': [CalendarEvent(title: 'जातीय भेदभाव तथा छुवाछुत उन्मूलन राष्ट्रिय दिवस', description: '', type: 'festival')],
            '22': [CalendarEvent(title: 'गोसाइँकुण्ड स्नान समाप्ति', description: 'वैतडी विश्वनाथ मन्दिरमा गङ्गादशहरा स्नानमेला', type: 'festival')],
            '24': [CalendarEvent(title: 'निर्जला एकादशी', description: 'तुलसीको दल राख्ने, ईद–उल–अज्हा (बकर ईद)', type: 'festival')],
            '27': [CalendarEvent(title: 'पूर्णिमाव्रत', description: '', type: 'festival')],
            '28': [CalendarEvent(title: 'मष्टपूर्णिमा (ज्याःपुन्हिः)', description: 'पनौती स्नान, पनौती रथ यात्रा, कवीर जयन्ती', type: 'festival')],
            '31': [CalendarEvent(title: 'विश्व रक्तदाता दिवस', description: 'ललितपुर रातो मच्छिन्द्रनाथको भोटो देखाउने जात्राको दि', type: 'festival')],
          };
        case 3: // Ashar 2082 (असार २०८२)
          return {
            '4': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '5': [CalendarEvent(title: 'भलभल अष्टमी', description: 'देउपाटनमा त्रिशुल जात्रा', type: 'festival')],
            '7': [CalendarEvent(title: 'स्मार्तहरूको योगिनी एकादशीव्रत', description: '', type: 'festival')],
            '8': [CalendarEvent(title: 'वैष्णवहरुको योगिनी एकादशीव्रत', description: 'भूमिरज', type: 'festival')],
            '10': [CalendarEvent(title: 'दिलाचःह्रे:', description: '', type: 'festival')],
            '11': [CalendarEvent(title: 'दर्शश्राद्ध', description: 'भूमिपूजा', type: 'festival')],
            '13': [CalendarEvent(title: 'जगन्नाथ रथयात्रा', description: '', type: 'festival')],
            '15': [CalendarEvent(title: 'दहिचिउरा खाने दिन', description: 'राष्ट्रिय धान दिवस', type: 'festival')],
            '19': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '22': [CalendarEvent(title: 'हरिशयनी एकादशीव्रत', description: 'तुलसी रोप्ने, चतुर्मासव्रत आरम्भ', type: 'festival')],
            '26': [CalendarEvent(title: 'गुरू पूर्णिमा', description: 'पूर्णिमाव्रत, दिला पुन्हिः, व्यास जयन्ती', type: 'festival')],
            '27': [CalendarEvent(title: 'विश्व जनसंख्या दिवस', description: '', type: 'festival')],
            '29': [CalendarEvent(title: 'भानु जयन्ती', description: '', type: 'festival')],
          };
        case 4: // Shrawan 2082 (श्रावण २०८२)
          return {
            '1': [CalendarEvent(title: 'साउन संक्रान्ती', description: 'लुतो फाल्ने एवं राँको बाल्ने, थारू गुरिया पर्व', type: 'festival')],
            '2': [CalendarEvent(title: 'गोरखकाली पूजा', description: '', type: 'festival')],
            '5': [CalendarEvent(title: 'कामिका एकादशी', description: '', type: 'festival')],
            '7': [CalendarEvent(title: 'घण्टाकर्ण चतुर्दशी', description: 'गठाँमुगचह्नेः', type: 'festival')],
            '9': [CalendarEvent(title: 'गुँलाधर्म आरम्भ', description: '', type: 'festival')],
            '10': [CalendarEvent(title: 'चन्द्रोदय', description: '', type: 'festival')],
            '11': [CalendarEvent(title: 'वराह जयन्ती', description: '', type: 'festival')],
            '13': [CalendarEvent(title: 'नाग पञ्चमी', description: '', type: 'festival')],
            '14': [CalendarEvent(title: 'कल्की जयन्ती', description: '', type: 'festival')],
            '15': [CalendarEvent(title: 'खिर खाने दिन', description: 'गोस्वामी तुलसीदास जयन्ती', type: 'festival')],
            '16': [CalendarEvent(title: 'अष्टमीव्रत', description: 'यल पञ्चदान, गोरखकाली पूजा', type: 'festival')],
            '20': [CalendarEvent(title: 'पुत्रदा एकादशी', description: '', type: 'festival')],
            '23': [CalendarEvent(title: 'पूर्णिमाव्रत', description: 'बाजुरा बडिमालिका मेला', type: 'festival')],
            '24': [CalendarEvent(title: 'जनैपूर्णिमा', description: 'ऋषितर्पणी, रक्षाबन्धन, गुँःपुन्हिः', type: 'festival')],
            '25': [CalendarEvent(title: 'गाईजात्रा (सापारू)', description: 'काठमाडौं उपत्यकालाई र देशभरका नेवार समुदायका लागि मात्र बिदा', type: 'festival')],
            '26': [CalendarEvent(title: 'रोपाइँ जात्रा', description: 'यल मत्या', type: 'festival')],
            '29': [CalendarEvent(title: 'ललितपुर नृसिंह यात्रा', description: '', type: 'festival')],
            '31': [CalendarEvent(title: 'श्रीकृष्णजन्माष्टमी', description: '', type: 'festival')],
          };
        case 5: // Bhadra 2082 (भाद्र २०८२)
          return {
            '3': [CalendarEvent(title: 'अजा एकादशीव्रत', description: 'राष्ट्रिय सूचना दिवस', type: 'festival')],
            '4': [CalendarEvent(title: 'स्वयम्भूको छायाँ दर्शन', description: '', type: 'festival')],
            '5': [CalendarEvent(title: 'यें पञ्चदान', description: 'जुगःच:ह्रे पुजा', type: 'festival')],
            '7': [CalendarEvent(title: 'कुशे औँसी', description: 'बाबुको मुख हेर्ने दिन', type: 'festival')],
            '8': [CalendarEvent(title: 'गुलांँधर्म समाप्ति', description: '', type: 'festival')],
            '9': [CalendarEvent(title: 'दरखाने दिन', description: '', type: 'festival')],
            '10': [CalendarEvent(title: 'हरितालिका व्रत', description: 'तीज (महिला कर्मचारीहरूको लागि मात्र बिदा)', type: 'festival')],
            '11': [CalendarEvent(title: 'गणेश चतुर्थी', description: '', type: 'festival')],
            '12': [CalendarEvent(title: 'ऋषिपञ्चमी', description: 'विरूडा पञ्चमी, सप्तऋषि पूजा', type: 'festival')],
            '14': [CalendarEvent(title: 'गौरा सप्तमी', description: '', type: 'festival')],
            '15': [CalendarEvent(title: 'गौरा पर्व', description: 'गौरा पर्व मनाउने कर्मचारीहरुका लागि मात्र बिदा, काय अष्टमी, कागेश्वर मेला, गोरखकाली पूजा, दूर्वाष्टमी', type: 'festival')],
            '18': [CalendarEvent(title: 'हरिपरिवर्तनी एकादशी', description: '', type: 'festival')],
            '19': [CalendarEvent(title: 'इन्द्रध्वजोत्थान', description: 'वामन द्वादशी, उपाकु, मोहम्मद जयन्ती (नेपाली मुस्लिम धर्मावलम्बीहरूको लागि मात्र बिदा)', type: 'festival')],
            '20': [CalendarEvent(title: 'मानव वेचविखन विरुद्धको राष्ट्रिय दिवस', description: '', type: 'festival')],
            '21': [CalendarEvent(title: 'ईन्द्रजात्रा', description: 'काठमाडौं उपत्यका मात्र विदा', type: 'festival')],
            '22': [CalendarEvent(title: 'यैँया पुन्हिः', description: 'चेपाङ चोनाम पर्व, निजामती सेवा दिवस', type: 'festival')],
            '23': [CalendarEvent(title: 'सोह्रश्राद्ध आरम्भ', description: 'प्रतिपदा श्राद्ध', type: 'festival')],
            '24': [CalendarEvent(title: 'द्वितीया श्राद्ध', description: '', type: 'festival')],
            '25': [CalendarEvent(title: 'तृतीया श्राद्ध', description: '', type: 'festival')],
            '26': [CalendarEvent(title: 'चतुर्थी श्राद्ध', description: 'इन्द्रध्वजपातन, नानिचाया', type: 'festival')],
            '27': [CalendarEvent(title: 'पञ्चमी श्राद्ध', description: 'षष्ठी श्राद्ध', type: 'festival')],
            '28': [CalendarEvent(title: 'सप्तमी श्राद्ध', description: '', type: 'festival')],
            '29': [CalendarEvent(title: 'रवि सप्तमी', description: 'अष्टमीव्रत, अष्टमी श्राद्ध, गोरखकाली पूजा, बाल दिवस', type: 'festival')],
            '30': [CalendarEvent(title: 'जितिया पर्व', description: 'जितिया पर्व मनाउने महिला कर्मचारीहरुका लागि मात्र बिदा, नवमी श्राद्ध', type: 'festival')],
            '31': [CalendarEvent(title: 'दशमी श्राद्ध', description: '', type: 'festival')],
          };
        case 6: // Ashwin 2082 (आश्विन २०८२)
          return {
            '1': [CalendarEvent(title: 'एकादशी श्राद्ध', description: 'इन्दिरा एकादशीव्रत, विश्वकर्मा पूजा, वास्तु दिवस', type: 'festival')],
            '2': [CalendarEvent(title: 'द्वादशी श्राद्ध', description: '', type: 'festival')],
            '3': [CalendarEvent(title: 'त्रयोदशी श्राद्ध', description: 'संविधान दिवस (राष्ट्रिय दिवस), जुम्ला खलङ्गामा चन्दननाथको लिङ्गो ठड्याउने', type: 'festival')],
            '4': [CalendarEvent(title: 'चतुर्दशी श्राद्ध', description: '', type: 'festival')],
            '5': [CalendarEvent(title: 'औंसी श्राद्ध', description: 'दर्शश्राद्ध, पितृ विसर्जन (सोह्र श्राद्ध समाप्ति)', type: 'festival')],
            '6': [CalendarEvent(title: 'घटस्थापना', description: 'नवरात्र आरम्भ, मातामह श्राद्ध', type: 'festival')],
            '11': [CalendarEvent(title: 'पचली भैरव यात्रा', description: '', type: 'festival')],
            '13': [CalendarEvent(title: 'फूलपाती', description: 'नवपत्रिका प्रवेश', type: 'festival')],
            '14': [CalendarEvent(title: 'महाअष्टमी', description: 'कालरात्रि, कुछि भ्वय्', type: 'festival')],
            '15': [CalendarEvent(title: 'महा नवमी', description: 'स्याक्वःत्याक्वः', type: 'festival')],
            '16': [CalendarEvent(title: 'विजयादशमी', description: 'दर्शैको टीका २०८२, देवीविसर्जन', type: 'festival')],
            '17': [CalendarEvent(title: 'पापाङकुशा एकादशीव्रत', description: 'अन्नपूर्णा यात्रा, असँः चालँः', type: 'festival')],
            '18': [CalendarEvent(title: 'द्वादशी', description: '', type: 'festival')],
            '20': [CalendarEvent(title: 'कोजाग्रतव्रत', description: '', type: 'festival')],
            '21': [CalendarEvent(title: 'कोजाग्रत पूर्णिमा', description: 'कतिं पुन्हिः, कात्तिकस्नान, आकाशदीपदान आरम्भ', type: 'festival')],
            '23': [CalendarEvent(title: 'हुलाक दिवस', description: '', type: 'festival')],
            '28': [CalendarEvent(title: 'राधा अष्टमी', description: 'भौमाष्टमी, गोरखकाली पूजा', type: 'festival')],
            '30': [CalendarEvent(title: 'खाद्य दिवस', description: '', type: 'festival')],
            '31': [CalendarEvent(title: 'रमा एकादशी', description: '', type: 'festival')],
          };
        case 7: // Kartik 2082 (कार्तिक २०८२)
          return {
            '1': [CalendarEvent(title: 'धन्तेरस', description: 'यमदीप दान', type: 'festival')],
            '2': [CalendarEvent(title: 'काग तिहार', description: 'धन्वन्तरी जयन्ती', type: 'festival')],
            '3': [CalendarEvent(title: 'कुकुर तिहार', description: 'नरक चतुर्दशी, लक्ष्मी पूजा, दीपमालिका', type: 'festival')],
            '4': [CalendarEvent(title: 'औंसी', description: 'दर्श श्राद्ध', type: 'festival')],
            '5': [CalendarEvent(title: 'गाई पूजा', description: 'गोरू पूजा, गोवर्द्धन पूजा, म्हःपूजा', type: 'festival')],
            '6': [CalendarEvent(title: 'भाइटीका', description: 'किजा पूजा, यम द्वितीया', type: 'festival')],
            '7': [CalendarEvent(title: 'तृतीया', description: '', type: 'festival')],
            '10': [CalendarEvent(title: 'छठ पर्व', description: 'डाला पूजा', type: 'festival')],
            '12': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '13': [CalendarEvent(title: 'कुष्माण्ड नवमी', description: '', type: 'festival')],
            '14': [CalendarEvent(title: 'बलम्वु महालक्ष्मी यात्रा', description: '', type: 'festival')],
            '15': [CalendarEvent(title: 'हरिबोधिनी एकादशी', description: 'तुलसी विवाह', type: 'festival')],
            '16': [CalendarEvent(title: 'चाँगुनारायण अखण्डदीप दर्शन', description: '', type: 'festival')],
            '18': [CalendarEvent(title: 'वैकुण्ठ चतुर्दशी', description: '', type: 'festival')],
            '19': [CalendarEvent(title: 'गुरूनानक जयन्ती', description: 'शिख धर्मावलम्बी कर्मचारीहरुका लागि मात्र बिदा, पूर्णिमाव्रत, चतुर्मासव्रत समाप्ति, सकिमना पुन्हिः', type: 'festival')],
            '25': [CalendarEvent(title: 'फाल्गुनन्द जयन्ती', description: 'किरात धर्मावलम्बीहरूका लागि मात्र बिदा', type: 'festival')],
            '26': [CalendarEvent(title: 'बुधाष्टमीव्रत', description: 'भैरवाष्टमी, गोरखकाली पूजा', type: 'festival')],
            '28': [CalendarEvent(title: 'श्री गुहेश्वरी यात्रा', description: '', type: 'festival')],
            '29': [CalendarEvent(title: 'उत्पतिका एकादशी', description: '', type: 'festival')],
          };
        case 8: // Mangsir 2082 (मंसिर २०८२)
          return {
            '2': [CalendarEvent(title: 'पशुपतिनाथ मेला', description: '', type: 'festival')],
            '3': [CalendarEvent(title: 'बाला चतुदर्शी', description: 'शतबीज छर्ने', type: 'festival')],
            '5': [CalendarEvent(title: 'टेलिभिजन दिवस', description: '', type: 'festival')],
            '9': [CalendarEvent(title: 'विवाह पञ्चमी', description: 'जनकपुरमा सीता विवाह पञ्चमी मेला, ब्रम्हचारी षडानन्द जयन्ती', type: 'festival')],
            '12': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '15': [CalendarEvent(title: 'मोक्षदा एकादशी', description: '', type: 'festival')],
            '17': [CalendarEvent(title: 'विश्व अपाङ्ग दिवस', description: 'अपाङ्गता भएका कर्मचारीहरूका लागि बिदा', type: 'festival')],
            '18': [CalendarEvent(title: 'यमरीपुन्हीः', description: 'उधौलीपर्व, धान्यपूर्णिमा, ज्यापु दिवस', type: 'festival')],
            '24': [CalendarEvent(title: 'मानव अधिकार दिवस', description: '', type: 'festival')],
            '26': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '29': [CalendarEvent(title: 'सफला एकादशी', description: '', type: 'festival')],
          };
        case 9: // Poush 2082 (पौष २०८२)
          return {
            '5': [CalendarEvent(title: 'तोल ल्होसार', description: '', type: 'festival')],
            '10': [CalendarEvent(title: 'क्रिसमस डे', description: '', type: 'festival')],
            '13': [CalendarEvent(title: 'अष्टमीव्रत', description: 'सेतो मच्छिन्द्रनाथ स्नान, जनवहाद्यः न्हवंः, गोरखकाली पूजा', type: 'festival')],
            '15': [CalendarEvent(title: 'पुत्रदा एकादशी', description: 'तमुल्होछार', type: 'festival')],
            '17': [CalendarEvent(title: 'सन् २०२६ आरम्भ', description: 'राष्ट्रिय टोपी दिवस', type: 'festival')],
            '19': [CalendarEvent(title: 'श्रीस्वस्थानीव्रत आरम्भ', description: 'मिला पुन्हिः, माघस्नान सुरू', type: 'festival')],
            '21': [CalendarEvent(title: 'गुरु गोविन्दसिंह जयन्ती', description: '', type: 'festival')],
            '23': [CalendarEvent(title: 'अरनिको स्मृति दिवस', description: '', type: 'festival')],
            '24': [CalendarEvent(title: 'नेपाल ज्योतिष परिषद् स्थापना दिवस', description: '', type: 'festival')],
            '26': [CalendarEvent(title: 'अष्टमीव्रत', description: '', type: 'festival')],
            '27': [CalendarEvent(title: 'पृथ्वी जयन्ती', description: 'राष्ट्रिय एकता दिवस', type: 'festival')],
            '30': [CalendarEvent(title: 'षट्तिला एकादशी', description: '', type: 'festival')],
          };
        case 10: // Magh 2082 (माघ २०८२)
          return {
            '1': [CalendarEvent(title: 'माघे संक्रान्ति', description: 'माघी पर्व, घ्य:चाकु संल्हु, राष्ट्रिय कृषि जैविक विविधता दिवस', type: 'festival')],
            '2': [CalendarEvent(title: 'राष्ट्रिय भुकम्प सुरक्षा दिवस', description: '', type: 'festival')],
            '4': [CalendarEvent(title: 'पशुपति क्षेत्रमा माधव नारायण मेला', description: 'त्रिवेणी मेला', type: 'festival')],
            '5': [CalendarEvent(title: 'सोनाम ल्होसार', description: 'तामाङ ल्होछार, श्रीवल्लभ जयन्ती', type: 'festival')],
            '9': [CalendarEvent(title: 'वसन्तपञ्यमी', description: 'वसन्तश्रवण श्रीपञ्यमी सरस्वती पूजा (शिक्षण संस्थाहरूका लागि मात्र विदा)', type: 'festival')],
            '11': [CalendarEvent(title: 'रवि सप्तमी', description: 'अचला सप्तमी', type: 'festival')],
            '12': [CalendarEvent(title: 'अष्टमीव्रत', description: 'भिष्माष्टमी', type: 'festival')],
            '14': [CalendarEvent(title: 'सम्पत्ति शुद्धीकरण निवारण राष्ट्रिय दिवस', description: '', type: 'festival')],
            '15': [CalendarEvent(title: 'जया एकादशी', description: '', type: 'festival')],
            '16': [CalendarEvent(title: 'सहिद दिवस', description: 'चाँगुमा माधव नारायण मेला', type: 'festival')],
            '17': [CalendarEvent(title: 'श्री पशुपतिनाथमा छायाँ दर्शन', description: '', type: 'festival')],
            '18': [CalendarEvent(title: 'श्रीस्वस्थानीव्रत समाप्ति', description: 'सि पुन्हिः, माघस्नान समाप्ति', type: 'festival')],
            '25': [CalendarEvent(title: 'भानु सप्तमी', description: '', type: 'festival')],
            '26': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
          };
        case 11: // Falgun 2082 (फाल्गुन २०८२)
          return {
            '1': [CalendarEvent(title: 'विजया एकादशी व्रत', description: 'रेडियो दिवस', type: 'festival')],
            '2': [CalendarEvent(title: 'भ्यालेन्टाइन डे', description: '', type: 'festival')],
            '3': [CalendarEvent(title: 'महाशिवरात्रि', description: 'सिलाचह्रे:, नेपाली सेना दिवस', type: 'festival')],
            '6': [CalendarEvent(title: 'ग्याल्पो ल्होसार', description: '', type: 'festival')],
            '7': [CalendarEvent(title: 'प्रजातन्त्र दिवस', description: 'निर्वाचन दिवस', type: 'festival')],
            '12': [CalendarEvent(title: 'भौमाष्टमीव्रत', description: 'चिरोत्थान, होलिकारम्भ, चिरस्वायगु', type: 'festival')],
            '15': [CalendarEvent(title: 'आमलकी एकादशी', description: '', type: 'festival')],
            '18': [CalendarEvent(title: 'होली', description: 'चीरदाह, पूर्णिमाव्रत (हिमाली, पहाडी तथा भित्री मधेशका ५६ जिल्लाहरूमा बिदा)', type: 'festival')],
            '19': [CalendarEvent(title: 'फागु पूर्णिमा (होलीपुन्हीः)', description: 'तराइमा होली (तराईका २१ जिल्लाहरूमा बिदा)', type: 'festival')],
            '20': [CalendarEvent(title: 'नाला मच्छिन्द्रनाथ स्नान', description: 'नाला न्हवं', type: 'festival')],
            '22': [CalendarEvent(title: 'नाला मच्छिन्द्रनाथ रथयात्रा', description: '', type: 'festival')],
            '24': [CalendarEvent(title: 'अन्तर्राष्ट्रिय महिला दिवस', description: '', type: 'festival')],
            '27': [CalendarEvent(title: 'शीतलाष्टमी', description: 'बुधाष्टमीव्रत, गोरखकाली पूजा', type: 'festival')],
          };
        case 12: // Chaitra 2082 (चैत्र २०८२)
          return {
            '1': [CalendarEvent(title: 'पापमोचिनी एकादशी', description: '', type: 'festival')],
            '3': [CalendarEvent(title: 'पाहाँ (पासा) चह्रे:', description: '', type: 'festival')],
            '4': [CalendarEvent(title: 'घोडेजात्रा', description: 'काठमाडौं उपत्यकालाई मात्र विदा', type: 'festival')],
            '5': [CalendarEvent(title: 'चोभार आदिनाथ स्नान', description: 'चोभाद्यः न्हवं', type: 'festival')],
            '7': [CalendarEvent(title: 'मत्स्येनारायण मेला', description: 'मत्स्येजयन्ती, ईद-उल-फित्रको दिन (चैत ७ गते वा सो पर्व पर्ने दिन) बिदा', type: 'festival')],
            '10': [CalendarEvent(title: 'चैती छठ', description: '', type: 'festival')],
            '12': [CalendarEvent(title: 'चैते दशैं', description: 'सेतो मच्छिन्द्रनाथ रथयात्रा आरम्भ, चैत्राष्टमी', type: 'festival')],
            '13': [CalendarEvent(title: 'राम नवमी', description: 'श्रीराम जयन्ती', type: 'festival')],
            '15': [CalendarEvent(title: 'कामदा एकादशी', description: '', type: 'festival')],
            '17': [CalendarEvent(title: 'महावीर जयन्ती', description: '', type: 'festival')],
            '18': [CalendarEvent(title: 'पूर्णिमाव्रत', description: '', type: 'festival')],
            '19': [CalendarEvent(title: 'ल्हुतिपुन्हीः', description: 'वैशाख स्नान सुरू, श्री हनुमान जयन्ती, बालाजु बाइसधारा मेला', type: 'festival')],
            '22': [CalendarEvent(title: 'स्वामी शशिधर जयन्ती', description: '', type: 'festival')],
            '27': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', type: 'festival')],
            '30': [CalendarEvent(title: 'बरूथिनी एकादशीव्रत', description: 'भक्तपुर विश्वध्वजोत्थान', type: 'festival')],
          };
        default:
          return {};
      }
    }
    return {};
  }

}

// Simple CalendarEvent class for homepage
class CalendarEvent {
  final String title;
  final String description;
  final String type;

  CalendarEvent({
    required this.title,
    required this.description,
    required this.type,
  });
}
