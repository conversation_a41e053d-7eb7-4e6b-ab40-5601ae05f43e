class BhagavadGitaChapter {
  final int chapterNumber;
  final String titleNepali;
  final String titleEnglish;
  final String titleSanskrit;
  final String description;
  final int verseCount;
  final String youtubeVideoId;
  final String thumbnailUrl;
  final String duration;
  final String summary;

  BhagavadGitaChapter({
    required this.chapterNumber,
    required this.titleNepali,
    required this.titleEnglish,
    required this.titleSanskrit,
    required this.description,
    required this.verseCount,
    required this.youtubeVideoId,
    required this.thumbnailUrl,
    required this.duration,
    required this.summary,
  });

  factory BhagavadGitaChapter.fromJson(Map<String, dynamic> json) {
    return BhagavadGitaChapter(
      chapterNumber: json['chapterNumber'] ?? 0,
      titleNepali: json['titleNepali'] ?? '',
      titleEnglish: json['titleEnglish'] ?? '',
      titleSanskrit: json['titleSanskrit'] ?? '',
      description: json['description'] ?? '',
      verseCount: json['verseCount'] ?? 0,
      youtubeVideoId: json['youtubeVideoId'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      duration: json['duration'] ?? '',
      summary: json['summary'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'chapterNumber': chapterNumber,
      'titleNepali': titleNepali,
      'titleEnglish': titleEnglish,
      'titleSanskrit': titleSanskrit,
      'description': description,
      'verseCount': verseCount,
      'youtubeVideoId': youtubeVideoId,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration,
      'summary': summary,
    };
  }
}

class BhagavadGitaVerse {
  final int chapterNumber;
  final int verseNumber;
  final String sanskrit;
  final String transliteration;
  final String translationNepali;
  final String translationEnglish;
  final String commentary;

  BhagavadGitaVerse({
    required this.chapterNumber,
    required this.verseNumber,
    required this.sanskrit,
    required this.transliteration,
    required this.translationNepali,
    required this.translationEnglish,
    required this.commentary,
  });

  factory BhagavadGitaVerse.fromJson(Map<String, dynamic> json) {
    return BhagavadGitaVerse(
      chapterNumber: json['chapterNumber'] ?? 0,
      verseNumber: json['verseNumber'] ?? 0,
      sanskrit: json['sanskrit'] ?? '',
      transliteration: json['transliteration'] ?? '',
      translationNepali: json['translationNepali'] ?? '',
      translationEnglish: json['translationEnglish'] ?? '',
      commentary: json['commentary'] ?? '',
    );
  }
}

class PlaylistInfo {
  final String playlistId;
  final String title;
  final String description;
  final int videoCount;
  final String thumbnailUrl;

  PlaylistInfo({
    required this.playlistId,
    required this.title,
    required this.description,
    required this.videoCount,
    required this.thumbnailUrl,
  });
}
