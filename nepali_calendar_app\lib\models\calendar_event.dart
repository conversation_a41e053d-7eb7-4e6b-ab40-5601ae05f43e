class CalendarEvent {
  final String title;
  final String description;
  final int nepaliYear;
  final int nepaliMonth;
  final int nepaliDay;
  final String type;
  final String color;

  CalendarEvent({
    required this.title,
    required this.description,
    required this.nepaliYear,
    required this.nepaliMonth,
    required this.nepaliDay,
    required this.type,
    required this.color,
  });

  factory CalendarEvent.fromMap(Map<String, dynamic> map) {
    return CalendarEvent(
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      nepaliYear: map['nepaliYear'] ?? 0,
      nepaliMonth: map['nepaliMonth'] ?? 0,
      nepaliDay: map['nepaliDay'] ?? 0,
      type: map['type'] ?? '',
      color: map['color'] ?? '#FF5722',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'nepaliYear': nepaliYear,
      'nepaliMonth': nepaliMonth,
      'nepaliDay': nepaliDay,
      'type': type,
      'color': color,
    };
  }
}
