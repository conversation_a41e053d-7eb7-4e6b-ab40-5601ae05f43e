class DictionaryEntry {
  final String word;
  final String phonetic;
  final List<DictionaryMeaning> meanings;
  final List<String> phonetics;
  final String? sourceUrl;

  DictionaryEntry({
    required this.word,
    required this.phonetic,
    required this.meanings,
    required this.phonetics,
    this.sourceUrl,
  });

  factory DictionaryEntry.fromJson(Map<String, dynamic> json) {
    return DictionaryEntry(
      word: json['word'] ?? '',
      phonetic: json['phonetic'] ?? '',
      meanings: (json['meanings'] as List<dynamic>?)
          ?.map((meaning) => DictionaryMeaning.fromJson(meaning))
          .toList() ?? [],
      phonetics: (json['phonetics'] as List<dynamic>?)
          ?.map((phonetic) => phonetic['text'] ?? '')
          .where((text) => text.isNotEmpty)
          .cast<String>()
          .toList() ?? [],
      sourceUrl: (json['sourceUrls'] as List<dynamic>?)?.first,
    );
  }
}

class DictionaryMeaning {
  final String partOfSpeech;
  final List<DictionaryDefinition> definitions;
  final List<String> synonyms;
  final List<String> antonyms;

  DictionaryMeaning({
    required this.partOfSpeech,
    required this.definitions,
    required this.synonyms,
    required this.antonyms,
  });

  factory DictionaryMeaning.fromJson(Map<String, dynamic> json) {
    return DictionaryMeaning(
      partOfSpeech: json['partOfSpeech'] ?? '',
      definitions: (json['definitions'] as List<dynamic>?)
          ?.map((def) => DictionaryDefinition.fromJson(def))
          .toList() ?? [],
      synonyms: (json['synonyms'] as List<dynamic>?)
          ?.cast<String>()
          .toList() ?? [],
      antonyms: (json['antonyms'] as List<dynamic>?)
          ?.cast<String>()
          .toList() ?? [],
    );
  }
}

class DictionaryDefinition {
  final String definition;
  final String? example;
  final List<String> synonyms;
  final List<String> antonyms;

  DictionaryDefinition({
    required this.definition,
    this.example,
    required this.synonyms,
    required this.antonyms,
  });

  factory DictionaryDefinition.fromJson(Map<String, dynamic> json) {
    return DictionaryDefinition(
      definition: json['definition'] ?? '',
      example: json['example'],
      synonyms: (json['synonyms'] as List<dynamic>?)
          ?.cast<String>()
          .toList() ?? [],
      antonyms: (json['antonyms'] as List<dynamic>?)
          ?.cast<String>()
          .toList() ?? [],
    );
  }
}

class DictionarySearchHistory {
  final String word;
  final DateTime searchTime;

  DictionarySearchHistory({
    required this.word,
    required this.searchTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'word': word,
      'searchTime': searchTime.toIso8601String(),
    };
  }

  factory DictionarySearchHistory.fromJson(Map<String, dynamic> json) {
    return DictionarySearchHistory(
      word: json['word'] ?? '',
      searchTime: DateTime.parse(json['searchTime']),
    );
  }
}
