class ForexResponse {
  final Status status;
  final Data data;
  final Pagination pagination;

  ForexResponse({
    required this.status,
    required this.data,
    required this.pagination,
  });

  factory ForexResponse.fromJson(Map<String, dynamic> json) {
    return ForexResponse(
      status: Status.fromJson(json['status']),
      data: Data.fromJson(json['data']),
      pagination: Pagination.fromJson(json['pagination']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.toJson(),
      'data': data.toJson(),
      'pagination': pagination.toJson(),
    };
  }
}

class Status {
  final int code;

  Status({required this.code});

  factory Status.fromJson(Map<String, dynamic> json) {
    return Status(code: json['code']);
  }

  Map<String, dynamic> toJson() {
    return {'code': code};
  }
}

class Data {
  final List<ForexData>? payload;

  Data({this.payload});

  factory Data.fromJson(Map<String, dynamic> json) {
    return Data(
      payload: json['payload'] != null
          ? (json['payload'] as List)
              .map((item) => ForexData.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'payload': payload?.map((item) => item.toJson()).toList(),
    };
  }
}

class ForexData {
  final String date;
  final String publishedOn;
  final String modifiedOn;
  final List<CurrencyRate> rates;

  ForexData({
    required this.date,
    required this.publishedOn,
    required this.modifiedOn,
    required this.rates,
  });

  factory ForexData.fromJson(Map<String, dynamic> json) {
    return ForexData(
      date: json['date'],
      publishedOn: json['published_on'],
      modifiedOn: json['modified_on'],
      rates: (json['rates'] as List)
          .map((rate) => CurrencyRate.fromJson(rate))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'published_on': publishedOn,
      'modified_on': modifiedOn,
      'rates': rates.map((rate) => rate.toJson()).toList(),
    };
  }
}

class CurrencyRate {
  final Currency currency;
  final String buy;
  final String sell;

  CurrencyRate({
    required this.currency,
    required this.buy,
    required this.sell,
  });

  factory CurrencyRate.fromJson(Map<String, dynamic> json) {
    return CurrencyRate(
      currency: Currency.fromJson(json['currency']),
      buy: json['buy'],
      sell: json['sell'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currency': currency.toJson(),
      'buy': buy,
      'sell': sell,
    };
  }
}

class Currency {
  final String iso3;
  final String name;
  final int unit;

  Currency({
    required this.iso3,
    required this.name,
    required this.unit,
  });

  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      iso3: json['iso3'],
      name: json['name'],
      unit: json['unit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'iso3': iso3,
      'name': name,
      'unit': unit,
    };
  }
}

class Pagination {
  final int? page;
  final int? pages;
  final int? perPage;
  final int? total;

  Pagination({
    this.page,
    this.pages,
    this.perPage,
    this.total,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
      page: json['page'],
      pages: json['pages'],
      perPage: json['per_page'],
      total: json['total'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'pages': pages,
      'per_page': perPage,
      'total': total,
    };
  }
}

// Popular currencies for quick display
class PopularCurrency {
  final String iso3;
  final String name;
  final String flag;
  final String symbol;

  PopularCurrency({
    required this.iso3,
    required this.name,
    required this.flag,
    required this.symbol,
  });

  static List<PopularCurrency> getPopularCurrencies() {
    return [
      PopularCurrency(iso3: 'USD', name: 'US Dollar', flag: '🇺🇸', symbol: '\$'),
      PopularCurrency(iso3: 'EUR', name: 'Euro', flag: '🇪🇺', symbol: '€'),
      PopularCurrency(iso3: 'GBP', name: 'British Pound', flag: '🇬🇧', symbol: '£'),
      PopularCurrency(iso3: 'JPY', name: 'Japanese Yen', flag: '🇯🇵', symbol: '¥'),
      PopularCurrency(iso3: 'INR', name: 'Indian Rupee', flag: '🇮🇳', symbol: '₹'),
      PopularCurrency(iso3: 'CNY', name: 'Chinese Yuan', flag: '🇨🇳', symbol: '¥'),
      PopularCurrency(iso3: 'AUD', name: 'Australian Dollar', flag: '🇦🇺', symbol: 'A\$'),
      PopularCurrency(iso3: 'CAD', name: 'Canadian Dollar', flag: '🇨🇦', symbol: 'C\$'),
    ];
  }
}
