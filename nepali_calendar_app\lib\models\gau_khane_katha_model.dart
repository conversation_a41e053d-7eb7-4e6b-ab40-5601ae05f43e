import 'dart:math';

class <PERSON>au<PERSON><PERSON><PERSON><PERSON><PERSON> {
  final String question;
  final String answer;

  GauK<PERSON><PERSON><PERSON><PERSON>({required this.question, required this.answer});
}

class GauKhaneKathaGame {
  static final List<GauKhaneKatha> _allKathas = [
    G<PERSON><PERSON><PERSON><PERSON><PERSON>a(question: "चादीको थेकी सेउलाको बिर्को, के हो?", answer: "मुला"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(question: "मन चोर्ने मायालु राती राती आउछिन्, उज्यालो हुन नपाई कुन्नी कता जान्छिन्, के हो?", answer: "निद्रा"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(question: "लामो लामो लहरमा फल्ने मिठो फल, मुख भित्र हाल्दा हुन्छ जल नै जल, के हो?", answer: "अंगुर"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(question: "आउछ जान्छ फर्कदैन, कसैलाई पर्खदैन, के हो?", answer: "समय"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(question: "चादीको थेकी, सुनको खोल, के हो?", answer: "केरा"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(question: "खुट्टा नहुँदा जमिनमा हिड्न पाएन, पखेटा नहुँदा आकाशमा उड्न पाएन, के हो?", answer: "माछो"),
    <PERSON><PERSON><PERSON><PERSON>(question: "गहिरो पोखरीमा चादीको थाल, के हो?", answer: "चन्द्रमा"),
    GauKhaneKatha(question: "कालो ओधारमा लहराई जान्ती", answer: "कमिलाको ताती"),
    GauKhaneKatha(question: "एक जना मान्छेको जिउ भरी प्वाल, के हो?", answer: "डोको"),
    GauKhaneKatha(question: "एक सिंघे गाई, जति खुवायो उति खाई, के हो?", answer: "जाँतो"),
    GauKhaneKatha(question: "आमा भन्दा छोरी कुप्री, के हो?", answer: "निउरो"),
    GauKhaneKatha(question: "एउटा थुतोमा दुई वटा च्याउ, यो कथा नजान्ने मुसा पोली खाउ, के हो?", answer: "कान"),
    GauKhaneKatha(question: "खाँदा उठ्छ, भोक लाग्दा सुत्छ, के हो?", answer: "तेल पेल्ने कोल"),
    GauKhaneKatha(question: "एक खुट्टे दाइको जिउमा कडाइ कडा, के हो?", answer: "ऐसेलुको बोट"),
    GauKhaneKatha(question: "एउटै रूप, आ आफ्ना काम, उही शरीरमा भिन्न भिन्नै नाम, के हो?", answer: "रौं"),
    GauKhaneKatha(question: "काठकी बच्ची ढुङ्गाकी गाई, जति दुह्यो उति आई, के हो?", answer: "चन्दन घोटेको"),
    GauKhaneKatha(question: "अगाडि अगाडि शंख बज्छ, पछाडि पछाडि ध्वजा हल्लिन्छ, के हो?", answer: "कुकुर भुकेको"),
    GauKhaneKatha(question: "आफू छ काली, नाम भने राती, के हो?", answer: "रात"),
    GauKhaneKatha(question: "उड्छ भुवा हैन, बोल्छ चील हैन, के हो?", answer: "हवाईजहाज"),
    GauKhaneKatha(question: "एक मुट्ठी खर, घुमाउने घर, के हो?", answer: "छाता"),
    GauKhaneKatha(question: "आकाशकी रानी पातालमा खसिन्, सूर्यलाई देख्दा थुप्पुक्का बसिन्, के हो?", answer: "शीत"),
    GauKhaneKatha(question: "एक खुट्टे गाई, दैलोमा न आताई, के हो?", answer: "छाता"),
    GauKhaneKatha(question: "अध्यारोमा बस्छे झुत्री बुढी, बिहानै सबेरै उठेर लटपट गर्छे, के हो?", answer: "भान्सा वा घर लोटाउने पोतो"),
    GauKhaneKatha(question: "अग्लो चुचुराको एउटा पाखामा फुलेको छ एक थुंगा फूल, के हो?", answer: "नाकमा लगाएको फुली"),
    GauKhaneKatha(question: "आगोमा उभिएर तिन भाइ निहुरेका, घाटीमा एउटै सिक्री सिउरेका, के हो?", answer: "ओदान"),
    GauKhaneKatha(question: "एक सिंघे गोरु, दुबै दाइ गर्छन्, के हो?", answer: "जाँतो पिधेको"),
    GauKhaneKatha(question: "एक देशमा थिए राजा, खुवाउथे सबैलाई मिठो खाजा, के हो?", answer: "घट्टा"),
    GauKhaneKatha(question: "अधेरी रानी बन, गाई हरायो खोज्न जाम, के हो?", answer: "जुम्रा हेरेको"),
    GauKhaneKatha(question: "एउटा गोरुका सोह्र सिंग, के हो?", answer: "चर्खा"),
    GauKhaneKatha(question: "एउटा रुख, बाह्र हाँगा, बाह्र हाँगाका दुई वटा पात, के हो?", answer: "महिना, दिन र रात"),
    GauKhaneKatha(question: "अध्यारो कुनामा काली बुढी, के हो?", answer: "मकै भुट्ने हाँडी"),
    GauKhaneKatha(question: "इंगे बिंगे, तिन सिंगे, के हो?", answer: "त्रिशूल"),
    GauKhaneKatha(question: "घर भित्र पस्दा नाङ्गै जाने, बाहिर निक्लिदा कपडा लगाएर आउने, के हो?", answer: "मकै"),
    GauKhaneKatha(question: "अध्यारोमा देखिन्छ, उज्यालोमा छैन, जति कोसिस गरेपनि टिप्न सकिदैन, के हो?", answer: "आकाशका तारा"),
    GauKhaneKatha(question: "उल्टो पढ्, सुल्टो पढ्, अर्थ एकै हुन्छ, पकाएर खाँदा मिठो दाल बन्छ, के हो?", answer: "रहर"),
    GauKhaneKatha(question: "अक्षर छ किताब हैन, डुल्छ खुट्टा हैन, आउछ जान्छ पाहुना हैन, खान हुन्न, लगाउन पनि हुन्न, तर नभै हुन्न, के हो?", answer: "पैसा"),
    GauKhaneKatha(question: "सुरिलो रुखको एउटा पात, के हो?", answer: "पुन्यु"),
    GauKhaneKatha(question: "अड्किने तौलीको खड्किने भात, खर खुर पारी एकै गास, के हो?", answer: "ओखर"),
    GauKhaneKatha(question: "छोड छोड बुढी म अगाडि जान्छु, के हो?", answer: "लौरो"),
    GauKhaneKatha(question: "आउती आमाका बत्तीस छोरा, आमालाई कुट्छन् सबै मोरा, के हो?", answer: "जिब्रो र दाँत"),
    GauKhaneKatha(question: "हात खुट्टा केही छैन जता सुकै जान्छ, बोल्ने मुख नभएपनि जता ततै जान्छ, के हो?", answer: "चिठी"),
    GauKhaneKatha(question: "सफा चिज चाहिदैन फोहोरमा बस्छ, रात भरि आहार खोजी दिन भरि लुक्छ, के हो?", answer: "लामखुट्टे"),
    GauKhaneKatha(question: "एउटा मानिसका आठ ओटा खुट्टा, के हो?", answer: "दौरा/भोटो वा चोलोको थुना"),
    GauKhaneKatha(question: "उधो बाट हाँस आयो, एक मुट्ठी घाँस ल्यायो, के हो?", answer: "मुला"),
    GauKhaneKatha(question: "उज्यालोमा भन्दा अध्यारोमा राम्री, दामी नामै उत्तिकै छिन् रत्न हुन् हाम्री, के हो?", answer: "चन्द्रमा"),
    GauKhaneKatha(question: "आफ्ना घरमा भात पकाई, अरुका घरमा तिहुन चकाई, के हो?", answer: "जेरी"),
    GauKhaneKatha(question: "घुम्छ फनफनी, खान्छ मत्यांग्रा, छर्छ धुलो, के हो?", answer: "घट्टा/जाँतो"),
    GauKhaneKatha(question: "उठ्ने बित्तिकै खोपमा हात, के हो?", answer: "लुगा लगाएको"),
    GauKhaneKatha(question: "एक मुट्ठी छ्वालीले पुरै घर छायेको, के हो?", answer: "बत्ती बालेको"),
    GauKhaneKatha(question: "खुट्टा नहुँदा जमिनमा हिड्न पाएन, पखेटा नहुँदा आकाशमा उड्न पाएन, के हो?", answer: "माछा"),
  ];

  List<GauKhaneKatha> _shuffledKathas = [];
  int _currentIndex = 0;

  bool _showAnswer = false;
  Random _random = Random();

  GauKhaneKathaGame() {
    _shuffleKathas();
  }

  void _shuffleKathas() {
    _shuffledKathas = List.from(_allKathas);
    _shuffledKathas.shuffle(_random);
    _currentIndex = 0;
  }

  GauKhaneKatha get currentKatha => _shuffledKathas[_currentIndex];
  
  bool get hasNextQuestion => _currentIndex < _shuffledKathas.length - 1;
  
  bool get showAnswer => _showAnswer;
  
  int get totalQuestions => _allKathas.length;
  
  int get currentQuestionNumber => _currentIndex + 1;
  


  void toggleAnswer() {
    _showAnswer = !_showAnswer;
  }

  void nextQuestion() {
    if (hasNextQuestion) {
      _currentIndex++;
      _showAnswer = false;
    } else {
      // Reshuffle when all questions are done
      _shuffleKathas();
      _showAnswer = false;
    }
  }



  void resetGame() {
    _shuffleKathas();
    _showAnswer = false;
  }

  String getProgressText() {
    return '${currentQuestionNumber}/${totalQuestions}';
  }


}
