class GoldSilverRate {
  final String metal;
  final String purity;
  final double buyingPrice;
  final double sellingPrice;
  final String unit;
  final String currency;
  final DateTime lastUpdated;

  GoldSilverRate({
    required this.metal,
    required this.purity,
    required this.buyingPrice,
    required this.sellingPrice,
    required this.unit,
    required this.currency,
    required this.lastUpdated,
  });

  factory GoldSilverRate.fromJson(Map<String, dynamic> json) {
    return GoldSilverRate(
      metal: json['metal'] ?? '',
      purity: json['purity'] ?? '',
      buyingPrice: (json['buying_price'] ?? 0).toDouble(),
      sellingPrice: (json['selling_price'] ?? 0).toDouble(),
      unit: json['unit'] ?? 'per tola',
      currency: json['currency'] ?? 'NPR',
      lastUpdated: DateTime.tryParse(json['last_updated'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'metal': metal,
      'purity': purity,
      'buying_price': buyingPrice,
      'selling_price': sellingPrice,
      'unit': unit,
      'currency': currency,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }
}

class GoldSilverData {
  final List<GoldSilverRate> goldRates;
  final List<GoldSilverRate> silverRates;
  final DateTime lastUpdated;
  final String source;

  GoldSilverData({
    required this.goldRates,
    required this.silverRates,
    required this.lastUpdated,
    required this.source,
  });

  factory GoldSilverData.fromJson(Map<String, dynamic> json) {
    List<GoldSilverRate> goldList = [];
    List<GoldSilverRate> silverList = [];

    // Parse gold rates
    if (json['gold'] != null) {
      if (json['gold'] is List) {
        goldList = (json['gold'] as List)
            .map((item) => GoldSilverRate.fromJson(item))
            .toList();
      } else if (json['gold'] is Map) {
        goldList = [GoldSilverRate.fromJson(json['gold'])];
      }
    }

    // Parse silver rates
    if (json['silver'] != null) {
      if (json['silver'] is List) {
        silverList = (json['silver'] as List)
            .map((item) => GoldSilverRate.fromJson(item))
            .toList();
      } else if (json['silver'] is Map) {
        silverList = [GoldSilverRate.fromJson(json['silver'])];
      }
    }

    return GoldSilverData(
      goldRates: goldList,
      silverRates: silverList,
      lastUpdated: DateTime.tryParse(json['last_updated'] ?? '') ?? DateTime.now(),
      source: json['source'] ?? 'Source Nepal',
    );
  }

  // Create sample data for fallback
  factory GoldSilverData.sampleData() {
    return GoldSilverData(
      goldRates: [
        GoldSilverRate(
          metal: 'सुन',
          purity: '24K',
          buyingPrice: 135000,
          sellingPrice: 135500,
          unit: 'प्रति तोला',
          currency: 'NPR',
          lastUpdated: DateTime.now(),
        ),
        GoldSilverRate(
          metal: 'सुन',
          purity: '22K',
          buyingPrice: 123500,
          sellingPrice: 124000,
          unit: 'प्रति तोला',
          currency: 'NPR',
          lastUpdated: DateTime.now(),
        ),
        GoldSilverRate(
          metal: 'सुन',
          purity: '18K',
          buyingPrice: 101500,
          sellingPrice: 102000,
          unit: 'प्रति तोला',
          currency: 'NPR',
          lastUpdated: DateTime.now(),
        ),
      ],
      silverRates: [
        GoldSilverRate(
          metal: 'चादी',
          purity: '999',
          buyingPrice: 1650,
          sellingPrice: 1700,
          unit: 'प्रति तोला',
          currency: 'NPR',
          lastUpdated: DateTime.now(),
        ),
        GoldSilverRate(
          metal: 'चादी',
          purity: '925',
          buyingPrice: 1550,
          sellingPrice: 1600,
          unit: 'प्रति तोला',
          currency: 'NPR',
          lastUpdated: DateTime.now(),
        ),
      ],
      lastUpdated: DateTime.now(),
      source: 'Sample Data - Google Sheets डाटा लोड गर्न सकिएन',
    );
  }
}
