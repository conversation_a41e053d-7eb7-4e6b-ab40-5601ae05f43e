class BankInterestRate {
  final String bankName;
  final String savingRate;
  final String fixedRate;
  final String lastUpdated;

  BankInterestRate({
    required this.bankName,
    required this.savingRate,
    required this.fixedRate,
    required this.lastUpdated,
  });

  factory BankInterestRate.fromCsvRow(List<String> row) {
    return BankInterestRate(
      bankName: row.length > 0 ? row[0].trim() : '',
      savingRate: row.length > 1 ? row[1].trim() : '',
      fixedRate: row.length > 2 ? row[2].trim() : '',
      lastUpdated: DateTime.now().toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bankName': bankName,
      'savingRate': savingRate,
      'fixedRate': fixedRate,
      'lastUpdated': lastUpdated,
    };
  }

  factory BankInterestRate.fromJson(Map<String, dynamic> json) {
    return BankInterestRate(
      bankName: json['bankName'] ?? '',
      savingRate: json['savingRate'] ?? '',
      fixedRate: json['fixedRate'] ?? '',
      lastUpdated: json['lastUpdated'] ?? '',
    );
  }
}

class InterestRateData {
  final List<BankInterestRate> rates;
  final DateTime lastUpdated;
  final bool isFromCache;

  InterestRateData({
    required this.rates,
    required this.lastUpdated,
    this.isFromCache = false,
  });

  bool get isEmpty => rates.isEmpty;
  
  int get bankCount => rates.length;
  
  String get formattedLastUpdated {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);
    
    if (difference.inMinutes < 1) {
      return 'अहिले';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} घण्टा अगाडि';
    } else {
      return '${difference.inDays} दिन अगाडि';
    }
  }
}
