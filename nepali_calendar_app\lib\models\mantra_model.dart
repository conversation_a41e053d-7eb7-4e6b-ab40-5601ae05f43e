class Mantra {
  final int id;
  final String titleNepali;
  final String titleEnglish;
  final String titleSanskrit;
  final String description;
  final String audioAssetPath;
  final String benefits;
  final String category;
  final String iconEmoji;

  Mantra({
    required this.id,
    required this.titleNepali,
    required this.titleEnglish,
    required this.titleSanskrit,
    required this.description,
    required this.audioAssetPath,
    required this.benefits,
    required this.category,
    required this.iconEmoji,
  });

  factory Mantra.fromJson(Map<String, dynamic> json) {
    return Mantra(
      id: json['id'],
      titleNepali: json['titleNepali'],
      titleEnglish: json['titleEnglish'],
      titleSanskrit: json['titleSanskrit'],
      description: json['description'],
      audioAssetPath: json['audioAssetPath'],
      benefits: json['benefits'],
      category: json['category'],
      iconEmoji: json['iconEmoji'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'titleNepali': titleNepali,
      'titleEnglish': titleEnglish,
      'titleSanskrit': titleSanskrit,
      'description': description,
      'audioAssetPath': audioAssetPath,
      'benefits': benefits,
      'category': category,
      'iconEmoji': iconEmoji,
    };
  }
}
