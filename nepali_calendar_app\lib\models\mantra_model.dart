class Mantra {
  final int id;
  final String titleNepali;
  final String titleEnglish;
  final String titleSanskrit;
  final String description;
  final String youtubeVideoId;
  final String thumbnailUrl;
  final String duration;
  final String benefits;
  final String category;

  Mantra({
    required this.id,
    required this.titleNepali,
    required this.titleEnglish,
    required this.titleSanskrit,
    required this.description,
    required this.youtubeVideoId,
    required this.thumbnailUrl,
    required this.duration,
    required this.benefits,
    required this.category,
  });

  factory Mantra.fromJson(Map<String, dynamic> json) {
    return Mantra(
      id: json['id'],
      titleNepali: json['titleNepali'],
      titleEnglish: json['titleEnglish'],
      titleSanskrit: json['titleSanskrit'],
      description: json['description'],
      youtubeVideoId: json['youtubeVideoId'],
      thumbnailUrl: json['thumbnailUrl'],
      duration: json['duration'],
      benefits: json['benefits'],
      category: json['category'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'titleNepali': titleNepali,
      'titleEnglish': titleEnglish,
      'titleSanskrit': titleSanskrit,
      'description': description,
      'youtubeVideoId': youtubeVideoId,
      'thumbnailUrl': thumbnailUrl,
      'duration': duration,
      'benefits': benefits,
      'category': category,
    };
  }
}
