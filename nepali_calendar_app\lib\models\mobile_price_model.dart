import 'package:flutter/material.dart';

class MobilePriceModel {
  final String model;
  final String price;
  final String storage;
  final int priceValue; // For comparison

  MobilePriceModel({
    required this.model,
    required this.price,
    required this.storage,
    required this.priceValue,
  });

  factory MobilePriceModel.fromJson(Map<String, dynamic> json) {
    return MobilePriceModel(
      model: json['model'] ?? '',
      price: json['price'] ?? '',
      storage: json['storage'] ?? '',
      priceValue: json['priceValue'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'model': model,
      'price': price,
      'storage': storage,
      'priceValue': priceValue,
    };
  }

  // Extract numeric price value for comparison and find lowest price
  static int extractPriceValue(String price) {
    // Remove "New", "(New)", and other unwanted text
    String cleanPrice = price
        .replaceAll(RegExp(r'\(New\)', caseSensitive: false), '')
        .replaceAll(RegExp(r'\bNew\b', caseSensitive: false), '')
        .replaceAll(RegExp(r'\(नयाँ\)', caseSensitive: false), '')
        .replaceAll(RegExp(r'\bनयाँ\b', caseSensitive: false), '')
        .replaceAll(RegExp(r'\(latest\)', caseSensitive: false), '')
        .replaceAll(RegExp(r'\blatest\b', caseSensitive: false), '')
        .trim();

    // Find all price patterns
    final pricePattern = RegExp(r'(?:Rs\.?\s*)?(\d{1,3}(?:,\d{3})*|\d+)', caseSensitive: false);
    final matches = pricePattern.allMatches(cleanPrice);
    final priceValues = <int>[];

    for (final match in matches) {
      final numberStr = match.group(1)?.replaceAll(',', '') ?? '';
      final value = int.tryParse(numberStr);
      if (value != null && value >= 5000) { // Only consider realistic mobile prices
        priceValues.add(value);
      }
    }

    // Return the lowest price, or 0 if no valid prices found
    return priceValues.isNotEmpty ? priceValues.reduce((a, b) => a < b ? a : b) : 0;
  }

  // Clean and format price string to show only the lowest price
  static String cleanPriceString(String price) {
    // Remove "New", "(New)", and other unwanted text
    String cleanPrice = price
        .replaceAll(RegExp(r'\(New\)', caseSensitive: false), '')
        .replaceAll(RegExp(r'\bNew\b', caseSensitive: false), '')
        .replaceAll(RegExp(r'\(नयाँ\)', caseSensitive: false), '')
        .replaceAll(RegExp(r'\bनयाँ\b', caseSensitive: false), '')
        .replaceAll(RegExp(r'\(latest\)', caseSensitive: false), '')
        .replaceAll(RegExp(r'\blatest\b', caseSensitive: false), '')
        .trim();

    // Find all price patterns like "Rs. 15,000", "15000", "Rs 15000", etc.
    final pricePattern = RegExp(r'(?:Rs\.?\s*)?(\d{1,3}(?:,\d{3})*|\d+)', caseSensitive: false);
    final matches = pricePattern.allMatches(cleanPrice);
    final priceValues = <int>[];

    for (final match in matches) {
      final numberStr = match.group(1)?.replaceAll(',', '') ?? '';
      final value = int.tryParse(numberStr);
      if (value != null && value >= 5000) { // Only consider realistic mobile prices (5000+)
        priceValues.add(value);
      }
    }

    if (priceValues.isNotEmpty) {
      final lowestPrice = priceValues.reduce((a, b) => a < b ? a : b);
      // Format with commas for better readability
      final formattedPrice = _formatNumberWithCommas(lowestPrice);
      return 'Rs. $formattedPrice';
    }

    // If no valid price found, try to clean and return original
    cleanPrice = cleanPrice.replaceAll(RegExp(r'[^\d,.\s]'), '').trim();
    if (cleanPrice.isNotEmpty) {
      return cleanPrice.startsWith('Rs') ? cleanPrice : 'Rs. $cleanPrice';
    }

    return 'Rs. N/A';
  }

  // Helper method to format numbers with commas
  static String _formatNumberWithCommas(int number) {
    final str = number.toString();
    final result = StringBuffer();

    for (int i = 0; i < str.length; i++) {
      if (i > 0 && (str.length - i) % 3 == 0) {
        result.write(',');
      }
      result.write(str[i]);
    }

    return result.toString();
  }
}

class MobileBrandData {
  final String brandName;
  final String brandLogo;
  final List<MobilePriceModel> mobiles;
  final String lastUpdated;

  MobileBrandData({
    required this.brandName,
    required this.brandLogo,
    required this.mobiles,
    required this.lastUpdated,
  });

  factory MobileBrandData.fromJson(Map<String, dynamic> json) {
    return MobileBrandData(
      brandName: json['brandName'] ?? '',
      brandLogo: json['brandLogo'] ?? '',
      mobiles: (json['mobiles'] as List<dynamic>?)
          ?.map((mobile) => MobilePriceModel.fromJson(mobile))
          .toList() ?? [],
      lastUpdated: json['lastUpdated'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'brandName': brandName,
      'brandLogo': brandLogo,
      'mobiles': mobiles.map((mobile) => mobile.toJson()).toList(),
      'lastUpdated': lastUpdated,
    };
  }
}

class MobileBrandInfo {
  final String name;
  final String nepaliName;
  final String logo;
  final String sheetId;
  final Color primaryColor;
  final Color secondaryColor;

  MobileBrandInfo({
    required this.name,
    required this.nepaliName,
    required this.logo,
    required this.sheetId,
    required this.primaryColor,
    required this.secondaryColor,
  });
}
