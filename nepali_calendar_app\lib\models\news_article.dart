class NewsArticle {
  final String title;
  final String description;
  final String url;
  final String imageUrl;
  final String source;
  final DateTime publishedAt;
  final String category;

  NewsArticle({
    required this.title,
    required this.description,
    required this.url,
    required this.imageUrl,
    required this.source,
    required this.publishedAt,
    required this.category,
  });

  factory NewsArticle.fromJson(Map<String, dynamic> json, String source) {
    return NewsArticle(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      url: json['url'] ?? '',
      imageUrl: json['urlToImage'] ?? '',
      source: source,
      publishedAt: DateTime.tryParse(json['publishedAt'] ?? '') ?? DateTime.now(),
      category: json['category'] ?? 'सामान्य',
    );
  }

  // Create from RSS feed item
  factory NewsArticle.fromRss(Map<String, dynamic> rssItem, String source) {
    return NewsArticle(
      title: rssItem['title'] ?? '',
      description: rssItem['description'] ?? '',
      url: rssItem['link'] ?? '',
      imageUrl: rssItem['image'] ?? '',
      source: source,
      publishedAt: DateTime.tryParse(rssItem['pubDate'] ?? '') ?? DateTime.now(),
      category: rssItem['category'] ?? 'सामान्य',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'url': url,
      'urlToImage': imageUrl,
      'source': source,
      'publishedAt': publishedAt.toIso8601String(),
      'category': category,
    };
  }
}
