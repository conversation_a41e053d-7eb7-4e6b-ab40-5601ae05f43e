class PanchangData {
  final DateInfo dateInfo;
  final SunMoonTimes sunMoon;
  final TithiInfo tithi;
  final String paksha;
  final NakshatraInfo nakshatra;
  final YogaInfo yoga;
  final List<KaranaInfo> karana;
  final String moonSign;
  final String dayDuration;
  final String season;
  final String ayana;

  PanchangData({
    required this.dateInfo,
    required this.sunMoon,
    required this.tithi,
    required this.paksha,
    required this.nakshatra,
    required this.yoga,
    required this.karana,
    required this.moonSign,
    required this.dayDuration,
    required this.season,
    required this.ayana,
  });

  factory PanchangData.fromJson(Map<String, dynamic> json) {
    return PanchangData(
      dateInfo: DateInfo.fromJson(json['dateInfo']),
      sunMoon: SunMoonTimes.fromJson(json['sunMoon']),
      tithi: TithiInfo.fromJson(json['tithi']),
      paksha: json['paksha'] ?? '',
      nakshatra: NakshatraInfo.fromJson(json['nakshatra']),
      yoga: YogaInfo.fromJson(json['yoga']),
      karana: (json['karana'] as List?)
          ?.map((k) => KaranaInfo.fromJson(k))
          .toList() ?? [],
      moonSign: json['moonSign'] ?? '',
      dayDuration: json['dayDuration'] ?? '',
      season: json['season'] ?? '',
      ayana: json['ayana'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dateInfo': dateInfo.toJson(),
      'sunMoon': sunMoon.toJson(),
      'tithi': tithi.toJson(),
      'paksha': paksha,
      'nakshatra': nakshatra.toJson(),
      'yoga': yoga.toJson(),
      'karana': karana.map((k) => k.toJson()).toList(),
      'moonSign': moonSign,
      'dayDuration': dayDuration,
      'season': season,
      'ayana': ayana,
    };
  }
}

class DateInfo {
  final String nepali;
  final String english;
  final String nepalSamvat;

  DateInfo({
    required this.nepali,
    required this.english,
    required this.nepalSamvat,
  });

  factory DateInfo.fromJson(Map<String, dynamic> json) {
    return DateInfo(
      nepali: json['nepali'] ?? '',
      english: json['english'] ?? '',
      nepalSamvat: json['nepalSamvat'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nepali': nepali,
      'english': english,
      'nepalSamvat': nepalSamvat,
    };
  }
}

class SunMoonTimes {
  final String sunrise;
  final String sunset;
  final String moonrise;
  final String moonset;

  SunMoonTimes({
    required this.sunrise,
    required this.sunset,
    required this.moonrise,
    required this.moonset,
  });

  factory SunMoonTimes.fromJson(Map<String, dynamic> json) {
    return SunMoonTimes(
      sunrise: json['sunrise'] ?? '',
      sunset: json['sunset'] ?? '',
      moonrise: json['moonrise'] ?? '',
      moonset: json['moonset'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sunrise': sunrise,
      'sunset': sunset,
      'moonrise': moonrise,
      'moonset': moonset,
    };
  }
}

class TithiInfo {
  final String current;
  final String endTime;
  final String next;

  TithiInfo({
    required this.current,
    required this.endTime,
    required this.next,
  });

  factory TithiInfo.fromJson(Map<String, dynamic> json) {
    return TithiInfo(
      current: json['current'] ?? '',
      endTime: json['endTime'] ?? '',
      next: json['next'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current': current,
      'endTime': endTime,
      'next': next,
    };
  }
}

class NakshatraInfo {
  final String current;
  final String endTime;
  final String next;

  NakshatraInfo({
    required this.current,
    required this.endTime,
    required this.next,
  });

  factory NakshatraInfo.fromJson(Map<String, dynamic> json) {
    return NakshatraInfo(
      current: json['current'] ?? '',
      endTime: json['endTime'] ?? '',
      next: json['next'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current': current,
      'endTime': endTime,
      'next': next,
    };
  }
}

class YogaInfo {
  final String current;
  final String endTime;
  final String next;

  YogaInfo({
    required this.current,
    required this.endTime,
    required this.next,
  });

  factory YogaInfo.fromJson(Map<String, dynamic> json) {
    return YogaInfo(
      current: json['current'] ?? '',
      endTime: json['endTime'] ?? '',
      next: json['next'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current': current,
      'endTime': endTime,
      'next': next,
    };
  }
}

class KaranaInfo {
  final String current;
  final String endTime;
  final String next;

  KaranaInfo({
    required this.current,
    required this.endTime,
    required this.next,
  });

  factory KaranaInfo.fromJson(Map<String, dynamic> json) {
    return KaranaInfo(
      current: json['current'] ?? '',
      endTime: json['endTime'] ?? '',
      next: json['next'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current': current,
      'endTime': endTime,
      'next': next,
    };
  }
}
