class RadioStation {
  final String id;
  final String name;
  final String streamUrl;
  final String? address;
  final String? frequency;
  final String? description;
  final bool isWorking;

  RadioStation({
    required this.id,
    required this.name,
    required this.streamUrl,
    this.address,
    this.frequency,
    this.description,
    this.isWorking = true,
  });

  factory RadioStation.fromJson(Map<String, dynamic> json) {
    return RadioStation(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      streamUrl: json['streamUrl']?.toString() ?? '',
      address: json['address']?.toString(),
      frequency: json['frequency']?.toString(),
      description: json['description']?.toString(),
      isWorking: json['isWorking'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'streamUrl': streamUrl,
      'address': address,
      'frequency': frequency,
      'description': description,
      'isWorking': isWorking,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RadioStation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  String getFreqString() {
    if (frequency != null && frequency!.isNotEmpty) {
      return frequency!;
    }
    return 'FM';
  }

  @override
  String toString() {
    return 'RadioStation(id: $id, name: $name, streamUrl: $streamUrl)';
  }
}
