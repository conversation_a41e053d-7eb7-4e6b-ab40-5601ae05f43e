class RasifalModel {
  final String sign;
  final String horoscope;
  final String date;

  RasifalModel({
    required this.sign,
    required this.horoscope,
    required this.date,
  });

  factory RasifalModel.fromJson(Map<String, dynamic> json) {
    return RasifalModel(
      sign: json['sign'] ?? '',
      horoscope: json['horoscope'] ?? '',
      date: json['date'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sign': sign,
      'horoscope': horoscope,
      'date': date,
    };
  }
}

class ZodiacSign {
  final String nepaliName;
  final String englishName;
  final String emoji;
  final String apiName;

  ZodiacSign({
    required this.nepaliName,
    required this.englishName,
    required this.emoji,
    required this.apiName,
  });
}