class SahitData {
  final String title;
  final List<MonthDates> monthDates;
  final String iconName;
  final int colorValue;

  SahitData({
    required this.title,
    required this.monthDates,
    required this.iconName,
    required this.colorValue,
  });

  factory SahitData.fromJson(Map<String, dynamic> json) {
    return SahitData(
      title: json['title'],
      monthDates: (json['monthDates'] as List)
          .map((month) => MonthDates.fromJson(month))
          .toList(),
      iconName: json['iconName'],
      colorValue: json['colorValue'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'monthDates': monthDates.map((month) => month.toJson()).toList(),
      'iconName': iconName,
      'colorValue': colorValue,
    };
  }
}

class MonthDates {
  final String month;
  final List<int> dates;

  MonthDates({
    required this.month,
    required this.dates,
  });

  factory MonthDates.fromJson(Map<String, dynamic> json) {
    return MonthDates(
      month: json['month'],
      dates: List<int>.from(json['dates']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'dates': dates,
    };
  }
}

// Predefined Sahit data
class SahitDataProvider {
  static List<SahitData> getAllSahitData() {
    return [
      SahitData(
        title: 'बिवाह गर्ने साइत',
        iconName: 'wedding',
        colorValue: 0xFFE91E63, // Pink
        monthDates: [
          MonthDates(
            month: 'वैशाख',
            dates: [1, 3, 5, 7, 8, 16, 17, 22, 23, 28, 30, 31],
          ),
          MonthDates(
            month: 'जेठ',
            dates: [1, 2, 14, 18, 19, 24, 25],
          ),
          MonthDates(
            month: 'मंसिर',
            dates: [6, 8, 13, 14, 18, 19, 20],
          ),
          MonthDates(
            month: 'माघ',
            dates: [21, 22, 27],
          ),
          MonthDates(
            month: 'फागुन',
            dates: [8, 12, 13, 14, 25, 26, 27, 28, 30],
          ),
        ],
      ),
      SahitData(
        title: 'पास्नी गर्ने साइतहरु',
        iconName: 'child',
        colorValue: 0xFF2196F3, // Blue
        monthDates: [
          MonthDates(
            month: 'वैशाख',
            dates: [1, 17],
          ),
          MonthDates(
            month: 'जेठ',
            dates: [14],
          ),
          MonthDates(
            month: 'असार',
            dates: [2, 13],
          ),
          MonthDates(
            month: 'साउन',
            dates: [26, 29],
          ),
          MonthDates(
            month: 'भदौ',
            dates: [29],
          ),
          MonthDates(
            month: 'असोज',
            dates: [16],
          ),
          MonthDates(
            month: 'कार्तिक',
            dates: [7, 17, 21, 24],
          ),
        ],
      ),
      SahitData(
        title: 'रुद्री गर्ने साइत',
        iconName: 'prayer',
        colorValue: 0xFFFF9800, // Orange
        monthDates: [
          MonthDates(
            month: 'वैशाख',
            dates: [1, 4, 5, 6, 8, 11, 12, 13, 16, 19, 20, 21, 23, 26, 27, 28, 30],
          ),
          MonthDates(
            month: 'जेठ',
            dates: [2, 3, 4, 6, 9, 10, 11, 17, 18, 19, 21, 25],
          ),
          MonthDates(
            month: 'असार',
            dates: [2, 7, 30, 31],
          ),
          MonthDates(
            month: 'साउन',
            dates: [2, 5, 6, 10, 13, 14, 15, 18, 21, 22, 23, 25, 28, 29, 31],
          ),
          MonthDates(
            month: 'भदौ',
            dates: [3, 4, 5, 9, 12, 13, 14, 16],
          ),
          MonthDates(
            month: 'असोज',
            dates: [7, 11, 12, 13, 15, 18, 20, 22, 24, 25, 26, 28, 31],
          ),
          MonthDates(
            month: 'कार्तिक',
            dates: [1, 9, 10, 11, 13, 17, 18, 20, 23, 24, 25, 26, 29, 30],
          ),
        ],
      ),
      SahitData(
        title: 'होम गर्ने साइत',
        iconName: 'fire',
        colorValue: 0xFFF44336, // Red
        monthDates: [
          MonthDates(
            month: 'वैशाख',
            dates: [1, 3, 5, 7, 9, 11, 13, 14, 15, 17, 19, 22, 24, 26, 28, 30],
          ),
          MonthDates(
            month: 'जेठ',
            dates: [1, 3, 4, 6, 8, 10, 12, 16, 18, 20, 22, 24],
          ),
          MonthDates(
            month: 'असार',
            dates: [27, 29, 31],
          ),
          MonthDates(
            month: 'साउन',
            dates: [1, 3, 4, 6, 9, 12, 14, 16, 17, 18, 20, 22, 24, 26, 28, 31],
          ),
          MonthDates(
            month: 'भदौ',
            dates: [2, 4, 6, 8, 10, 12, 14, 16, 18],
          ),
          MonthDates(
            month: 'असोज',
            dates: [6, 8, 10, 12, 14, 16, 18, 21, 23, 25, 26, 28, 30],
          ),
          MonthDates(
            month: 'कार्तिक',
            dates: [1, 8, 9, 11, 13, 17, 19, 21, 24, 26, 28],
          ),
        ],
      ),
      SahitData(
        title: 'ब्रतबन्ध गर्ने साइत',
        iconName: 'ceremony',
        colorValue: 0xFF9C27B0, // Purple
        monthDates: [
          MonthDates(
            month: 'वैशाख',
            dates: [5, 19, 24],
          ),
          MonthDates(
            month: 'फागुन',
            dates: [7, 14, 15],
          ),
          MonthDates(
            month: 'चैत',
            dates: [6, 15],
          ),
        ],
      ),
      SahitData(
        title: 'पसल खोल्ने साइत',
        iconName: 'shop',
        colorValue: 0xFF4CAF50, // Green
        monthDates: [
          MonthDates(
            month: 'वैशाख',
            dates: [7, 8, 17],
          ),
          MonthDates(
            month: 'जेठ',
            dates: [4, 14, 17],
          ),
          MonthDates(
            month: 'असार',
            dates: [27, 28],
          ),
          MonthDates(
            month: 'साउन',
            dates: [5, 29],
          ),
          MonthDates(
            month: 'भदौ',
            dates: [9, 15],
          ),
          MonthDates(
            month: 'असोज',
            dates: [11, 25, 26],
          ),
          MonthDates(
            month: 'कार्तिक',
            dates: [7, 17, 21, 29, 30],
          ),
        ],
      ),
      SahitData(
        title: 'जग राख्न्ने साइत',
        iconName: 'foundation',
        colorValue: 0xFF795548, // Brown
        monthDates: [
          MonthDates(
            month: 'साउन',
            dates: [26],
          ),
          MonthDates(
            month: 'कार्तिक',
            dates: [21],
          ),
          MonthDates(
            month: 'मंसिर',
            dates: [11, 19, 20],
          ),
          MonthDates(
            month: 'फागुन',
            dates: [14],
          ),
        ],
      ),
      SahitData(
        title: 'घर सर्ने साइत',
        iconName: 'house',
        colorValue: 0xFF009688, // Teal
        monthDates: [
          MonthDates(
            month: 'माघ',
            dates: [28],
          ),
          MonthDates(
            month: 'फागुन',
            dates: [14, 30],
          ),
        ],
      ),
    ];
  }
}
