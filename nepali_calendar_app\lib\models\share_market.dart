class ShareMarket {
  final String symbol;
  final String ltp; // Last Traded Price
  final String changePercent;
  final String open;
  final String high;
  final String low;
  final String quantity;

  ShareMarket({
    required this.symbol,
    required this.ltp,
    required this.changePercent,
    required this.open,
    required this.high,
    required this.low,
    required this.quantity,
  });

  // Helper method to get change color
  bool get isPositive {
    try {
      final change = double.parse(changePercent.replaceAll('%', '').replaceAll(',', ''));
      return change >= 0;
    } catch (e) {
      return true;
    }
  }

  // Helper method to format numbers
  String get formattedLtp {
    return ltp.replaceAll('"', '').trim();
  }

  String get formattedChange {
    return changePercent.replaceAll('"', '').trim();
  }

  String get formattedOpen {
    return open.replaceAll('"', '').trim();
  }

  String get formattedHigh {
    return high.replaceAll('"', '').trim();
  }

  String get formattedLow {
    return low.replaceAll('"', '').trim();
  }

  String get formattedQuantity {
    return quantity.replaceAll('"', '').trim();
  }

  @override
  String toString() {
    return 'ShareMarket(symbol: $symbol, ltp: $ltp, change: $changePercent, open: $open, high: $high, low: $low, qty: $quantity)';
  }
}
