class SimpleVegetable {
  final String name;
  final String price; // Will store as "min,max,avg" format
  final String unit;
  final String category;
  final String emoji;

  SimpleVegetable({
    required this.name,
    required this.price,
    required this.unit,
    required this.category,
    required this.emoji,
  });

  // Helper methods to get individual price components - keep Nepali format
  String get minPrice {
    final parts = price.split(',');
    return parts.isNotEmpty ? parts[0].trim() : 'रू ०';
  }

  String get maxPrice {
    final parts = price.split(',');
    return parts.length > 1 ? parts[1].trim() : 'रू ०';
  }

  String get avgPrice {
    final parts = price.split(',');
    return parts.length > 2 ? parts[2].trim() : 'रू ०';
  }

  @override
  String toString() {
    return 'SimpleVegetable(name: $name, price: $price, unit: $unit, category: $category, emoji: $emoji)';
  }
}
