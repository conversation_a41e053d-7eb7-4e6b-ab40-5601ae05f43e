import 'dart:math';

enum Direction { up, down, left, right }

enum GameState { ready, playing, paused, gameOver }

class Position {
  final int x;
  final int y;

  Position(this.x, this.y);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Position && runtimeType == other.runtimeType && x == other.x && y == other.y;

  @override
  int get hashCode => x.hashCode ^ y.hashCode;

  Position operator +(Position other) => Position(x + other.x, y + other.y);
}

class SnakeGame {
  static const int gridWidth = 25;
  static const int gridHeight = 25;
  
  List<Position> snake;
  Position food;
  Direction direction;
  Direction? nextDirection;
  GameState gameState;
  int score;
  int highScore;
  int speed;
  Random random;

  SnakeGame()
      : snake = [Position(12, 12)],
        food = Position(18, 18),
        direction = Direction.right,
        nextDirection = null,
        gameState = GameState.ready,
        score = 0,
        highScore = 0,
        speed = 200,
        random = Random();

  // Start the game
  void startGame() {
    resetGame();
    gameState = GameState.playing;
  }

  // Reset game to initial state
  void resetGame() {
    snake = [Position(12, 12)];
    direction = Direction.right;
    nextDirection = null;
    score = 0;
    speed = 200;
    _generateFood();
    gameState = GameState.ready;
  }

  // Pause/Resume game
  void togglePause() {
    if (gameState == GameState.playing) {
      gameState = GameState.paused;
    } else if (gameState == GameState.paused) {
      gameState = GameState.playing;
    }
  }

  // Change direction
  void changeDirection(Direction newDirection) {
    if (gameState != GameState.playing) return;

    // Prevent reverse direction
    if (_isOppositeDirection(newDirection, direction)) return;

    nextDirection = newDirection;
  }

  // Update game state
  void update() {
    if (gameState != GameState.playing) return;

    // Apply next direction if set
    if (nextDirection != null) {
      direction = nextDirection!;
      nextDirection = null;
    }

    // Move snake
    Position head = snake.first;
    Position newHead = _getNextPosition(head, direction);

    // Check wall collision
    if (_isOutOfBounds(newHead)) {
      _gameOver();
      return;
    }

    // Check self collision
    if (snake.contains(newHead)) {
      _gameOver();
      return;
    }

    // Add new head
    snake.insert(0, newHead);

    // Check food collision
    if (newHead == food) {
      _eatFood();
    } else {
      // Remove tail if no food eaten
      snake.removeLast();
    }
  }

  // Generate new food position
  void _generateFood() {
    do {
      food = Position(
        random.nextInt(gridWidth),
        random.nextInt(gridHeight),
      );
    } while (snake.contains(food));
  }

  // Handle food eating
  void _eatFood() {
    score += 10;
    
    // Increase speed every 50 points
    if (score % 50 == 0 && speed > 100) {
      speed -= 10;
    }
    
    _generateFood();
  }

  // Handle game over
  void _gameOver() {
    gameState = GameState.gameOver;
    if (score > highScore) {
      highScore = score;
    }
  }

  // Get next position based on direction
  Position _getNextPosition(Position current, Direction dir) {
    switch (dir) {
      case Direction.up:
        return Position(current.x, current.y - 1);
      case Direction.down:
        return Position(current.x, current.y + 1);
      case Direction.left:
        return Position(current.x - 1, current.y);
      case Direction.right:
        return Position(current.x + 1, current.y);
    }
  }

  // Check if position is out of bounds
  bool _isOutOfBounds(Position pos) {
    return pos.x < 0 || pos.x >= gridWidth || pos.y < 0 || pos.y >= gridHeight;
  }

  // Check if two directions are opposite
  bool _isOppositeDirection(Direction dir1, Direction dir2) {
    return (dir1 == Direction.up && dir2 == Direction.down) ||
           (dir1 == Direction.down && dir2 == Direction.up) ||
           (dir1 == Direction.left && dir2 == Direction.right) ||
           (dir1 == Direction.right && dir2 == Direction.left);
  }

  // Get game state message
  String getGameStateMessage() {
    switch (gameState) {
      case GameState.ready:
        return 'खेल सुरु गर्न ट्याप गर्नुहोस्';
      case GameState.playing:
        return 'स्कोर: $score';
      case GameState.paused:
        return 'रोकिएको - जारी राख्न ट्याप गर्नुहोस्';
      case GameState.gameOver:
        return 'खेल समाप्त! स्कोर: $score';
    }
  }

  // Get speed level
  int getSpeedLevel() {
    return ((200 - speed) ~/ 10) + 1;
  }

  // Check if position is snake head
  bool isSnakeHead(Position pos) {
    return snake.isNotEmpty && snake.first == pos;
  }

  // Check if position is snake body
  bool isSnakeBody(Position pos) {
    return snake.length > 1 && snake.skip(1).contains(pos);
  }

  // Check if position is food
  bool isFood(Position pos) {
    return food == pos;
  }

  // Get snake length
  int get snakeLength => snake.length;

  // Check if game is active
  bool get isGameActive => gameState == GameState.playing;

  // Check if game is over
  bool get isGameOver => gameState == GameState.gameOver;

  // Check if game is paused
  bool get isGamePaused => gameState == GameState.paused;

  // Check if game is ready
  bool get isGameReady => gameState == GameState.ready;
}
