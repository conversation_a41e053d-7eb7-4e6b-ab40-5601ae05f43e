enum Player { X, O, none }

enum GameState { playing, xWins, oWins, draw }

enum GameMode { friend, bot }

enum BotDifficulty { easy, medium, hard }

class TicTacToeGame {
  List<List<Player>> board;
  Player currentPlayer;
  GameState gameState;
  List<int>? winningLine;
  int xScore;
  int oScore;
  int drawCount;
  GameMode gameMode;
  Player humanPlayer;
  Player botPlayer;
  BotDifficulty botDifficulty;

  TicTacToeGame({
    this.gameMode = GameMode.friend,
    this.humanPlayer = Player.X,
    this.botDifficulty = BotDifficulty.medium,
  }) : board = List.generate(3, (_) => List.filled(3, Player.none)),
        currentPlayer = Player.X,
        gameState = GameState.playing,
        winningLine = null,
        xScore = 0,
        oScore = 0,
        drawCount = 0,
        botPlayer = humanPlayer == Player.X ? Player.O : Player.X;

  // Make a move at the specified position
  bool makeMove(int row, int col) {
    if (board[row][col] != Player.none || gameState != GameState.playing) {
      return false;
    }

    board[row][col] = currentPlayer;
    _checkGameState();

    if (gameState == GameState.playing) {
      currentPlayer = currentPlayer == Player.X ? Player.O : Player.X;
    }

    return true;
  }

  // Check the current game state
  void _checkGameState() {
    // Check rows
    for (int i = 0; i < 3; i++) {
      if (board[i][0] != Player.none &&
          board[i][0] == board[i][1] &&
          board[i][1] == board[i][2]) {
        gameState = board[i][0] == Player.X ? GameState.xWins : GameState.oWins;
        winningLine = [i * 3, i * 3 + 1, i * 3 + 2];
        _updateScore();
        return;
      }
    }

    // Check columns
    for (int i = 0; i < 3; i++) {
      if (board[0][i] != Player.none &&
          board[0][i] == board[1][i] &&
          board[1][i] == board[2][i]) {
        gameState = board[0][i] == Player.X ? GameState.xWins : GameState.oWins;
        winningLine = [i, i + 3, i + 6];
        _updateScore();
        return;
      }
    }

    // Check diagonals
    if (board[0][0] != Player.none &&
        board[0][0] == board[1][1] &&
        board[1][1] == board[2][2]) {
      gameState = board[0][0] == Player.X ? GameState.xWins : GameState.oWins;
      winningLine = [0, 4, 8];
      _updateScore();
      return;
    }

    if (board[0][2] != Player.none &&
        board[0][2] == board[1][1] &&
        board[1][1] == board[2][0]) {
      gameState = board[0][2] == Player.X ? GameState.xWins : GameState.oWins;
      winningLine = [2, 4, 6];
      _updateScore();
      return;
    }

    // Check for draw
    bool isFull = true;
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
        if (board[i][j] == Player.none) {
          isFull = false;
          break;
        }
      }
      if (!isFull) break;
    }

    if (isFull) {
      gameState = GameState.draw;
      drawCount++;
    }
  }

  // Update score based on game result
  void _updateScore() {
    if (gameState == GameState.xWins) {
      xScore++;
    } else if (gameState == GameState.oWins) {
      oScore++;
    }
  }

  // Reset the current game
  void resetGame() {
    board = List.generate(3, (_) => List.filled(3, Player.none));
    currentPlayer = Player.X;
    gameState = GameState.playing;
    winningLine = null;
  }

  // Reset all scores
  void resetScores() {
    xScore = 0;
    oScore = 0;
    drawCount = 0;
    resetGame();
  }

  // Get the symbol for a player
  String getPlayerSymbol(Player player) {
    switch (player) {
      case Player.X:
        return 'X';
      case Player.O:
        return 'O';
      case Player.none:
        return '';
    }
  }

  // Get the name for a player
  String getPlayerName(Player player) {
    switch (player) {
      case Player.X:
        return 'खेलाडी X';
      case Player.O:
        return 'खेलाडी O';
      case Player.none:
        return '';
    }
  }

  // Get game result message
  String getGameResultMessage() {
    switch (gameState) {
      case GameState.xWins:
        return 'खेलाडी X जित्यो! 🎉';
      case GameState.oWins:
        return 'खेलाडी O जित्यो! 🎉';
      case GameState.draw:
        return 'बराबरी! 🤝';
      case GameState.playing:
        return '${getPlayerName(currentPlayer)} को पालो';
    }
  }

  // Check if a cell is part of winning line
  bool isWinningCell(int row, int col) {
    if (winningLine == null) return false;
    int cellIndex = row * 3 + col;
    return winningLine!.contains(cellIndex);
  }

  // Bot makes a move using difficulty-based AI
  void makeBotMove() {
    if (gameState != GameState.playing || currentPlayer != botPlayer) return;

    final random = DateTime.now().millisecondsSinceEpoch % 100;

    // Different strategies based on difficulty
    switch (botDifficulty) {
      case BotDifficulty.easy:
        _makeEasyBotMove(random);
        break;
      case BotDifficulty.medium:
        _makeMediumBotMove(random);
        break;
      case BotDifficulty.hard:
        _makeHardBotMove(random);
        break;
    }
  }

  // Easy bot - makes many mistakes, user wins often
  void _makeEasyBotMove(int random) {
    // 60% chance to make random move (lots of mistakes)
    if (random < 60) {
      _makeRandomMove();
      return;
    }

    // Only 40% chance to play strategically
    _makeStrategicMove(random, winChance: 100, blockChance: 30, centerChance: 20, cornerChance: 30);
  }

  // Medium bot - balanced, user has good winning chances
  void _makeMediumBotMove(int random) {
    // 30% chance to make random move (some mistakes)
    if (random < 30) {
      _makeRandomMove();
      return;
    }

    // 70% chance to play strategically
    _makeStrategicMove(random, winChance: 100, blockChance: 70, centerChance: 50, cornerChance: 60);
  }

  // Hard bot - plays well but still beatable
  void _makeHardBotMove(int random) {
    // 15% chance to make random move (few mistakes)
    if (random < 15) {
      _makeRandomMove();
      return;
    }

    // 85% chance to play strategically
    _makeStrategicMove(random, winChance: 100, blockChance: 90, centerChance: 80, cornerChance: 85);
  }

  // Strategic move with configurable chances
  void _makeStrategicMove(int random, {
    required int winChance,
    required int blockChance,
    required int centerChance,
    required int cornerChance,
  }) {
    // Always try to win first (bot should win when it can)
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
        if (board[i][j] == Player.none) {
          board[i][j] = botPlayer;
          if (_checkWinCondition(botPlayer)) {
            board[i][j] = Player.none; // Reset
            makeMove(i, j); // Make the winning move
            return;
          }
          board[i][j] = Player.none; // Reset
        }
      }
    }

    // Try to block human from winning (based on difficulty)
    if (random < blockChance) {
      for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
          if (board[i][j] == Player.none) {
            board[i][j] = humanPlayer;
            if (_checkWinCondition(humanPlayer)) {
              board[i][j] = Player.none; // Reset
              makeMove(i, j); // Block the human
              return;
            }
            board[i][j] = Player.none; // Reset
          }
        }
      }
    }

    // Take center if available (based on difficulty)
    if (board[1][1] == Player.none && random < centerChance) {
      makeMove(1, 1);
      return;
    }

    // Take corners strategically (based on difficulty)
    if (random < cornerChance) {
      List<List<int>> corners = [[0, 0], [0, 2], [2, 0], [2, 2]];
      corners.shuffle(); // Randomize corner selection
      for (var corner in corners) {
        if (board[corner[0]][corner[1]] == Player.none) {
          makeMove(corner[0], corner[1]);
          return;
        }
      }
    }

    // Take any available spot (fallback)
    _makeRandomMove();
  }

  // Make a random move (used for bot mistakes)
  void _makeRandomMove() {
    List<List<int>> availableMoves = [];
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
        if (board[i][j] == Player.none) {
          availableMoves.add([i, j]);
        }
      }
    }

    if (availableMoves.isNotEmpty) {
      // Use current time as seed for randomness
      final randomIndex = DateTime.now().millisecondsSinceEpoch % availableMoves.length;
      final move = availableMoves[randomIndex];
      makeMove(move[0], move[1]);
    }
  }

  // Check if a player has won (helper for bot AI)
  bool _checkWinCondition(Player player) {
    // Check rows
    for (int i = 0; i < 3; i++) {
      if (board[i][0] == player && board[i][1] == player && board[i][2] == player) {
        return true;
      }
    }

    // Check columns
    for (int i = 0; i < 3; i++) {
      if (board[0][i] == player && board[1][i] == player && board[2][i] == player) {
        return true;
      }
    }

    // Check diagonals
    if (board[0][0] == player && board[1][1] == player && board[2][2] == player) {
      return true;
    }

    if (board[0][2] == player && board[1][1] == player && board[2][0] == player) {
      return true;
    }

    return false;
  }

  // Check if it's bot's turn
  bool isBotTurn() {
    return gameMode == GameMode.bot && currentPlayer == botPlayer && gameState == GameState.playing;
  }

  // Get game mode display text
  String getGameModeText() {
    if (gameMode == GameMode.bot) {
      String difficultyText = '';
      switch (botDifficulty) {
        case BotDifficulty.easy:
          difficultyText = 'सजिलो';
          break;
        case BotDifficulty.medium:
          difficultyText = 'मध्यम';
          break;
        case BotDifficulty.hard:
          difficultyText = 'कठिन';
          break;
      }
      return 'बोट विरुद्ध ($difficultyText)';
    }
    return 'साथी विरुद्ध';
  }

  // Get difficulty display text
  String getDifficultyText() {
    switch (botDifficulty) {
      case BotDifficulty.easy:
        return 'सजिलो';
      case BotDifficulty.medium:
        return 'मध्यम';
      case BotDifficulty.hard:
        return 'कठिन';
    }
  }
}
