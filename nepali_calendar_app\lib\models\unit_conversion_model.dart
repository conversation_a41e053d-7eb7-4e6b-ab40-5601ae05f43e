class UnitType {
  final String name;
  final String nepaliName;
  final List<Unit> units;

  UnitType({
    required this.name,
    required this.nepaliName,
    required this.units,
  });
}

class Unit {
  final String name;
  final String nepaliName;
  final String symbol;
  final double conversionFactor; // Factor to convert to base unit (meter for length, sq.meter for area)

  Unit({
    required this.name,
    required this.nepaliName,
    required this.symbol,
    required this.conversionFactor,
  });
}

class ConversionResult {
  final double value;
  final Unit unit;
  final String? municipalFormat; // For Nepali area units

  ConversionResult({
    required this.value,
    required this.unit,
    this.municipalFormat,
  });

  String get displayValue {
    if (value == value.toInt()) {
      return value.toInt().toString();
    } else {
      return value.toStringAsFixed(4).replaceAll(RegExp(r'0*$'), '').replaceAll(RegExp(r'\.$'), '');
    }
  }

  String get fullDisplay {
    String result = '$displayValue ${unit.symbol}';
    if (municipalFormat != null && municipalFormat!.isNotEmpty) {
      result += ' ($municipalFormat)';
    }
    return result;
  }
}

class UnitConversionData {
  static final List<UnitType> unitTypes = [
    // Length Units
    UnitType(
      name: 'Length',
      nepaliName: 'लम्बाइ',
      units: [
        Unit(name: 'Meter', nepaliName: 'मिटर', symbol: 'm', conversionFactor: 1.0),
        Unit(name: 'Centimeter', nepaliName: 'सेन्टिमिटर', symbol: 'cm', conversionFactor: 0.01),
        Unit(name: 'Millimeter', nepaliName: 'मिलिमिटर', symbol: 'mm', conversionFactor: 0.001),
        Unit(name: 'Kilometer', nepaliName: 'किलोमिटर', symbol: 'km', conversionFactor: 1000.0),
        Unit(name: 'Feet', nepaliName: 'फिट', symbol: 'ft', conversionFactor: 0.3048),
        Unit(name: 'Inch', nepaliName: 'इन्च', symbol: 'in', conversionFactor: 0.0254),
        Unit(name: 'Yard', nepaliName: 'गज', symbol: 'yd', conversionFactor: 0.9144),
        Unit(name: 'Miles', nepaliName: 'माइल', symbol: 'mi', conversionFactor: 1609.344),
      ],
    ),
    
    // Area Units
    UnitType(
      name: 'Area',
      nepaliName: 'क्षेत्रफल',
      units: [
        Unit(name: 'Square Meter', nepaliName: 'वर्ग मिटर', symbol: 'm²', conversionFactor: 1.0),
        Unit(name: 'Square Feet', nepaliName: 'वर्ग फिट', symbol: 'ft²', conversionFactor: 0.09290304),
        Unit(name: 'Square Inch', nepaliName: 'वर्ग इन्च', symbol: 'in²', conversionFactor: 0.0006452),
        Unit(name: 'Hectare', nepaliName: 'हेक्टर', symbol: 'ha', conversionFactor: 10000.0),
        Unit(name: 'Acre', nepaliName: 'एकर', symbol: 'ac', conversionFactor: 4046.856),
        Unit(name: 'Square Miles', nepaliName: 'वर्ग माइल', symbol: 'mi²', conversionFactor: 2589988.0),
        
        // Nepali Area Units
        Unit(name: 'Ropani', nepaliName: 'रोपनी', symbol: 'रो.', conversionFactor: 508.73704704),
        Unit(name: 'Ana', nepaliName: 'आना', symbol: 'आ.', conversionFactor: 31.79605175),
        Unit(name: 'Paisa', nepaliName: 'पैसा', symbol: 'पै.', conversionFactor: 7.949012938),
        Unit(name: 'Dam', nepaliName: 'दाम', symbol: 'दा.', conversionFactor: 1.98725409),
        Unit(name: 'Bigha', nepaliName: 'बिघा', symbol: 'बि.', conversionFactor: 6772.631616),
        Unit(name: 'Kattha', nepaliName: 'कठ्ठा', symbol: 'क.', conversionFactor: 338.6315808),
        Unit(name: 'Dhur', nepaliName: 'धुर', symbol: 'धु.', conversionFactor: 16.93157904),
        Unit(name: 'Mato Muri', nepaliName: 'माटो मुरी', symbol: 'मा.मु.', conversionFactor: 127.184207),
      ],
    ),
    
    // Weight Units
    UnitType(
      name: 'Weight',
      nepaliName: 'तौल',
      units: [
        Unit(name: 'Kilogram', nepaliName: 'किलोग्राम', symbol: 'kg', conversionFactor: 1.0),
        Unit(name: 'Gram', nepaliName: 'ग्राम', symbol: 'g', conversionFactor: 0.001),
        Unit(name: 'Pound', nepaliName: 'पाउन्ड', symbol: 'lb', conversionFactor: 0.453592),
        Unit(name: 'Ounce', nepaliName: 'औंस', symbol: 'oz', conversionFactor: 0.0283495),
        Unit(name: 'Ton', nepaliName: 'टन', symbol: 't', conversionFactor: 1000.0),
        
        // Nepali Weight Units
        Unit(name: 'Mana', nepaliName: 'माना', symbol: 'मा.', conversionFactor: 0.6),
        Unit(name: 'Pathi', nepaliName: 'पाथी', symbol: 'पा.', conversionFactor: 2.4),
        Unit(name: 'Dharni', nepaliName: 'धार्नी', symbol: 'ध.', conversionFactor: 2.4),
        Unit(name: 'Ser', nepaliName: 'सेर', symbol: 'से.', conversionFactor: 0.933),
      ],
    ),
    
    // Volume Units
    UnitType(
      name: 'Volume',
      nepaliName: 'आयतन',
      units: [
        Unit(name: 'Liter', nepaliName: 'लिटर', symbol: 'L', conversionFactor: 1.0),
        Unit(name: 'Milliliter', nepaliName: 'मिलिलिटर', symbol: 'mL', conversionFactor: 0.001),
        Unit(name: 'Gallon', nepaliName: 'ग्यालन', symbol: 'gal', conversionFactor: 3.78541),
        Unit(name: 'Cubic Meter', nepaliName: 'घन मिटर', symbol: 'm³', conversionFactor: 1000.0),
        Unit(name: 'Cubic Feet', nepaliName: 'घन फिट', symbol: 'ft³', conversionFactor: 28.3168),
        
        // Nepali Volume Units
        Unit(name: 'Mana', nepaliName: 'माना', symbol: 'मा.', conversionFactor: 0.6),
        Unit(name: 'Pathi', nepaliName: 'पाथी', symbol: 'पा.', conversionFactor: 2.4),
      ],
    ),
  ];

  static UnitType? getUnitTypeByName(String name) {
    try {
      return unitTypes.firstWhere((type) => type.name == name || type.nepaliName == name);
    } catch (e) {
      return null;
    }
  }

  static Unit? getUnitByName(String unitTypeName, String unitName) {
    final unitType = getUnitTypeByName(unitTypeName);
    if (unitType == null) return null;
    
    try {
      return unitType.units.firstWhere(
        (unit) => unit.name == unitName || unit.nepaliName == unitName
      );
    } catch (e) {
      return null;
    }
  }
}
