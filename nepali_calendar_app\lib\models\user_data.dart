import 'package:nepali_utils/nepali_utils.dart';

class UserData {
  final String id;
  final String name;
  final String gender;
  final NepaliDateTime birthDateBS; // Nepali BS date
  final DateTime birthDateAD; // Converted AD date
  final String birthTime; // HH:MM format
  final String birthPlace;
  final String district;
  final double latitude;
  final double longitude;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserData({
    required this.id,
    required this.name,
    required this.gender,
    required this.birthDateBS,
    required this.birthDateAD,
    required this.birthTime,
    required this.birthPlace,
    required this.district,
    required this.latitude,
    required this.longitude,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert to JSON for local storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gender': gender,
      'birthDateBS': {
        'year': birthDateBS.year,
        'month': birthDateBS.month,
        'day': birthDateBS.day,
      },
      'birthDateAD': birthDateAD.toIso8601String(),
      'birthTime': birthTime,
      'birthPlace': birthPlace,
      'district': district,
      'latitude': latitude,
      'longitude': longitude,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Create from JSON
  factory UserData.fromJson(Map<String, dynamic> json) {
    final bsDate = json['birthDateBS'];
    return UserData(
      id: json['id'],
      name: json['name'],
      gender: json['gender'],
      birthDateBS: NepaliDateTime(
        bsDate['year'],
        bsDate['month'],
        bsDate['day'],
      ),
      birthDateAD: DateTime.parse(json['birthDateAD']),
      birthTime: json['birthTime'],
      birthPlace: json['birthPlace'],
      district: json['district'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  // Create copy with updated fields
  UserData copyWith({
    String? name,
    String? gender,
    NepaliDateTime? birthDateBS,
    DateTime? birthDateAD,
    String? birthTime,
    String? birthPlace,
    String? district,
    double? latitude,
    double? longitude,
  }) {
    return UserData(
      id: id,
      name: name ?? this.name,
      gender: gender ?? this.gender,
      birthDateBS: birthDateBS ?? this.birthDateBS,
      birthDateAD: birthDateAD ?? this.birthDateAD,
      birthTime: birthTime ?? this.birthTime,
      birthPlace: birthPlace ?? this.birthPlace,
      district: district ?? this.district,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  // Get formatted date for API (DD/MM/YYYY)
  String get formattedDateForAPI {
    return '${birthDateAD.day.toString().padLeft(2, '0')}/${birthDateAD.month.toString().padLeft(2, '0')}/${birthDateAD.year}';
  }

  // Get formatted time for API (HH:MM)
  String get formattedTimeForAPI {
    return birthTime;
  }

  // Get timezone for Nepal
  String get timezoneForAPI {
    return '5.75'; // Nepal timezone UTC+5:45
  }

  // Get display name with birth info
  String get displayName {
    return '$name (${birthDateBS.year}/${birthDateBS.month}/${birthDateBS.day})';
  }

  // Get age in years
  int get ageInYears {
    final now = DateTime.now();
    int age = now.year - birthDateAD.year;
    if (now.month < birthDateAD.month || 
        (now.month == birthDateAD.month && now.day < birthDateAD.day)) {
      age--;
    }
    return age;
  }

  @override
  String toString() {
    return 'UserData(id: $id, name: $name, birthDate: ${birthDateBS.year}/${birthDateBS.month}/${birthDateBS.day}, district: $district)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Helper class for date conversion
class DateConverter {
  // Convert Nepali BS date to English AD date
  static DateTime convertBStoAD(NepaliDateTime bsDate) {
    try {
      // Use nepali_utils package for accurate conversion
      return bsDate.toDateTime();
    } catch (e) {
      // Fallback: approximate conversion if exact conversion fails
      // This is a rough approximation and should be replaced with accurate conversion
      final approximateYear = bsDate.year - 57; // Rough BS to AD conversion
      return DateTime(approximateYear, bsDate.month, bsDate.day);
    }
  }

  // Convert English AD date to Nepali BS date
  static NepaliDateTime convertADtoBS(DateTime adDate) {
    try {
      return NepaliDateTime.fromDateTime(adDate);
    } catch (e) {
      // Fallback: approximate conversion
      final approximateYear = adDate.year + 57; // Rough AD to BS conversion
      return NepaliDateTime(approximateYear, adDate.month, adDate.day);
    }
  }

  // Validate if a BS date is valid
  static bool isValidBSDate(int year, int month, int day) {
    try {
      NepaliDateTime(year, month, day);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get current Nepali date
  static NepaliDateTime getCurrentNepaliDate() {
    return NepaliDateTime.now();
  }

  // Format Nepali date for display
  static String formatNepaliDate(NepaliDateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  // Format English date for API
  static String formatEnglishDateForAPI(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

// Gender options
class Gender {
  static const String male = 'पुरुष';
  static const String female = 'महिला';
  static const String other = 'अन्य';

  static List<String> get all => [male, female, other];

  static String getEnglish(String nepali) {
    switch (nepali) {
      case male:
        return 'Male';
      case female:
        return 'Female';
      case other:
        return 'Other';
      default:
        return 'Male';
    }
  }
}
