class WeatherData {
  final String cityName;
  final String region;
  final double tempC;
  final double feelsLikeC;
  final String condition;
  final String conditionIcon;
  final int humidity;
  final double windKph;
  final String windDir;
  final double pressureMb;
  final double visKm;
  final int uv;
  final DateTime lastUpdated;

  WeatherData({
    required this.cityName,
    required this.region,
    required this.tempC,
    required this.feelsLikeC,
    required this.condition,
    required this.conditionIcon,
    required this.humidity,
    required this.windKph,
    required this.windDir,
    required this.pressureMb,
    required this.visKm,
    required this.uv,
    required this.lastUpdated,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    final location = json['location'];
    final current = json['current'];
    
    return WeatherData(
      cityName: location['name'] ?? '',
      region: location['region'] ?? '',
      tempC: (current['temp_c'] ?? 0).toDouble(),
      feelsLikeC: (current['feelslike_c'] ?? 0).toDouble(),
      condition: current['condition']['text'] ?? '',
      conditionIcon: current['condition']['icon'] ?? '',
      humidity: current['humidity'] ?? 0,
      windKph: (current['wind_kph'] ?? 0).toDouble(),
      windDir: current['wind_dir'] ?? '',
      pressureMb: (current['pressure_mb'] ?? 0).toDouble(),
      visKm: (current['vis_km'] ?? 0).toDouble(),
      uv: (current['uv'] ?? 0).toInt(),
      lastUpdated: DateTime.parse(current['last_updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  String get temperatureDisplay => '${tempC.round()}°C';
  String get feelsLikeDisplay => '${feelsLikeC.round()}°C';
  String get windDisplay => '${windKph.round()} km/h';
  String get pressureDisplay => '${pressureMb.round()} mb';
  String get visibilityDisplay => '${visKm.round()} km';
  String get humidityDisplay => '$humidity%';
  String get uvDisplay => uv.toString();
  
  String get conditionNepali {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return 'घामिलो';
      case 'partly cloudy':
        return 'आंशिक बादल';
      case 'cloudy':
      case 'overcast':
        return 'बादल';
      case 'mist':
      case 'fog':
        return 'कुहिरो';
      case 'patchy rain possible':
      case 'light rain':
        return 'हल्का वर्षा';
      case 'moderate rain':
        return 'मध्यम वर्षा';
      case 'heavy rain':
        return 'भारी वर्षा';
      case 'thundery outbreaks possible':
      case 'thunderstorm':
        return 'आँधी';
      case 'snow':
        return 'हिउँ';
      default:
        return condition;
    }
  }
}

class ForecastDay {
  final DateTime date;
  final double maxTempC;
  final double minTempC;
  final String condition;
  final String conditionIcon;
  final double chanceOfRain;
  final double maxWindKph;
  final double avgHumidity;

  ForecastDay({
    required this.date,
    required this.maxTempC,
    required this.minTempC,
    required this.condition,
    required this.conditionIcon,
    required this.chanceOfRain,
    required this.maxWindKph,
    required this.avgHumidity,
  });

  factory ForecastDay.fromJson(Map<String, dynamic> json) {
    final day = json['day'];
    
    return ForecastDay(
      date: DateTime.parse(json['date']),
      maxTempC: (day['maxtemp_c'] ?? 0).toDouble(),
      minTempC: (day['mintemp_c'] ?? 0).toDouble(),
      condition: day['condition']['text'] ?? '',
      conditionIcon: day['condition']['icon'] ?? '',
      chanceOfRain: (day['daily_chance_of_rain'] ?? 0).toDouble(),
      maxWindKph: (day['maxwind_kph'] ?? 0).toDouble(),
      avgHumidity: (day['avghumidity'] ?? 0).toDouble(),
    );
  }

  String get maxTempDisplay => '${maxTempC.round()}°';
  String get minTempDisplay => '${minTempC.round()}°';
  String get tempRangeDisplay => '${maxTempC.round()}°/${minTempC.round()}°';
  String get rainChanceDisplay => '${chanceOfRain.round()}%';
  
  String get dayName {
    final nepaliDays = [
      'सोमबार', 'मंगलबार', 'बुधबार', 'बिहिबार', 'शुक्रबार', 'शनिबार', 'आइतबार'
    ];
    return nepaliDays[date.weekday - 1];
  }
  
  String get shortDayName {
    final shortDays = ['सोम', 'मंगल', 'बुध', 'बिहि', 'शुक्र', 'शनि', 'आइत'];
    return shortDays[date.weekday - 1];
  }
  
  String get dateDisplay {
    final nepaliMonths = [
      'जनवरी', 'फेब्रुअरी', 'मार्च', 'अप्रिल', 'मे', 'जुन',
      'जुलाई', 'अगस्ट', 'सेप्टेम्बर', 'अक्टोबर', 'नोभेम्बर', 'डिसेम्बर'
    ];
    return '${date.day} ${nepaliMonths[date.month - 1]}';
  }
  
  String get conditionNepali {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return 'घामिलो';
      case 'partly cloudy':
        return 'आंशिक बादल';
      case 'cloudy':
      case 'overcast':
        return 'बादल';
      case 'mist':
      case 'fog':
        return 'कुहिरो';
      case 'patchy rain possible':
      case 'light rain':
        return 'हल्का वर्षा';
      case 'moderate rain':
        return 'मध्यम वर्षा';
      case 'heavy rain':
        return 'भारी वर्षा';
      case 'thundery outbreaks possible':
      case 'thunderstorm':
        return 'आँधी';
      case 'snow':
        return 'हिउँ';
      default:
        return condition;
    }
  }
}

class WeatherForecast {
  final WeatherData current;
  final List<ForecastDay> forecast;

  WeatherForecast({
    required this.current,
    required this.forecast,
  });

  factory WeatherForecast.fromJson(Map<String, dynamic> json) {
    final forecastDays = (json['forecast']['forecastday'] as List)
        .map((day) => ForecastDay.fromJson(day))
        .take(3) // Only 3 days
        .toList();

    return WeatherForecast(
      current: WeatherData.fromJson(json),
      forecast: forecastDays,
    );
  }
}

class NepalCity {
  final String name;
  final String nepaliName;

  NepalCity({
    required this.name,
    required this.nepaliName,
  });
}

class LocationResult {
  final String name;
  final String region;
  final String country;
  final double lat;
  final double lon;
  final String url;

  LocationResult({
    required this.name,
    required this.region,
    required this.country,
    required this.lat,
    required this.lon,
    required this.url,
  });

  factory LocationResult.fromJson(Map<String, dynamic> json) {
    return LocationResult(
      name: json['name'] ?? '',
      region: json['region'] ?? '',
      country: json['country'] ?? '',
      lat: (json['lat'] ?? 0).toDouble(),
      lon: (json['lon'] ?? 0).toDouble(),
      url: json['url'] ?? '',
    );
  }

  String get displayName {
    if (region.isNotEmpty && region != name) {
      return '$name, $region, $country';
    } else {
      return '$name, $country';
    }
  }

  String get searchQuery => '$name,$region,$country';
}

class NepalCities {
  static final List<NepalCity> cities = [
    NepalCity(name: 'Kathmandu', nepaliName: 'काठमाडौं'),
    NepalCity(name: 'Pokhara', nepaliName: 'पोखरा'),
    NepalCity(name: 'Lalitpur', nepaliName: 'ललितपुर'),
    NepalCity(name: 'Bhaktapur', nepaliName: 'भक्तपुर'),
    NepalCity(name: 'Biratnagar', nepaliName: 'विराटनगर'),
    NepalCity(name: 'Birgunj', nepaliName: 'वीरगञ्ज'),
    NepalCity(name: 'Dharan', nepaliName: 'धरान'),
    NepalCity(name: 'Butwal', nepaliName: 'बुटवल'),
    NepalCity(name: 'Hetauda', nepaliName: 'हेटौडा'),
    NepalCity(name: 'Dhangadhi', nepaliName: 'धनगढी'),
    NepalCity(name: 'Janakpur', nepaliName: 'जनकपुर'),
    NepalCity(name: 'Nepalgunj', nepaliName: 'नेपालगञ्ज'),
    NepalCity(name: 'Bharatpur', nepaliName: 'भरतपुर'),
    NepalCity(name: 'Lumbini', nepaliName: 'लुम्बिनी'),
    NepalCity(name: 'Chitwan', nepaliName: 'चितवन'),
  ];

  static NepalCity? findByName(String name) {
    try {
      return cities.firstWhere(
        (city) => city.name.toLowerCase() == name.toLowerCase() ||
                  city.nepaliName == name,
      );
    } catch (e) {
      return null;
    }
  }

  static List<String> get nepaliNames => cities.map((c) => c.nepaliName).toList();
}
