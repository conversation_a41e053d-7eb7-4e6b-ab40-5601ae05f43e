import 'dart:async';
import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:flutter/services.dart';
import 'models/calendar_event.dart';
// Firebase removed

class NepaliCalendarPage extends StatefulWidget {
  const NepaliCalendarPage({Key? key}) : super(key: key);

  @override
  State<NepaliCalendarPage> createState() => _NepaliCalendarPageState();
}

class _NepaliCalendarPageState extends State<NepaliCalendarPage> {
  late NepaliDateTime currentNepaliDate;
  late DateTime currentEnglishDate;
  NepaliDateTime? selectedDate; // Make nullable - no auto selection
  late NepaliDateTime todayNepaliDate;
  Timer? _dateUpdateTimer;
  // Firebase removed
  Map<String, List<CalendarEvent>> monthEvents = {};
  bool isLoading = false;

  // Bottom navigation state
  int _selectedIndex = 1; // Calendar page is index 1
  bool _isNepaliLanguage = true;

  @override
  void initState() {
    super.initState();
    currentEnglishDate = DateTime.now();
    currentNepaliDate = currentEnglishDate.toNepaliDateTime();
    todayNepaliDate = currentNepaliDate; // Store today's date
    selectedDate = null; // No date selected by default
    _loadEventsForCurrentMonth();
    _startDateUpdateTimer(); // Start real-time date updates
  }

  @override
  void dispose() {
    _dateUpdateTimer?.cancel();
    super.dispose();
  }

  void _loadEventsForCurrentMonth() {
    // Instant loading - no delays, no async operations
    _loadSampleData();
    setState(() {
      isLoading = false;
    });
  }

  void _loadSampleData() {
    setState(() {
      monthEvents.clear();
      _loadTithiData();
    });
  }

  void _loadTithiData() {
    // Load festival data based on current month and year
    Map<String, List<CalendarEvent>> festivalData = _getFestivalsForMonth(currentNepaliDate.year, currentNepaliDate.month);

    festivalData.forEach((day, events) {
      monthEvents[day] = events;
    });
  }

  void _goToPreviousMonth() {
    setState(() {
      if (currentNepaliDate.month == 1) {
        currentNepaliDate = NepaliDateTime(currentNepaliDate.year - 1, 12, 1);
      } else {
        currentNepaliDate = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month - 1, 1);
      }
      currentEnglishDate = currentNepaliDate.toDateTime();
      // Don't auto-select any date when navigating months
      // User must manually select a date if they want
    });
    _loadEventsForCurrentMonth();
  }

  void _goToNextMonth() {
    setState(() {
      if (currentNepaliDate.month == 12) {
        currentNepaliDate = NepaliDateTime(currentNepaliDate.year + 1, 1, 1);
      } else {
        currentNepaliDate = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month + 1, 1);
      }
      currentEnglishDate = currentNepaliDate.toDateTime();
      // Don't auto-select any date when navigating months
      // User must manually select a date if they want
    });
    _loadEventsForCurrentMonth();
  }

  void _goToToday() {
    setState(() {
      currentNepaliDate = todayNepaliDate;
      currentEnglishDate = todayNepaliDate.toDateTime();
      selectedDate = todayNepaliDate; // Select today's date when going to today
    });
    _loadEventsForCurrentMonth();
  }

  void _startDateUpdateTimer() {
    // Check for date changes every minute
    _dateUpdateTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (mounted) {
        final now = DateTime.now();
        final newNepaliDate = now.toNepaliDateTime();

        // Check if date has changed
        if (newNepaliDate.day != todayNepaliDate.day ||
            newNepaliDate.month != todayNepaliDate.month ||
            newNepaliDate.year != todayNepaliDate.year) {
          setState(() {
            todayNepaliDate = newNepaliDate;
            // If user is viewing current month, update the display
            if (currentNepaliDate.month == newNepaliDate.month &&
                currentNepaliDate.year == newNepaliDate.year) {
              currentEnglishDate = now;
              currentNepaliDate = newNepaliDate;
            }
          });
        }
      }
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('नेपाली पात्रो', style: TextStyle(color: Colors.white)),
        backgroundColor: const Color(0xFF3F51B5),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu, color: Colors.white),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        actions: [
          // Today button to go back to current date
          TextButton.icon(
            icon: const Icon(Icons.today, color: Colors.white, size: 18),
            label: const Text('आज', style: TextStyle(color: Colors.white, fontSize: 14)),
            onPressed: _goToToday,
          ),
        ],
      ),
      drawer: _buildDrawer(),
      backgroundColor: Colors.blue[50], // Light blue background
      body: Column(
        children: [
          // Calendar Header - Compact
          Container(
            width: double.infinity,
            color: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4), // FURTHER REDUCED from 8 to 4
            child: Column(
              children: [
                // Navigation and Month Row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Previous Month Button
                    IconButton(
                      icon: const Icon(Icons.chevron_left, color: Colors.white, size: 30),
                      onPressed: _goToPreviousMonth,
                    ),
                    // Month and Year Info
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            _getNepaliMonthName(currentNepaliDate.month),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20, // Reduced from 24
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'वि.सं. ${_convertToNepaliNumerals(currentNepaliDate.year.toString())}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14, // Reduced from 16
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 6), // Reduced spacing
                              Text(
                                '${_getEnglishMonthName(currentEnglishDate.month)} ${currentEnglishDate.year}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12, // Reduced from 14
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Next Month Button
                    IconButton(
                      icon: const Icon(Icons.chevron_right, color: Colors.white, size: 30),
                      onPressed: _goToNextMonth,
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Professional Days of Week Header
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue[700]!, // Deep Blue
                  Colors.blue[800]!, // Darker Blue
                  Colors.blue[900]!, // Very Dark Blue
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue[700]!.withOpacity(0.4),
                  blurRadius: 15,
                  spreadRadius: 2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 6), // REDUCED from 12 to 6
              child: Column(
                children: [
                  // Nepali Day Names (Large)
                  Row(
                    children: [
                      _buildProDayHeader('आइत', 'SUN', false),
                      _buildProDayHeader('सोम', 'MON', false),
                      _buildProDayHeader('मंगल', 'TUE', false),
                      _buildProDayHeader('बुध', 'WED', false),
                      _buildProDayHeader('बिहि', 'THU', false),
                      _buildProDayHeader('शुक्र', 'FRI', false),
                      _buildProDayHeader('शनि', 'SAT', true), // Saturday highlighted
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Professional Calendar Grid
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.grey[50]!,
                  Colors.grey[100]!,
                  Colors.grey[200]!,
                ],
              ),
            ),
            child: isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
                    strokeWidth: 3,
                  ),
                )
              : _buildProfessionalCalendarGrid(),
          ),

          // Selected Date Information - Right under calendar
          Container(
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[700],
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.2),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: selectedDate != null
                                ? (selectedDate!.day == todayNepaliDate.day &&
                                   selectedDate!.month == todayNepaliDate.month &&
                                   selectedDate!.year == todayNepaliDate.year
                                   ? 'आजको मिति: ${_convertToNepaliNumerals(selectedDate!.day.toString())} ${_getNepaliMonthName(selectedDate!.month)} ${_convertToNepaliNumerals(selectedDate!.year.toString())} '
                                   : 'रोजेको मिति: ${_convertToNepaliNumerals(selectedDate!.day.toString())} ${_getNepaliMonthName(selectedDate!.month)} ${_convertToNepaliNumerals(selectedDate!.year.toString())} ')
                                : 'आजको मिति: ${_convertToNepaliNumerals(todayNepaliDate.day.toString())} ${_getNepaliMonthName(todayNepaliDate.month)} ${_convertToNepaliNumerals(todayNepaliDate.year.toString())} ',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 15, // REDUCED from 16 to 15
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            TextSpan(
                              text: selectedDate != null
                                ? '(${selectedDate!.toDateTime().day} ${_getEnglishMonthName(selectedDate!.toDateTime().month)} ${selectedDate!.toDateTime().year})'
                                : '(${todayNepaliDate.toDateTime().day} ${_getEnglishMonthName(todayNepaliDate.toDateTime().month)} ${todayNepaliDate.toDateTime().year})',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                                fontSize: 12, // SMALL TEXT for English date
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Combined Tithi and Festival info on same line with comma
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          _buildTithiAndFestivalText(selectedDate ?? todayNepaliDate),
                          style: TextStyle(
                            color: Colors.yellow[200]!, // LIGHT YELLOW for festival details (back to original)
                            fontSize: 15, // REDUCED from 16 to 15
                            fontWeight: FontWeight.bold, // BOLD text
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
            ),
          ),

          // Spacer to push content up and avoid bottom UI
          const Spacer(),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  // Firebase method removed

  Widget _buildProDayHeader(String nepaliDay, String englishDay, bool isSaturday) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6),
        decoration: BoxDecoration(
          border: Border(
            right: BorderSide(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            Text(
              nepaliDay,
              style: TextStyle(
                color: isSaturday ? Colors.yellow[300] : Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),
            Text(
              englishDay,
              style: TextStyle(
                color: isSaturday ? Colors.yellow[200] : Colors.white.withOpacity(0.8),
                fontSize: 10,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayHeader(String nepaliDay, String englishDay, Color bgColor) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: bgColor,
          border: Border.all(color: Colors.white, width: 0.5),
        ),
        child: Column(
          children: [
            Text(
              nepaliDay,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              englishDay,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalCalendarGrid() {
    // Get first day of current Nepali month
    NepaliDateTime firstDayOfMonth = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month, 1);
    DateTime firstDayEnglish = firstDayOfMonth.toDateTime();

    // Get number of days in current month
    int daysInMonth = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month + 1, 1)
        .subtract(const Duration(days: 1))
        .day;

    // Calculate starting weekday (0 = Sunday, 6 = Saturday)
    int startingWeekday = firstDayEnglish.weekday % 7;

    // Calculate how many weeks we actually need (not always 6)
    int totalCells = startingWeekday + daysInMonth;
    int weeksNeeded = (totalCells / 7).ceil();

    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(2), // Minimal padding
        child: Column(
          children: [
            // Build only the necessary calendar rows - no extra empty lines
            for (int week = 0; week < weeksNeeded; week++)
              Container(
                height: 85, // Height for festival text display
                child: Row(
                  children: [
                    for (int day = 0; day < 7; day++)
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Colors.grey.withOpacity(0.1),
                              width: 0.5,
                            ),
                          ),
                          child: _buildCompactDateCell(week, day, startingWeekday, daysInMonth),
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactDateCell(int week, int day, int startingWeekday, int daysInMonth) {
    int index = week * 7 + day;
    int dayNumber = index - startingWeekday + 1;

    // Always show a cell, even if empty
    bool isEmpty = dayNumber <= 0 || dayNumber > daysInMonth;
    bool isToday = !isEmpty && dayNumber == todayNepaliDate.day &&
                   currentNepaliDate.month == todayNepaliDate.month &&
                   currentNepaliDate.year == todayNepaliDate.year;
    bool isSelected = !isEmpty && selectedDate != null &&
                      dayNumber == selectedDate!.day &&
                      currentNepaliDate.month == selectedDate!.month &&
                      currentNepaliDate.year == selectedDate!.year;
    bool isManuallySelected = isSelected && !isToday;
    bool isSaturday = day == 6;
    bool isPublicHoliday = !isEmpty && _isPublicHoliday(currentNepaliDate.year, currentNepaliDate.month, dayNumber);
    bool hasEvent = !isEmpty && monthEvents.containsKey(dayNumber.toString()) && monthEvents[dayNumber.toString()]!.isNotEmpty;

    // Convert to English date for display (only if not empty)
    DateTime? englishDate;
    if (!isEmpty) {
      NepaliDateTime nepaliDate = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month, dayNumber);
      englishDate = nepaliDate.toDateTime();
    }

    return Container(
      height: 85, // FULL HEIGHT - large calendar boxes
      decoration: BoxDecoration(
        gradient: isEmpty
          ? LinearGradient(
              colors: [
                Colors.grey[100]!,
                Colors.grey[200]!,
              ],
            )
          : isToday
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.green[600]!,
                  Colors.green[700]!,
                ],
              )
            : isManuallySelected
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.blue[500]!,
                    Colors.blue[600]!,
                  ],
                )
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white,
                    Colors.grey[50]!,
                  ],
                ),
        // Grid lines for calendar cells
        border: Border.all(
          color: Colors.grey[300]!,
          width: 0.5,
        ),
        boxShadow: isEmpty
          ? []
          : isToday
            ? [
                BoxShadow(
                  color: Colors.green.withOpacity(0.4),
                  blurRadius: 6,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ]
            : isManuallySelected
              ? [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: 4,
                    spreadRadius: 1,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEmpty ? null : () {
            setState(() {
              selectedDate = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month, dayNumber);
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 3),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // Changed from spaceEvenly to spaceBetween
              crossAxisAlignment: CrossAxisAlignment.center, // Ensure center alignment
              children: [
                // Tithi above date - BOLD and LARGE - ALIGNED
                Container(
                  height: 20, // Fixed height for consistent alignment
                  alignment: Alignment.center, // Center alignment for consistent positioning
                  child: isEmpty ? null : Text(
                    _getTithiForDate(currentNepaliDate.year, currentNepaliDate.month, dayNumber),
                    style: TextStyle(
                      fontSize: 13, // INCREASED from 12 to 13
                      fontWeight: FontWeight.bold, // BOLD
                      color: isToday || isManuallySelected
                        ? Colors.white
                        : (isSaturday || isPublicHoliday)
                          ? Colors.red[700] // RED for Saturday AND public holidays
                          : Colors.black87,
                      height: 1.0, // Consistent line height
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Large Nepali Date - CENTERED
                if (!isEmpty)
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      _convertToNepaliNumerals(dayNumber.toString()),
                      style: TextStyle(
                        fontSize: 24, // LARGE for full height boxes
                        fontWeight: FontWeight.bold,
                        color: isToday || isManuallySelected
                          ? Colors.white
                          : (isSaturday || isPublicHoliday)
                            ? Colors.red[700] // RED for Saturday AND public holidays
                            : Colors.black87,
                        letterSpacing: 0.3,
                        height: 1.0,
                      ),
                    ),
                  ),

                // English date - CENTERED with consistent spacing
                if (!isEmpty && englishDate != null)
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      englishDate.day.toString(),
                      style: TextStyle(
                        fontSize: 11, // LARGER for full height boxes
                        fontWeight: FontWeight.w500,
                        color: isToday || isManuallySelected
                          ? Colors.white.withOpacity(0.8)
                          : (isSaturday || isPublicHoliday)
                            ? Colors.red[700] // RED for Saturday AND public holidays
                            : Colors.grey[600],
                        height: 1.0, // Consistent line height
                      ),
                    ),
                  ),

                // Festival text with small gap - better visibility
                if (hasEvent)
                  Container(
                    width: double.infinity, // Full width
                    margin: const EdgeInsets.only(top: 3), // Small gap so they don't touch
                    padding: const EdgeInsets.symmetric(horizontal: 1),
                    child: Text(
                      monthEvents[dayNumber.toString()]?.first.title ?? '', // Show actual event title
                      style: TextStyle(
                        fontSize: 10, // Larger for better visibility
                        fontWeight: FontWeight.w600,
                        color: isToday || isSelected
                          ? Colors.white
                          : (isSaturday || isPublicHoliday)
                            ? Colors.red[700] // RED for Saturday and public holidays
                            : Colors.blue[700], // BLUE for regular festival text
                        height: 1.0, // Normal line height
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2, // Allow 2 lines for text
                      overflow: TextOverflow.ellipsis, // Handle overflow gracefully
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfessionalDateCell(int nepaliDay, bool isToday, bool isSaturday, bool hasEvent) {
    // Convert to English date for display
    NepaliDateTime nepaliDate = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month, nepaliDay);
    DateTime englishDate = nepaliDate.toDateTime();
    bool isPublicHoliday = _isPublicHoliday(currentNepaliDate.year, currentNepaliDate.month, nepaliDay);

    return Container(
      decoration: BoxDecoration(
        gradient: isToday
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFD32F2F),
                const Color(0xFFB71C1C),
                const Color(0xFF8E0000),
              ],
            )
          : LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white,
                Colors.grey[50]!,
              ],
            ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isToday
            ? Colors.white.withOpacity(0.3)
            : (isSaturday || isPublicHoliday)
              ? Colors.red.withOpacity(0.3) // RED border for Saturday AND public holidays
              : Colors.grey.withOpacity(0.2),
          width: isToday ? 2 : 1,
        ),
        boxShadow: isToday
          ? [
              BoxShadow(
                color: const Color(0xFFD32F2F).withOpacity(0.4),
                blurRadius: 8,
                spreadRadius: 2,
                offset: const Offset(0, 3),
              ),
            ]
          : [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // Handle date tap
          },
          child: Padding(
            padding: const EdgeInsets.all(4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Extra Large Nepali Date
                Text(
                  _convertToNepaliNumerals(nepaliDay.toString()),
                  style: TextStyle(
                    fontSize: 28, // Extra large size
                    fontWeight: FontWeight.bold,
                    color: isToday
                      ? Colors.white
                      : (isSaturday || isPublicHoliday)
                        ? Colors.red[700] // RED for Saturday AND public holidays
                        : Colors.black87,
                    letterSpacing: 0.5,
                  ),
                ),
                // Small English Date
                Text(
                  englishDate.day.toString(),
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: isToday
                      ? Colors.white.withOpacity(0.8)
                      : Colors.grey[600],
                  ),
                ),
                // Event indicator
                if (hasEvent)
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: isToday ? Colors.white : Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    // Get first day of current Nepali month
    NepaliDateTime firstDayOfMonth = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month, 1);
    DateTime firstDayEnglish = firstDayOfMonth.toDateTime();

    // Get number of days in current month
    int daysInMonth = _getDaysInNepaliMonth(currentNepaliDate.year, currentNepaliDate.month);

    // Get starting weekday (0 = Sunday, 6 = Saturday)
    int startingWeekday = firstDayEnglish.weekday % 7;

    List<Widget> calendarCells = [];

    // Add empty cells for days before month starts
    for (int i = 0; i < startingWeekday; i++) {
      calendarCells.add(_buildEmptyCell());
    }

    // Add cells for each day of the month
    for (int day = 1; day <= daysInMonth; day++) {
      NepaliDateTime nepaliDay = NepaliDateTime(currentNepaliDate.year, currentNepaliDate.month, day);
      DateTime englishDay = nepaliDay.toDateTime();
      bool isSaturday = englishDay.weekday == DateTime.saturday;
      bool isToday = day == currentNepaliDate.day;

      bool isPublicHoliday = _isPublicHoliday(currentNepaliDate.year, currentNepaliDate.month, day);
      calendarCells.add(_buildDateCell(day, englishDay.day, isSaturday, isToday, isPublicHoliday));
    }

    return GridView.count(
      crossAxisCount: 7,
      childAspectRatio: 0.7, // FULL HEIGHT - taller boxes
      mainAxisSpacing: 2, // Small gap between rows
      crossAxisSpacing: 2, // Small gap between columns
      children: calendarCells,
    );
  }

  Widget _buildEmptyCell() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!, width: 0.5),
      ),
    );
  }

  Widget _buildDateCell(int nepaliDay, int englishDay, bool isSaturday, bool isToday, bool isPublicHoliday) {
    final dayKey = nepaliDay.toString();
    final dayEvents = monthEvents[dayKey] ?? [];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!, width: 0.5),
        color: isToday ? Colors.yellow[100] : Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(2.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Festival/Event info above date (if any)
            if (dayEvents.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                decoration: BoxDecoration(
                  color: _getEventColor(dayEvents.first.type),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Text(
                  dayEvents.first.title,
                  style: const TextStyle(
                    fontSize: 8,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 2),
            ],

            // Date numbers in center
            Column(
              children: [
                // Nepali Date Number
                Text(
                  _convertToNepaliNumber(nepaliDay),
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: (isSaturday || isPublicHoliday) ? Colors.red : Colors.black,
                  ),
                ),
                // English Date Number
                Text(
                  englishDay.toString(),
                  style: TextStyle(
                    fontSize: 9,
                    color: (isSaturday || isPublicHoliday) ? Colors.red : Colors.grey[600],
                  ),
                ),
              ],
            ),

            // Additional event info below date (if multiple events)
            if (dayEvents.length > 1) ...[
              const SizedBox(height: 2),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Text(
                  '+${dayEvents.length - 1} more',
                  style: TextStyle(
                    fontSize: 7,
                    color: Colors.blue[800],
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getEventColor(String type) {
    switch (type.toLowerCase()) {
      case 'festival':
        return Colors.red;
      case 'holiday':
        return Colors.green;
      case 'event':
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }

  String _convertToNepaliNumber(int number) {
    const nepaliDigits = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
    return number.toString().split('').map((digit) => nepaliDigits[int.parse(digit)]).join();
  }

  String _getNepaliMonthName(int month) {
    const months = [
      'बैशाख', 'जेठ', 'असार', 'साउन', 'भदौ', 'असोज',
      'कार्तिक', 'मंसिर', 'पुस', 'माघ', 'फागुन', 'चैत'
    ];
    return months[month - 1];
  }

  String _getEnglishMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  int _getDaysInNepaliMonth(int year, int month) {
    // Standard Nepali month lengths (approximate)
    const monthDays = [31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31];

    // For more accuracy, you could implement the actual Nepali calendar algorithm
    // This is a simplified version
    if (month >= 1 && month <= 12) {
      return monthDays[month - 1];
    }
    return 30; // Default fallback
  }

  String _convertToNepaliNumerals(String englishNumber) {
    const Map<String, String> numeralMap = {
      '0': '०',
      '1': '१',
      '2': '२',
      '3': '३',
      '4': '४',
      '5': '५',
      '6': '६',
      '7': '७',
      '8': '८',
      '9': '९',
    };

    String result = englishNumber;
    numeralMap.forEach((english, nepali) {
      result = result.replaceAll(english, nepali);
    });
    return result;
  }

  List<CalendarEvent> _getEventsForDate(NepaliDateTime date) {
    // Check if the date is in the current month being displayed
    if (date.month == currentNepaliDate.month && date.year == currentNepaliDate.year) {
      return monthEvents[date.day.toString()] ?? [];
    }
    return [];
  }

  String _getTithiForDate(int year, int month, int day) {
    // Get Tithi data for the specific date
    Map<String, String> tithiData = _getTithiForMonth(year, month);
    return tithiData[day.toString()] ?? '';
  }

  // Check if a date is a public holiday in Nepal BS 2082
  bool _isPublicHoliday(int year, int month, int day) {
    if (year == 2082) {
      switch (month) {
        case 1: // Baisakh
          return [1, 18, 29].contains(day);
        case 2: // Jestha
          return [15].contains(day);
        case 4: // Shrawan
          return [25, 31].contains(day);
        case 5: // Bhadra
          return [10, 15, 21, 30].contains(day);
        case 6: // Ashoj
          return [3, 6, 13, 14, 15, 16, 17, 18].contains(day);
        case 7: // Kartik
          return [3, 4, 5, 6, 7, 10].contains(day);
        case 8: // Mangsir
          return [17, 18].contains(day);
        case 9: // Poush
          return [10, 15].contains(day);
        case 10: // Magh
          return [1, 5, 9, 16].contains(day);
        case 11: // Falgun
          return [3, 6, 7, 18, 24].contains(day);
        case 12: // Chaitra
          return [1, 4].contains(day);
        default:
          return false;
      }
    }
    return false;
  }

  // Build combined Tithi and Festival text with comma separator
  String _buildTithiAndFestivalText(NepaliDateTime date) {
    final tithi = _getTithiForDate(date.year, date.month, date.day);
    final dayKey = date.day.toString();
    final monthFestivals = _getFestivalsForMonth(date.year, date.month);

    String result = tithi.isNotEmpty ? tithi : '';

    if (monthFestivals.containsKey(dayKey)) {
      final festivals = monthFestivals[dayKey]!;
      if (festivals.isNotEmpty) {
        final festival = festivals.first;
        if (result.isNotEmpty) {
          result += ', ${festival.title}';
        } else {
          result = festival.title;
        }
        if (festival.description.isNotEmpty) {
          result += ' - ${festival.description}';
        }
      }
    }

    return result.isNotEmpty ? result : 'कुनै विशेष जानकारी छैन';
  }

  Map<String, String> _getTithiForMonth(int year, int month) {
    if (year == 2082) {
      switch (month) {
        case 1: // Baisakh 2082 (बैशाख २०८२)
          return {
            '1': 'प्रतिपदा', '2': 'द्वितीया', '3': 'तृतीया', '4': 'चतुर्थी', '5': 'पञ्चमी',
            '6': 'षष्ठी', '7': 'सप्तमी', '8': 'अष्टमी', '9': 'नवमी', '10': 'दशमी',
            '11': 'एकादशी', '12': 'द्वादशी', '13': 'त्रयोदशी', '14': 'औंसी', '15': 'प्रतिपदा',
            '16': 'द्वितीया', '17': 'तृतीया', '18': 'चतुर्थी', '19': 'पञ्चमी', '20': 'षष्ठी',
            '21': 'सप्तमी', '22': 'अष्टमी', '23': 'नवमी', '24': 'दशमी', '25': 'एकादशी',
            '26': 'द्वादशी', '27': 'त्रयोदशी', '28': 'चतुर्दशी', '29': 'पूर्णिमा', '30': 'प्रतिपदा', '31': 'द्वितीया'
          };
        case 2: // Jestha 2082 (जेठ २०८२)
          return {
            '1': 'तृतीया', '2': 'चतुर्थी', '3': 'पञ्चमी', '4': 'षष्ठी', '5': 'सप्तमी',
            '6': 'अष्टमी', '7': 'नवमी', '8': 'दशमी', '9': 'एकादशी', '10': 'द्वादशी',
            '11': 'त्रयोदशी', '12': 'चतुर्दशी', '13': 'औंसी', '14': 'प्रतिपदा', '15': 'तृतीया',
            '16': 'चतुर्थी', '17': 'पञ्चमी', '18': 'षष्ठी', '19': 'सप्तमी', '20': 'अष्टमी',
            '21': 'नवमी', '22': 'दशमी', '23': 'एकादशी', '24': 'द्वादशी', '25': 'त्रयोदशी',
            '26': 'त्रयोदशी', '27': 'चतुर्दशी', '28': 'पूर्णिमा', '29': 'प्रतिपदा', '30': 'द्वितीया', '31': 'तृतीया', '32': 'चतुर्थी'
          };
        case 3: // Ashar 2082 (असार २०८२)
          return {
            '1': 'चतुर्थी', '2': 'पञ्चमी', '3': 'षष्ठी', '4': 'सप्तमी', '5': 'अष्टमी',
            '6': 'नवमी', '7': 'एकादशी', '8': 'द्वादशी', '9': 'त्रयोदशी', '10': 'चतुर्दशी',
            '11': 'औंसी', '12': 'प्रतिपदा', '13': 'द्वितीया', '14': 'तृतीया', '15': 'चतुर्थी',
            '16': 'पञ्चमी', '17': 'षष्ठी', '18': 'सप्तमी', '19': 'अष्टमी', '20': 'नवमी',
            '21': 'दशमी', '22': 'एकादशी', '23': 'द्वादशी', '24': 'त्रयोदशी', '25': 'चतुर्दशी',
            '26': 'पूर्णिमा', '27': 'प्रतिपदा', '28': 'द्वितीया', '29': 'तृतीया', '30': 'चतुर्थी', '31': 'पञ्चमी', '32': 'षष्ठी'
          };
        case 4: // Shrawan 2082 (श्रावण २०८२)
          return {
            '1': 'सप्तमी', '2': 'अष्टमी', '3': 'नवमी', '4': 'दशमी', '5': 'एकादशी',
            '6': 'द्वादशी', '7': 'त्रयोदशी', '8': 'औंसी', '9': 'प्रतिपदा', '10': 'द्वितीया',
            '11': 'तृतीया', '12': 'चतुर्थी', '13': 'पञ्चमी', '14': 'षष्ठी', '15': 'सप्तमी',
            '16': 'अष्टमी', '17': 'अष्टमी', '18': 'नवमी', '19': 'दशमी', '20': 'एकादशी',
            '21': 'द्वादशी', '22': 'त्रयोदशी', '23': 'चतुर्दशी', '24': 'पूर्णिमा', '25': 'प्रतिपदा',
            '26': 'द्वितीया', '27': 'तृतीया', '28': 'चतुर्थी', '29': 'पञ्चमी', '30': 'सप्तमी', '31': 'अष्टमी', '32': 'नवमी'
          };
        case 5: // Bhadra 2082 (भाद्र २०८२)
          return {
            '1': 'नवमी', '2': 'दशमी', '3': 'एकादशी', '4': 'द्वादशी', '5': 'त्रयोदशी',
            '6': 'चतुर्दशी', '7': 'औंसी', '8': 'प्रतिपदा', '9': 'द्वितीया', '10': 'तृतीया',
            '11': 'चतुर्थी', '12': 'पञ्चमी', '13': 'षष्ठी', '14': 'सप्तमी', '15': 'अष्टमी',
            '16': 'नवमी', '17': 'दशमी', '18': 'एकादशी', '19': 'द्वादशी', '20': 'त्रयोदशी',
            '21': 'चतुर्दशी', '22': 'पूर्णिमा', '23': 'प्रतिपदा', '24': 'द्वितीया', '25': 'तृतीया',
            '26': 'चतुर्थी', '27': 'पञ्चमी', '28': 'षष्ठी', '29': 'सप्तमी', '30': 'नवमी', '31': 'दशमी'
          };
        case 6: // Ashwin 2082 (आश्विन २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'प्रतिपदा', '7': 'द्वितीया', '8': 'तृतीया', '9': 'चतुर्थी', '10': 'चतुर्थी',
            '11': 'पञ्चमी', '12': 'षष्ठी', '13': 'सप्तमी', '14': 'अष्टमी', '15': 'नवमी',
            '16': 'दशमी', '17': 'एकादशी', '18': 'द्वादशी', '19': 'त्रयोदशी', '20': 'चतुर्दशी',
            '21': 'पूर्णिमा', '22': 'द्वितीया', '23': 'तृतीया', '24': 'चतुर्थी', '25': 'पञ्चमी',
            '26': 'षष्ठी', '27': 'सप्तमी', '28': 'अष्टमी', '29': 'नवमी', '30': 'दशमी', '31': 'एकादशी'
          };
        case 7: // Kartik 2082 (कार्तिक २०८२)
          return {
            '1': 'द्वादशी', '2': 'त्रयोदशी', '3': 'चतुर्दशी', '4': 'औंसी', '5': 'प्रतिपदा',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'एकादशी'
          };
        case 8: // Mangsir 2082 (मंसिर २०८२)
          return {
            '1': 'द्वादशी', '2': 'त्रयोदशी', '3': 'चतुर्दशी', '4': 'औंसी', '5': 'प्रतिपदा',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'प्रतिपदा', '20': 'द्वितीया',
            '21': 'तृतीया', '22': 'चतुर्थी', '23': 'पञ्चमी', '24': 'षष्ठी', '25': 'सप्तमी',
            '26': 'अष्टमी', '27': 'नवमी', '28': 'दशमी', '29': 'एकादशी', '30': 'द्वादशी'
          };
        case 9: // Poush 2082 (पौष २०८२)
          return {
            '1': 'त्रयोदशी', '2': 'त्रयोदशी', '3': 'चतुर्दशी', '4': 'औंसी', '5': 'प्रतिपदा',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'सप्तमी', '13': 'अष्टमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'एकादशी'
          };
        case 10: // Magh 2082 (माघ २०८२)
          return {
            '1': 'द्वादशी', '2': 'त्रयोदशी', '3': 'चतुर्दशी', '4': 'औंसी', '5': 'प्रतिपदा',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'पूर्णिमा', '19': 'प्रतिपदा', '20': 'द्वितीया',
            '21': 'तृतीया', '22': 'चतुर्थी', '23': 'पञ्चमी', '24': 'षष्ठी', '25': 'सप्तमी',
            '26': 'अष्टमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'एकादशी'
          };
        case 11: // Falgun 2082 (फाल्गुन २०८२)
          return {
            '1': 'द्वादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'प्रतिपदा', '7': 'द्वितीया', '8': 'तृतीया', '9': 'चतुर्थी', '10': 'पञ्चमी',
            '11': 'षष्ठी', '12': 'सप्तमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'पूर्णिमा', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'दशमी'
          };
        case 12: // Chaitra 2082 (चैत्र २०८२)
          return {
            '1': 'एकादशी', '2': 'द्वादशी', '3': 'त्रयोदशी', '4': 'चतुर्दशी', '5': 'औंसी',
            '6': 'द्वितीया', '7': 'तृतीया', '8': 'चतुर्थी', '9': 'पञ्चमी', '10': 'षष्ठी',
            '11': 'सप्तमी', '12': 'अष्टमी', '13': 'नवमी', '14': 'दशमी', '15': 'एकादशी',
            '16': 'द्वादशी', '17': 'त्रयोदशी', '18': 'चतुर्दशी', '19': 'पूर्णिमा', '20': 'प्रतिपदा',
            '21': 'द्वितीया', '22': 'तृतीया', '23': 'चतुर्थी', '24': 'पञ्चमी', '25': 'षष्ठी',
            '26': 'सप्तमी', '27': 'अष्टमी', '28': 'नवमी', '29': 'दशमी', '30': 'एकादशी'
          };
        default:
          return {};
      }
    }
    return {};
  }

  Map<String, List<CalendarEvent>> _getFestivalsForMonth(int year, int month) {
    // Return Tithi data for BS 2082 (current implementation)
    if (year == 2082) {
      switch (month) {
        case 1: // Baisakh 2082 (बैशाख २०८२)
          return {
            '1': [CalendarEvent(title: 'नववर्ष २०८२ आरम्भ', description: 'ललितपुर रातो मच्छिन्द्रनाथ स्नान (बुँगद्यः न्हंवः), भक्तपुर विश्वध्वजपातन (विस्काजात्रा), खायू सँल्हू', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '2': [CalendarEvent(title: 'सिरूवा पावनी पर्व', description: 'सिरूवा पर्व मनाउने झापा, मोरङ, सुनसरी, सिराहा र सप्तरी जिल्लाहरुमा बिदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '3': [CalendarEvent(title: 'भक्तपुर ब्रम्हायणी यात्रा', description: 'स्वामी शशीधर जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'रविसप्तमी', description: 'अष्टमीव्रत', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '8': [CalendarEvent(title: 'गोरखकाली पूजा', description: 'छन्द दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 8, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'बरूथिनी एकादशी', description: 'लोकतन्त्र दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'माताति चःह्रे', description: 'राष्ट्रिय फोटो पत्रकारिता दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'मातातीर्थ औंसी', description: 'आमाको मुख हेर्ने', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'ललितपुर मच्छिन्द्रनाथ रथारोहण', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '16': [CalendarEvent(title: 'परशुराम जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 16, type: 'festival', color: '#FF5722')],
            '17': [CalendarEvent(title: 'अक्षय तृतीया', description: 'शिवपार्वती विवाह', nepaliYear: year, nepaliMonth: month, nepaliDay: 17, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'विश्व श्रमिक दिवस', description: 'ललितपुर रातो मच्छिन्द्रनाथ रथ यात्रा आरम्भ', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'आद्यगुरु शङ्कराचार्य जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '20': [CalendarEvent(title: 'राष्ट्रिय सूचना तथा सञ्चार प्रविधि दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 20, type: 'festival', color: '#FF5722')],
            '22': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 22, type: 'festival', color: '#FF5722')],
            '23': [CalendarEvent(title: 'सीता जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 23, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'किराँत समाजसुधार दिवस', description: 'राष्ट्रिय पत्रकारिता दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '25': [CalendarEvent(title: 'मोहिनी एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 25, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'राष्ट्रिय कानून दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '28': [CalendarEvent(title: 'नृसिंह जयन्ती', description: 'स्याङ्जा लसर्घा आलमदेवी पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 28, type: 'festival', color: '#FF5722')],
            '29': [CalendarEvent(title: 'गौतमबुद्ध जयन्ती', description: 'स्वाँया पुन्हिः, चण्डी पूर्णिमा, उभौली पर्व, ललितपुर गोटीखेल बैतरणी धाम स्नान, गोरखनाथ जयन्ती, वैशाख स्नान समाप्ति', nepaliYear: year, nepaliMonth: month, nepaliDay: 29, type: 'festival', color: '#FF5722')],
          };
        case 2: // Jestha 2082 (जेठ २०८२)
          return {
            '6': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 6, type: 'festival', color: '#FF5722')],
            '9': [CalendarEvent(title: 'अपरा एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 9, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'सिथिःचःह्रे', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'शनि जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'गोसाइँकुण्ड स्नान आरम्भ', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'गणतन्त्र दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'कुमार षष्ठी', description: 'सिथीः नख, भक्तपुर चण्डी भगवती यात्रा', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'कुमार यात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '20': [CalendarEvent(title: 'वायु अष्टमी', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 20, type: 'festival', color: '#FF5722')],
            '21': [CalendarEvent(title: 'जातीय भेदभाव तथा छुवाछुत उन्मूलन राष्ट्रिय दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 21, type: 'festival', color: '#FF5722')],
            '22': [CalendarEvent(title: 'गोसाइँकुण्ड स्नान समाप्ति', description: 'वैतडी विश्वनाथ मन्दिरमा गङ्गादशहरा स्नानमेला', nepaliYear: year, nepaliMonth: month, nepaliDay: 22, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'निर्जला एकादशी', description: 'तुलसीको दल राख्ने, ईद–उल–अज्हा (बकर ईद)', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '27': [CalendarEvent(title: 'पूर्णिमाव्रत', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 27, type: 'festival', color: '#FF5722')],
            '28': [CalendarEvent(title: 'मष्टपूर्णिमा (ज्याःपुन्हिः)', description: 'पनौती स्नान, पनौती रथ यात्रा, कवीर जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 28, type: 'festival', color: '#FF5722')],
            '31': [CalendarEvent(title: 'विश्व रक्तदाता दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 31, type: 'festival', color: '#FF5722')],
          };
        case 3: // Ashar 2082 (असार २०८२)
          return {
            '4': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 4, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'भलभल अष्टमी', description: 'देउपाटनमा त्रिशुल जात्रा', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'स्मार्तहरूको योगिनी एकादशीव्रत', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '8': [CalendarEvent(title: 'वैष्णवहरुको योगिनी एकादशीव्रत', description: 'भूमिरज', nepaliYear: year, nepaliMonth: month, nepaliDay: 8, type: 'festival', color: '#FF5722')],
            '10': [CalendarEvent(title: 'दिलाचःह्रे:', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 10, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'दर्शश्राद्ध', description: 'भूमिपूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'जगन्नाथ रथयात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'दहिचिउरा खाने दिन', description: 'राष्ट्रिय धान दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '22': [CalendarEvent(title: 'हरिशयनी एकादशीव्रत', description: 'तुलसी रोप्ने, चतुर्मासव्रत आरम्भ', nepaliYear: year, nepaliMonth: month, nepaliDay: 22, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'गुरू पूर्णिमा', description: 'पूर्णिमाव्रत, दिला पुन्हिः, व्यास जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '27': [CalendarEvent(title: 'विश्व जनसंख्या दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 27, type: 'festival', color: '#FF5722')],
            '29': [CalendarEvent(title: 'भानु जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 29, type: 'festival', color: '#FF5722')],
          };
        case 4: // Shrawan 2082 (श्रावण २०८२)
          return {
            '1': [CalendarEvent(title: 'साउन संक्रान्ती', description: 'लुतो फाल्ने एवं राँको बाल्ने, थारू गुरिया पर्व', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '2': [CalendarEvent(title: 'गोरखकाली पूजा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'कामिका एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'घण्टाकर्ण चतुर्दशी', description: 'गठाँमुगचह्नेः', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '9': [CalendarEvent(title: 'गुँलाधर्म आरम्भ', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 9, type: 'festival', color: '#FF5722')],
            '10': [CalendarEvent(title: 'चन्द्रोदय', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 10, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'वराह जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'नाग पञ्चमी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'कल्की जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'खिर खाने दिन', description: 'गोस्वामी तुलसीदास जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '16': [CalendarEvent(title: 'अष्टमीव्रत', description: 'यल पञ्चदान, गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 16, type: 'festival', color: '#FF5722')],
            '20': [CalendarEvent(title: 'पुत्रदा एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 20, type: 'festival', color: '#FF5722')],
            '23': [CalendarEvent(title: 'पूर्णिमाव्रत', description: 'बाजुरा बडिमालिका मेला', nepaliYear: year, nepaliMonth: month, nepaliDay: 23, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'जनैपूर्णिमा', description: 'ऋषितर्पणी, रक्षाबन्धन, गुँःपुन्हिः', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '25': [CalendarEvent(title: 'गाईजात्रा (सापारू)', description: 'काठमाडौं उपत्यकालाई र देशभरका नेवार समुदायका लागि मात्र बिदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 25, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'रोपाइँ जात्रा', description: 'यल मत्या', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '29': [CalendarEvent(title: 'ललितपुर नृसिंह यात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 29, type: 'festival', color: '#FF5722')],
            '31': [CalendarEvent(title: 'श्रीकृष्णजन्माष्टमी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 31, type: 'festival', color: '#FF5722')],
          };
        case 5: // Bhadra 2082 (भाद्र २०८२)
          return {
            '3': [CalendarEvent(title: 'अजा एकादशीव्रत', description: 'राष्ट्रिय सूचना दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '4': [CalendarEvent(title: 'स्वयम्भूको छायाँ दर्शन', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 4, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'यें पञ्चदान', description: 'जुगःच:ह्रे पुजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'कुशे औँसी', description: 'बाबुको मुख हेर्ने दिन', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '8': [CalendarEvent(title: 'गुलांँधर्म समाप्ति', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 8, type: 'festival', color: '#FF5722')],
            '9': [CalendarEvent(title: 'दरखाने दिन', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 9, type: 'festival', color: '#FF5722')],
            '10': [CalendarEvent(title: 'हरितालिका व्रत', description: 'तीज (महिला कर्मचारीहरूको लागि मात्र बिदा)', nepaliYear: year, nepaliMonth: month, nepaliDay: 10, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'गणेश चतुर्थी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '12': [CalendarEvent(title: 'ऋषिपञ्चमी', description: 'विरूडा पञ्चमी, सप्तऋषि पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 12, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'गौरा सप्तमी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'गौरा पर्व', description: 'गौरा पर्व मनाउने कर्मचारीहरुका लागि मात्र बिदा, काय अष्टमी, कागेश्वर मेला, गोरखकाली पूजा, दूर्वाष्टमी', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'हरिपरिवर्तनी एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'इन्द्रध्वजोत्थान', description: 'वामन द्वादशी, उपाकु, मोहम्मद जयन्ती (नेपाली मुस्लिम धर्मावलम्बीहरूको लागि मात्र बिदा)', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '20': [CalendarEvent(title: 'मानव वेचविखन विरुद्धको राष्ट्रिय दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 20, type: 'festival', color: '#FF5722')],
            '21': [CalendarEvent(title: 'ईन्द्रजात्रा', description: 'काठमाडौं उपत्यका मात्र विदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 21, type: 'festival', color: '#FF5722')],
            '22': [CalendarEvent(title: 'यैँया पुन्हिः', description: 'चेपाङ चोनाम पर्व, निजामती सेवा दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 22, type: 'festival', color: '#FF5722')],
            '23': [CalendarEvent(title: 'सोह्रश्राद्ध आरम्भ', description: 'प्रतिपदा श्राद्ध', nepaliYear: year, nepaliMonth: month, nepaliDay: 23, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'द्वितीया श्राद्ध', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '25': [CalendarEvent(title: 'तृतीया श्राद्ध', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 25, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'चतुर्थी श्राद्ध', description: 'इन्द्रध्वजपातन, नानिचाया', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '27': [CalendarEvent(title: 'पञ्चमी श्राद्ध', description: 'षष्ठी श्राद्ध', nepaliYear: year, nepaliMonth: month, nepaliDay: 27, type: 'festival', color: '#FF5722')],
            '28': [CalendarEvent(title: 'सप्तमी श्राद्ध', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 28, type: 'festival', color: '#FF5722')],
            '29': [CalendarEvent(title: 'रवि सप्तमी', description: 'अष्टमीव्रत, अष्टमी श्राद्ध, गोरखकाली पूजा, बाल दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 29, type: 'festival', color: '#FF5722')],
            '30': [CalendarEvent(title: 'जितिया पर्व', description: 'जितिया पर्व मनाउने महिला कर्मचारीहरुका लागि मात्र बिदा, नवमी श्राद्ध', nepaliYear: year, nepaliMonth: month, nepaliDay: 30, type: 'festival', color: '#FF5722')],
            '31': [CalendarEvent(title: 'दशमी श्राद्ध', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 31, type: 'festival', color: '#FF5722')],
          };
        case 6: // Ashwin 2082 (आश्विन २०८२)
          return {
            '1': [CalendarEvent(title: 'एकादशी श्राद्ध', description: 'इन्दिरा एकादशीव्रत, विश्वकर्मा पूजा, वास्तु दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '2': [CalendarEvent(title: 'द्वादशी श्राद्ध', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '3': [CalendarEvent(title: 'त्रयोदशी श्राद्ध', description: 'संविधान दिवस (राष्ट्रिय दिवस), जुम्ला खलङ्गामा चन्दननाथको लिङ्गो ठड्याउने', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '4': [CalendarEvent(title: 'चतुर्दशी श्राद्ध', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 4, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'औंसी श्राद्ध', description: 'दर्शश्राद्ध, पितृ विसर्जन (सोह्र श्राद्ध समाप्ति)', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '6': [CalendarEvent(title: 'घटस्थापना', description: 'नवरात्र आरम्भ, मातामह श्राद्ध', nepaliYear: year, nepaliMonth: month, nepaliDay: 6, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'पचली भैरव यात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'फूलपाती', description: 'नवपत्रिका प्रवेश', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'महाअष्टमी', description: 'कालरात्रि, कुछि भ्वय्', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'महा नवमी', description: 'स्याक्वःत्याक्वः', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '16': [CalendarEvent(title: 'विजयादशमी', description: 'दर्शैको टीका २०८२, देवीविसर्जन', nepaliYear: year, nepaliMonth: month, nepaliDay: 16, type: 'festival', color: '#FF5722')],
            '17': [CalendarEvent(title: 'पापाङकुशा एकादशीव्रत', description: 'अन्नपूर्णा यात्रा, असँः चालँः', nepaliYear: year, nepaliMonth: month, nepaliDay: 17, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'द्वादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '20': [CalendarEvent(title: 'कोजाग्रतव्रत', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 20, type: 'festival', color: '#FF5722')],
            '21': [CalendarEvent(title: 'कोजाग्रत पूर्णिमा', description: 'कतिं पुन्हिः, कात्तिकस्नान, आकाशदीपदान आरम्भ', nepaliYear: year, nepaliMonth: month, nepaliDay: 21, type: 'festival', color: '#FF5722')],
            '23': [CalendarEvent(title: 'हुलाक दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 23, type: 'festival', color: '#FF5722')],
            '28': [CalendarEvent(title: 'राधा अष्टमी', description: 'भौमाष्टमी, गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 28, type: 'festival', color: '#FF5722')],
            '30': [CalendarEvent(title: 'खाद्य दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 30, type: 'festival', color: '#FF5722')],
            '31': [CalendarEvent(title: 'रमा एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 31, type: 'festival', color: '#FF5722')],
          };
        case 7: // Kartik 2082 (कार्तिक २०८२)
          return {
            '1': [CalendarEvent(title: 'धन्तेरस', description: 'यमदीप दान', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '2': [CalendarEvent(title: 'काग तिहार', description: 'धन्वन्तरी जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '3': [CalendarEvent(title: 'कुकुर तिहार', description: 'नरक चतुर्दशी, लक्ष्मी पूजा, दीपमालिका', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '4': [CalendarEvent(title: 'औंसी', description: 'दर्श श्राद्ध', nepaliYear: year, nepaliMonth: month, nepaliDay: 4, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'गाई पूजा', description: 'गोरू पूजा, गोवर्द्धन पूजा, म्हःपूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '6': [CalendarEvent(title: 'भाइटीका', description: 'किजा पूजा, यम द्वितीया', nepaliYear: year, nepaliMonth: month, nepaliDay: 6, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'तृतीया', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '10': [CalendarEvent(title: 'छठ पर्व', description: 'डाला पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 10, type: 'festival', color: '#FF5722')],
            '12': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 12, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'कुष्माण्ड नवमी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'बलम्वु महालक्ष्मी यात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'हरिबोधिनी एकादशी', description: 'तुलसी विवाह', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '16': [CalendarEvent(title: 'चाँगुनारायण अखण्डदीप दर्शन', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 16, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'वैकुण्ठ चतुर्दशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'गुरूनानक जयन्ती', description: 'शिख धर्मावलम्बी कर्मचारीहरुका लागि मात्र बिदा, पूर्णिमाव्रत, चतुर्मासव्रत समाप्ति, सकिमना पुन्हिः', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '25': [CalendarEvent(title: 'फाल्गुनन्द जयन्ती', description: 'किरात धर्मावलम्बीहरूका लागि मात्र बिदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 25, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'बुधाष्टमीव्रत', description: 'भैरवाष्टमी, गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '28': [CalendarEvent(title: 'श्री गुहेश्वरी यात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 28, type: 'festival', color: '#FF5722')],
            '29': [CalendarEvent(title: 'उत्पतिका एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 29, type: 'festival', color: '#FF5722')],
          };
        case 8: // Mangsir 2082 (मंसिर २०८२)
          return {
            '2': [CalendarEvent(title: 'पशुपतिनाथ मेला', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '3': [CalendarEvent(title: 'बाला चतुदर्शी', description: 'शतबीज छर्ने', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'टेलिभिजन दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '9': [CalendarEvent(title: 'विवाह पञ्चमी', description: 'जनकपुरमा सीता विवाह पञ्चमी मेला, ब्रम्हचारी षडानन्द जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 9, type: 'festival', color: '#FF5722')],
            '12': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 12, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'मोक्षदा एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '17': [CalendarEvent(title: 'विश्व अपाङ्ग दिवस', description: 'अपाङ्गता भएका कर्मचारीहरूका लागि बिदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 17, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'यमरीपुन्हीः', description: 'उधौलीपर्व, धान्यपूर्णिमा, ज्यापु दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'मानव अधिकार दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '29': [CalendarEvent(title: 'सफला एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 29, type: 'festival', color: '#FF5722')],
          };
        case 9: // Poush 2082 (पौष २०८२)
          return {
            '5': [CalendarEvent(title: 'तोल ल्होसार', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '10': [CalendarEvent(title: 'क्रिसमस डे', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 10, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'अष्टमीव्रत', description: 'सेतो मच्छिन्द्रनाथ स्नान, जनवहाद्यः न्हवंः, गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'पुत्रदा एकादशी', description: 'तमुल्होछार', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '17': [CalendarEvent(title: 'सन् २०२६ आरम्भ', description: 'राष्ट्रिय टोपी दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 17, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'श्रीस्वस्थानीव्रत आरम्भ', description: 'मिला पुन्हिः, माघस्नान सुरू', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '21': [CalendarEvent(title: 'गुरु गोविन्दसिंह जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 21, type: 'festival', color: '#FF5722')],
            '23': [CalendarEvent(title: 'अरनिको स्मृति दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 23, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'नेपाल ज्योतिष परिषद् स्थापना दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'अष्टमीव्रत', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
            '27': [CalendarEvent(title: 'पृथ्वी जयन्ती', description: 'राष्ट्रिय एकता दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 27, type: 'festival', color: '#FF5722')],
            '30': [CalendarEvent(title: 'षट्तिला एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 30, type: 'festival', color: '#FF5722')],
          };
        case 10: // Magh 2082 (माघ २०८२)
          return {
            '1': [CalendarEvent(title: 'माघे संक्रान्ति', description: 'माघी पर्व, घ्य:चाकु संल्हु, राष्ट्रिय कृषि जैविक विविधता दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '2': [CalendarEvent(title: 'राष्ट्रिय भुकम्प सुरक्षा दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '4': [CalendarEvent(title: 'पशुपति क्षेत्रमा माधव नारायण मेला', description: 'त्रिवेणी मेला', nepaliYear: year, nepaliMonth: month, nepaliDay: 4, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'सोनाम ल्होसार', description: 'तामाङ ल्होछार, श्रीवल्लभ जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '9': [CalendarEvent(title: 'वसन्तपञ्यमी', description: 'वसन्तश्रवण श्रीपञ्यमी सरस्वती पूजा (शिक्षण संस्थाहरूका लागि मात्र विदा)', nepaliYear: year, nepaliMonth: month, nepaliDay: 9, type: 'festival', color: '#FF5722')],
            '11': [CalendarEvent(title: 'रवि सप्तमी', description: 'अचला सप्तमी', nepaliYear: year, nepaliMonth: month, nepaliDay: 11, type: 'festival', color: '#FF5722')],
            '12': [CalendarEvent(title: 'अष्टमीव्रत', description: 'भिष्माष्टमी', nepaliYear: year, nepaliMonth: month, nepaliDay: 12, type: 'festival', color: '#FF5722')],
            '14': [CalendarEvent(title: 'सम्पत्ति शुद्धीकरण निवारण राष्ट्रिय दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 14, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'जया एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '16': [CalendarEvent(title: 'सहिद दिवस', description: 'चाँगुमा माधव नारायण मेला', nepaliYear: year, nepaliMonth: month, nepaliDay: 16, type: 'festival', color: '#FF5722')],
            '17': [CalendarEvent(title: 'श्री पशुपतिनाथमा छायाँ दर्शन', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 17, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'श्रीस्वस्थानीव्रत समाप्ति', description: 'सि पुन्हिः, माघस्नान समाप्ति', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '25': [CalendarEvent(title: 'भानु सप्तमी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 25, type: 'festival', color: '#FF5722')],
            '26': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 26, type: 'festival', color: '#FF5722')],
          };
        case 11: // Falgun 2082 (फाल्गुन २०८२)
          return {
            '1': [CalendarEvent(title: 'विजया एकादशी व्रत', description: 'रेडियो दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '2': [CalendarEvent(title: 'भ्यालेन्टाइन डे', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 2, type: 'festival', color: '#FF5722')],
            '3': [CalendarEvent(title: 'महाशिवरात्रि', description: 'सिलाचह्रे:, नेपाली सेना दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '6': [CalendarEvent(title: 'ग्याल्पो ल्होसार', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 6, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'प्रजातन्त्र दिवस', description: 'निर्वाचन दिवस', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '12': [CalendarEvent(title: 'भौमाष्टमीव्रत', description: 'चिरोत्थान, होलिकारम्भ, चिरस्वायगु', nepaliYear: year, nepaliMonth: month, nepaliDay: 12, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'आमलकी एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'होली', description: 'चीरदाह, पूर्णिमाव्रत (हिमाली, पहाडी तथा भित्री मधेशका ५६ जिल्लाहरूमा बिदा)', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'फागु पूर्णिमा (होलीपुन्हीः)', description: 'तराइमा होली (तराईका २१ जिल्लाहरूमा बिदा)', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '20': [CalendarEvent(title: 'नाला मच्छिन्द्रनाथ स्नान', description: 'नाला न्हवं', nepaliYear: year, nepaliMonth: month, nepaliDay: 20, type: 'festival', color: '#FF5722')],
            '22': [CalendarEvent(title: 'नाला मच्छिन्द्रनाथ रथयात्रा', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 22, type: 'festival', color: '#FF5722')],
            '24': [CalendarEvent(title: 'अन्तर्राष्ट्रिय महिला दिवस', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 24, type: 'festival', color: '#FF5722')],
            '27': [CalendarEvent(title: 'शीतलाष्टमी', description: 'बुधाष्टमीव्रत, गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 27, type: 'festival', color: '#FF5722')],
          };
        case 12: // Chaitra 2082 (चैत्र २०८२)
          return {
            '1': [CalendarEvent(title: 'पापमोचिनी एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 1, type: 'festival', color: '#FF5722')],
            '3': [CalendarEvent(title: 'पाहाँ (पासा) चह्रे:', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 3, type: 'festival', color: '#FF5722')],
            '4': [CalendarEvent(title: 'घोडेजात्रा', description: 'काठमाडौं उपत्यकालाई मात्र विदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 4, type: 'festival', color: '#FF5722')],
            '5': [CalendarEvent(title: 'चोभार आदिनाथ स्नान', description: 'चोभाद्यः न्हवं', nepaliYear: year, nepaliMonth: month, nepaliDay: 5, type: 'festival', color: '#FF5722')],
            '7': [CalendarEvent(title: 'मत्स्येनारायण मेला', description: 'मत्स्येजयन्ती, ईद-उल-फित्रको दिन (चैत ७ गते वा सो पर्व पर्ने दिन) बिदा', nepaliYear: year, nepaliMonth: month, nepaliDay: 7, type: 'festival', color: '#FF5722')],
            '10': [CalendarEvent(title: 'चैती छठ', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 10, type: 'festival', color: '#FF5722')],
            '12': [CalendarEvent(title: 'चैते दशैं', description: 'सेतो मच्छिन्द्रनाथ रथयात्रा आरम्भ, चैत्राष्टमी', nepaliYear: year, nepaliMonth: month, nepaliDay: 12, type: 'festival', color: '#FF5722')],
            '13': [CalendarEvent(title: 'राम नवमी', description: 'श्रीराम जयन्ती', nepaliYear: year, nepaliMonth: month, nepaliDay: 13, type: 'festival', color: '#FF5722')],
            '15': [CalendarEvent(title: 'कामदा एकादशी', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 15, type: 'festival', color: '#FF5722')],
            '17': [CalendarEvent(title: 'महावीर जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 17, type: 'festival', color: '#FF5722')],
            '18': [CalendarEvent(title: 'पूर्णिमाव्रत', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 18, type: 'festival', color: '#FF5722')],
            '19': [CalendarEvent(title: 'ल्हुतिपुन्हीः', description: 'वैशाख स्नान सुरू, श्री हनुमान जयन्ती, बालाजु बाइसधारा मेला', nepaliYear: year, nepaliMonth: month, nepaliDay: 19, type: 'festival', color: '#FF5722')],
            '22': [CalendarEvent(title: 'स्वामी शशिधर जयन्ती', description: '', nepaliYear: year, nepaliMonth: month, nepaliDay: 22, type: 'festival', color: '#FF5722')],
            '27': [CalendarEvent(title: 'अष्टमीव्रत', description: 'गोरखकाली पूजा', nepaliYear: year, nepaliMonth: month, nepaliDay: 27, type: 'festival', color: '#FF5722')],
            '30': [CalendarEvent(title: 'बरूथिनी एकादशीव्रत', description: 'भक्तपुर विश्वध्वजोत्थान', nepaliYear: year, nepaliMonth: month, nepaliDay: 30, type: 'festival', color: '#FF5722')],
          };
        default:
          return {};
      }
    }
    return {};
  }

  Widget _buildDrawer() {
    // Complete features list - all 25 features with performance optimizations
    final List<Map<String, dynamic>> drawerFeatures = [
      {'titleEnglish': 'Nepali Calendar', 'titleNepali': 'नेपाली पात्रो', 'icon': Icons.calendar_today, 'color': Colors.indigo},
      {'titleEnglish': 'Nepali Panchang', 'titleNepali': 'नेपाली पंचांग', 'icon': Icons.schedule, 'color': Colors.deepOrange},
      {'titleEnglish': 'Kundali', 'titleNepali': 'कुन्डली', 'icon': Icons.star_border, 'color': Colors.amber[800]},
      {'titleEnglish': 'Horoscope', 'titleNepali': 'राशिफल', 'icon': Icons.psychology, 'color': Colors.pink},
      {'titleEnglish': 'Hindu Mantra Audio', 'titleNepali': 'हिन्दू मंत्र अडियो', 'icon': Icons.music_note, 'color': Colors.red[700]},
      {'titleEnglish': 'Sahit', 'titleNepali': 'साहित', 'icon': Icons.menu_book, 'color': Colors.brown},
      {'titleEnglish': 'News', 'titleNepali': 'समाचार', 'icon': Icons.newspaper, 'color': Colors.blue[800]},
      {'titleEnglish': 'FM Radio', 'titleNepali': 'एफ-एम रेडियो', 'icon': Icons.radio, 'color': Colors.purple[700]},
      {'titleEnglish': 'Bhagavad Gita', 'titleNepali': 'श्रीमद भगवद गीता', 'icon': Icons.auto_stories, 'color': Colors.orange[800]},
      {'titleEnglish': 'Date Converter', 'titleNepali': 'मिति रूपान्तरण', 'icon': Icons.date_range, 'color': Colors.teal[700]},
      {'titleEnglish': 'Foreign Exchange', 'titleNepali': 'बिदेशी बिनिमय दर', 'icon': Icons.currency_exchange, 'color': Colors.green[700]},
      {'titleEnglish': 'Gold/Silver Rate', 'titleNepali': 'सुन-चादी बजारभाउ', 'icon': Icons.monetization_on, 'color': Colors.yellow[800]},
      {'titleEnglish': 'Vegetable Rate', 'titleNepali': 'तरकारी बजारभाउ', 'icon': Icons.local_grocery_store, 'color': Colors.lightGreen},
      {'titleEnglish': 'Play Game', 'titleNepali': 'खेल खेल्नुहोस', 'icon': Icons.games, 'color': Colors.deepPurple},
      {'titleEnglish': 'Share Market', 'titleNepali': 'सेयर बजार', 'icon': Icons.trending_up, 'color': Colors.green[800]},
      {'titleEnglish': 'Weather', 'titleNepali': 'मौसम', 'icon': Icons.wb_sunny, 'color': Colors.orange},
      {'titleEnglish': 'Gaunkhane Katha', 'titleNepali': 'गाउखाने कथा', 'icon': Icons.record_voice_over, 'color': Colors.blue[600]},
      {'titleEnglish': 'Dictionary', 'titleNepali': 'शब्दकोश', 'icon': Icons.translate, 'color': Colors.indigo[700]},
      {'titleEnglish': 'Bank Interest Rate', 'titleNepali': 'ब्याजदर', 'icon': Icons.account_balance, 'color': Colors.cyan[700]},
      {'titleEnglish': 'Nepali Map', 'titleNepali': 'बैंक', 'icon': Icons.map, 'color': Colors.red[600]},
      {'titleEnglish': 'Dream Return', 'titleNepali': 'सपनाको फल', 'icon': Icons.bedtime, 'color': Colors.purple[600]},
      {'titleEnglish': 'Unit Converter', 'titleNepali': 'इकाई रूपांतरण', 'icon': Icons.calculate, 'color': Colors.teal[600]},
      {'titleEnglish': 'Pooja Bidhi', 'titleNepali': 'पूजा विधि', 'icon': Icons.temple_hindu, 'color': Colors.orange[700]},
      {'titleEnglish': 'Quiz', 'titleNepali': 'प्रश्नोत्तर', 'icon': Icons.quiz, 'color': Colors.pink[600]},
      {'titleEnglish': 'Funny Video', 'titleNepali': 'रमाइलो भिडियो', 'icon': Icons.video_library, 'color': Colors.red[500]},
    ];

    return Drawer(
      child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF8E24AA), // Purple
                Color(0xFFAD1457), // Deep Pink
                Color(0xFFE91E63), // Pink
              ],
            ),
          ),
          child: Column(
          children: [
            // Drawer Header - Minimal space for top bar
            Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8), // Much smaller padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        radius: 35,
                        backgroundColor: Colors.white,
                        child: Icon(
                          Icons.calendar_today,
                          size: 35,
                          color: Color(0xFF8E24AA), // Purple to match new theme
                        ),
                      ),
                      const Spacer(),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isNepaliLanguage ? 'नेपाली पात्रो' : 'Nepali Calendar',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _isNepaliLanguage ? 'सबै सुविधाहरू' : 'All Features',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(color: Colors.white30, thickness: 1),
            // Features List - Very minimal gap to start functions
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 2),
                itemCount: drawerFeatures.length,
                physics: const BouncingScrollPhysics(), // Smoother scrolling
                cacheExtent: 100, // Optimize rendering
                itemBuilder: (context, index) {
                  final feature = drawerFeatures[index];
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 1),
                    child: Material(
                      color: Colors.white.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(10),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(10),
                        onTap: () {
                          HapticFeedback.lightImpact();
                          Navigator.pop(context);

                          // Navigate to specific pages based on feature
                          if (index == 0) { // Nepali Calendar - already on this page
                            // Do nothing, already on calendar page
                          } else {
                            // For other features, go back to homepage
                            Navigator.pop(context);
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          child: Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: feature['color'],
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Icon(
                                  feature['icon'],
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _isNepaliLanguage ? feature['titleNepali'] : feature['titleEnglish'],
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              const Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.white60,
                                size: 12,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            // Footer
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Divider(color: Colors.white30, thickness: 1),

                  ListTile(
                    leading: const Icon(Icons.info, color: Colors.white),
                    title: const Text(
                      'हाम्रो बारेमा',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    onTap: () {
                      // About section - no action needed
                    },
                  ),
                ],
              ),
            ),
            // Bottom padding to prevent overlap with device navigation
            const SizedBox(height: 24), // Minimal bottom padding
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF0D47A1), // Dark Blue
            const Color(0xFF1565C0), // Medium Dark Blue
            const Color(0xFF0277BD), // Deep Blue
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF0D47A1).withOpacity(0.6),
            blurRadius: 25,
            spreadRadius: 4,
            offset: const Offset(0, -10),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: Colors.white70, // Lighter white for all
        unselectedItemColor: Colors.white70, // Lighter white for all
        selectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
          color: Colors.white70, // Lighter bold text
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold, // Bold for all text
          color: Colors.white70, // Lighter bold text
        ),
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });

          // Navigate based on selection
          if (index == 0) { // Home
            Navigator.pop(context);
          }
          // index 1 is current page (Patro), so no navigation needed
        },
        items: [
          BottomNavigationBarItem(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: _selectedIndex == 0 ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF42A5F5), // Medium Blue
                    const Color(0xFF1E88E5), // Darker Blue
                    const Color(0xFF1565C0), // Dark Blue
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF42A5F5).withOpacity(0.4),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ) : null,
              child: Icon(
                Icons.home,
                size: 26,
                color: Colors.white70, // Lighter color for all states
              ),
            ),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: _selectedIndex == 1 ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF42A5F5), // Medium Blue
                    const Color(0xFF1E88E5), // Darker Blue
                    const Color(0xFF1565C0), // Dark Blue
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF42A5F5).withOpacity(0.4),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ) : null,
              child: Icon(
                Icons.calendar_today,
                size: 26,
                color: Colors.white70, // Lighter color for all states
              ),
            ),
            label: 'Patro',
          ),
          BottomNavigationBarItem(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: _selectedIndex == 2 ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF42A5F5), // Medium Blue
                    const Color(0xFF1E88E5), // Darker Blue
                    const Color(0xFF1565C0), // Dark Blue
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF42A5F5).withOpacity(0.4),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ) : null,
              child: Icon(
                Icons.star,
                size: 26,
                color: Colors.white70, // Lighter color for all states
              ),
            ),
            label: 'Horoscope',
          ),
          BottomNavigationBarItem(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: _selectedIndex == 3 ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF42A5F5), // Medium Blue
                    const Color(0xFF1E88E5), // Darker Blue
                    const Color(0xFF1565C0), // Dark Blue
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF42A5F5).withOpacity(0.6), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF42A5F5).withOpacity(0.4),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ) : null,
              child: Icon(
                Icons.games,
                size: 26,
                color: Colors.white70, // Lighter color for all states
              ),
            ),
            label: 'Play Game',
          ),
        ],
      ),
    );
  }
}
