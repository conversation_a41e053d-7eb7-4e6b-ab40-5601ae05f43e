import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';
import '../models/user_data.dart';
import '../services/user_data_service.dart';
import '../data/nepali_districts.dart';

class AddUserPage extends StatefulWidget {
  final UserData? editUser;
  
  const AddUserPage({super.key, this.editUser});

  @override
  State<AddUserPage> createState() => _AddUserPageState();
}

class _AddUserPageState extends State<AddUserPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  // Date controllers
  final _yearController = TextEditingController();
  final _monthController = TextEditingController();
  final _dayController = TextEditingController();

  // Time controllers
  final _hourController = TextEditingController();
  final _minuteController = TextEditingController();

  String _selectedGender = Gender.male;
  String _selectedAmPm = 'AM';
  NepaliDistrict? _selectedDistrict;
  NepaliDateTime? _selectedBSDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.editUser != null) {
      _populateEditData();
    }
  }

  void _populateEditData() {
    final user = widget.editUser!;
    _nameController.text = user.name;
    _selectedGender = user.gender;
    _selectedBSDate = user.birthDateBS;

    // Populate date fields
    _yearController.text = user.birthDateBS.year.toString();
    _monthController.text = user.birthDateBS.month.toString();
    _dayController.text = user.birthDateBS.day.toString();

    // Populate time fields (convert 24-hour to 12-hour format)
    final timeParts = user.birthTime.split(':');
    final hour24 = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    if (hour24 == 0) {
      _hourController.text = '12';
      _selectedAmPm = 'AM';
    } else if (hour24 < 12) {
      _hourController.text = hour24.toString();
      _selectedAmPm = 'AM';
    } else if (hour24 == 12) {
      _hourController.text = '12';
      _selectedAmPm = 'PM';
    } else {
      _hourController.text = (hour24 - 12).toString();
      _selectedAmPm = 'PM';
    }

    _minuteController.text = minute.toString();

    _selectedDistrict = NepaliDistrictsData.getDistrictByName(user.district);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _hourController.dispose();
    _minuteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          widget.editUser != null ? 'प्रयोगकर्ता सम्पादन' : 'नयाँ प्रयोगकर्ता',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 100), // Extra bottom padding
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildHeaderCard(),
                  const SizedBox(height: 20),
                  _buildPersonalInfoSection(),
                  const SizedBox(height: 20),
                  _buildBirthInfoSection(),
                  const SizedBox(height: 20),
                  _buildLocationSection(),
                  const SizedBox(height: 30),
                  _buildSaveButton(),
                  const SizedBox(height: 20), // Extra space at bottom
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4CAF50).withOpacity(0.1),
            const Color(0xFF2E7D32).withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(
            widget.editUser != null ? Icons.edit : Icons.person_add,
            size: 50,
            color: const Color(0xFF2E7D32),
          ),
          const SizedBox(height: 12),
          Text(
            widget.editUser != null ? 'प्रयोगकर्ता जानकारी सम्पादन गर्नुहोस्' : 'नयाँ प्रयोगकर्ता जानकारी भर्नुहोस्',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'यो जानकारी कुण्डली सेवाहरूको लागि प्रयोग हुनेछ',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF388E3C),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return _buildSection(
      title: 'व्यक्तिगत जानकारी',
      icon: Icons.person,
      children: [
        _buildTextField(
          controller: _nameController,
          label: 'पूरा नाम',
          hint: 'आफ्नो पूरा नाम लेख्नुहोस्',
          icon: Icons.person_outline,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'नाम आवश्यक छ';
            }
            if (value.trim().length < 2) {
              return 'नाम कम्तिमा २ अक्षरको हुनुपर्छ';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        _buildGenderSelector(),
      ],
    );
  }

  Widget _buildBirthInfoSection() {
    return _buildSection(
      title: 'जन्म जानकारी',
      icon: Icons.cake,
      children: [
        _buildSimpleDateSelector(),
        const SizedBox(height: 16),
        _buildSimpleTimeSelector(),
      ],
    );
  }

  Widget _buildLocationSection() {
    return _buildSection(
      title: 'जन्म स्थान',
      icon: Icons.location_on,
      children: [
        _buildDistrictSelector(),
        if (_selectedDistrict != null) ...[
          const SizedBox(height: 12),
          _buildLocationInfo(),
        ],
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF2E7D32), size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      style: const TextStyle(color: Color(0xFF1B5E20), fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: const TextStyle(color: Color(0xFF2E7D32)),
        hintStyle: const TextStyle(color: Color(0xFF66BB6A)),
        prefixIcon: Icon(icon, color: const Color(0xFF2E7D32)),
        filled: true,
        fillColor: const Color(0xFFF1F8E9),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF4CAF50)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF81C784)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF2E7D32), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildGenderSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'लिङ्ग',
          style: TextStyle(
            color: Color(0xFF2E7D32),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: Gender.all.map((gender) {
            return Expanded(
              child: RadioListTile<String>(
                title: Text(
                  gender,
                  style: const TextStyle(color: Color(0xFF1B5E20), fontSize: 14),
                ),
                value: gender,
                groupValue: _selectedGender,
                onChanged: (value) {
                  setState(() {
                    _selectedGender = value!;
                  });
                },
                activeColor: const Color(0xFF2E7D32),
                contentPadding: EdgeInsets.zero,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSimpleDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'जन्म मिति (नेपाली)',
          style: TextStyle(
            color: Color(0xFF2E7D32),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Year
            Expanded(
              flex: 2,
              child: _buildNumberField(
                'वर्ष',
                _yearController,
                'जस्तै: 2080',
                4,
              ),
            ),
            const SizedBox(width: 12),
            // Month
            Expanded(
              child: _buildNumberField(
                'महिना',
                _monthController,
                '1-12',
                2,
              ),
            ),
            const SizedBox(width: 12),
            // Day
            Expanded(
              child: _buildNumberField(
                'दिन',
                _dayController,
                '1-32',
                2,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (_selectedBSDate != null)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'अंग्रेजी मिति: ${_selectedBSDate!.toDateTime().day}/${_selectedBSDate!.toDateTime().month}/${_selectedBSDate!.toDateTime().year}',
              style: const TextStyle(
                color: Color(0xFF2E7D32),
                fontSize: 12,
              ),
            ),
          )
        else if (_yearController.text.isNotEmpty || _monthController.text.isNotEmpty || _dayController.text.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'अवैध मिति - कृपया सही मिति प्रविष्ट गर्नुहोस्',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSimpleTimeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'जन्म समय (12 घण्टे)',
          style: TextStyle(
            color: Color(0xFF2E7D32),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // Hour
            Expanded(
              flex: 2,
              child: _buildNumberField(
                'घण्टा',
                _hourController,
                '1-12',
                2,
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              ':',
              style: TextStyle(
                color: Color(0xFF2E7D32),
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            // Minute
            Expanded(
              flex: 2,
              child: _buildNumberField(
                'मिनेट',
                _minuteController,
                '0-59',
                2,
              ),
            ),
            const SizedBox(width: 12),
            // AM/PM Selector
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F8E9),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFF4CAF50)),
                ),
                child: DropdownButtonFormField<String>(
                  value: _selectedAmPm,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  dropdownColor: Colors.white,
                  style: const TextStyle(color: Color(0xFF1B5E20), fontSize: 14),
                  items: ['AM', 'PM'].map((ampm) {
                    return DropdownMenuItem<String>(
                      value: ampm,
                      child: Text(
                        ampm,
                        style: const TextStyle(color: Color(0xFF1B5E20), fontSize: 14),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedAmPm = value!;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        const Text(
          'उदाहरण: बिहान ११:४० को लागि ११, ४०, AM छान्नुहोस्',
          style: TextStyle(
            color: Color(0xFF66BB6A),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildNumberField(String label, TextEditingController controller, String hint, int maxLength) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.number,
      maxLength: maxLength,
      style: const TextStyle(color: Color(0xFF1B5E20), fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: const TextStyle(color: Color(0xFF2E7D32), fontSize: 12),
        hintStyle: const TextStyle(color: Color(0xFF66BB6A), fontSize: 12),
        filled: true,
        fillColor: const Color(0xFFF1F8E9),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF4CAF50)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF81C784)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF2E7D32), width: 2),
        ),
        counterText: '', // Hide character counter
      ),
      onChanged: (value) => _updateDateFromFields(),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return '$label आवश्यक';
        }
        final number = int.tryParse(value);
        if (number == null) {
          return 'संख्या मात्र';
        }

        // Special validation for different fields
        if (label == 'घण्टा' && (number < 1 || number > 12)) {
          return '1-12 मात्र';
        }

        if (label == 'मिनेट' && (number < 0 || number > 59)) {
          return '0-59 मात्र';
        }

        if (label == 'वर्ष' && (number < 2000 || number > 2100)) {
          return '2000-2100 मात्र';
        }

        if (label == 'महिना' && (number < 1 || number > 12)) {
          return '1-12 मात्र';
        }

        if (label == 'दिन' && (number < 1 || number > 32)) {
          return '1-32 मात्र';
        }

        return null;
      },
    );
  }

  void _updateDateFromFields() {
    final year = int.tryParse(_yearController.text);
    final month = int.tryParse(_monthController.text);
    final day = int.tryParse(_dayController.text);

    if (year != null && month != null && day != null) {
      // Validate ranges before creating date
      if (year >= 2000 && year <= 2100 &&
          month >= 1 && month <= 12 &&
          day >= 1 && day <= 32) {
        try {
          final bsDate = NepaliDateTime(year, month, day);
          setState(() {
            _selectedBSDate = bsDate;
          });
        } catch (e) {
          // Invalid date combination (e.g., 30th of a 29-day month)
          setState(() {
            _selectedBSDate = null;
          });
        }
      } else {
        // Out of valid range
        setState(() {
          _selectedBSDate = null;
        });
      }
    } else {
      setState(() {
        _selectedBSDate = null;
      });
    }
  }

  Widget _buildDistrictSelector() {
    return GestureDetector(
      onTap: () => _selectDistrict(),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF1F8E9),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFF4CAF50)),
        ),
        child: Row(
          children: [
            const Icon(Icons.location_on, color: Color(0xFF2E7D32)),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'जन्म स्थान',
                    style: TextStyle(color: Color(0xFF2E7D32), fontSize: 14),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedDistrict?.nameNepali ?? 'जन्म स्थान छान्नुहोस्',
                    style: TextStyle(
                      color: _selectedDistrict != null ? const Color(0xFF1B5E20) : const Color(0xFF66BB6A),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_drop_down, color: Color(0xFF66BB6A)),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'स्थान विवरण:',
            style: TextStyle(
              color: Colors.blue[300],
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'प्रदेश: ${_selectedDistrict!.province}',
            style: TextStyle(color: Colors.blue[300], fontSize: 12),
          ),
          Text(
            'अक्षांश: ${_selectedDistrict!.latitude}°',
            style: TextStyle(color: Colors.blue[300], fontSize: 12),
          ),
          Text(
            'देशान्तर: ${_selectedDistrict!.longitude}°',
            style: TextStyle(color: Colors.blue[300], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
        ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: _isLoading ? null : _saveUserData,
          child: Center(
            child: _isLoading
                ? const CircularProgressIndicator(color: Colors.white)
                : Text(
                    widget.editUser != null ? 'अपडेट गर्नुहोस्' : 'सुरक्षित गर्नुहोस्',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ),
    );
  }



  Future<void> _selectDistrict() async {
    final district = await showDialog<NepaliDistrict>(
      context: context,
      builder: (context) => _DistrictPickerDialog(),
    );

    if (district != null) {
      setState(() {
        _selectedDistrict = district;
      });
    }
  }

  Future<void> _saveUserData() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedBSDate == null) {
      _showErrorSnackBar('जन्म मिति सही छैन');
      return;
    }

    // Validate and convert time from 12-hour to 24-hour format
    final hour12 = int.tryParse(_hourController.text);
    final minute = int.tryParse(_minuteController.text);

    if (hour12 == null || minute == null || hour12 < 1 || hour12 > 12 || minute < 0 || minute > 59) {
      _showErrorSnackBar('जन्म समय सही छैन (घण्टा: 1-12, मिनेट: 0-59)');
      return;
    }

    // Convert 12-hour to 24-hour format
    int hour24;
    if (_selectedAmPm == 'AM') {
      if (hour12 == 12) {
        hour24 = 0; // 12 AM = 00:xx
      } else {
        hour24 = hour12; // 1-11 AM = 01-11:xx
      }
    } else { // PM
      if (hour12 == 12) {
        hour24 = 12; // 12 PM = 12:xx
      } else {
        hour24 = hour12 + 12; // 1-11 PM = 13-23:xx
      }
    }

    if (_selectedDistrict == null) {
      _showErrorSnackBar('जन्म स्थान छान्नुहोस्');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final birthTimeString = '${hour24.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      final birthDateAD = DateConverter.convertBStoAD(_selectedBSDate!);

      final userData = UserData(
        id: widget.editUser?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        gender: _selectedGender,
        birthDateBS: _selectedBSDate!,
        birthDateAD: birthDateAD,
        birthTime: birthTimeString,
        birthPlace: _selectedDistrict!.nameNepali, // Use district as birth place
        district: _selectedDistrict!.nameNepali,
        latitude: _selectedDistrict!.latitude,
        longitude: _selectedDistrict!.longitude,
        createdAt: widget.editUser?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      bool success;
      if (widget.editUser != null) {
        success = await UserDataService.updateUserData(userData);
      } else {
        success = await UserDataService.addUserData(userData);
      }

      if (success) {
        _showSuccessSnackBar(
          widget.editUser != null
            ? 'प्रयोगकर्ता जानकारी अपडेट भयो'
            : 'प्रयोगकर्ता जानकारी सुरक्षित भयो'
        );
        Navigator.pop(context, true);
      } else {
        _showErrorSnackBar('डेटा सुरक्षित गर्न असफल');
      }
    } catch (e) {
      _showErrorSnackBar('त्रुटि: $e');
    }

    setState(() => _isLoading = false);
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
}

// District Picker Dialog with Search
class _DistrictPickerDialog extends StatefulWidget {
  @override
  State<_DistrictPickerDialog> createState() => _DistrictPickerDialogState();
}

class _DistrictPickerDialogState extends State<_DistrictPickerDialog> {
  String _searchQuery = '';
  List<NepaliDistrict> _filteredDistricts = NepaliDistrictsData.districts;

  void _filterDistricts(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredDistricts = NepaliDistrictsData.districts;
      } else {
        _filteredDistricts = NepaliDistrictsData.districts.where((district) {
          return district.nameNepali.contains(query) ||
                 district.name.toLowerCase().contains(query.toLowerCase()) ||
                 district.province.contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: const Text(
        'जन्म स्थान छान्नुहोस्',
        style: TextStyle(color: Color(0xFF1B5E20)),
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 450,
        child: Column(
          children: [
            // Search field
            TextField(
              style: const TextStyle(color: Color(0xFF1B5E20)),
              decoration: InputDecoration(
                hintText: 'जिल्ला खोज्नुहोस्...',
                hintStyle: const TextStyle(color: Color(0xFF66BB6A)),
                prefixIcon: const Icon(Icons.search, color: Color(0xFF2E7D32)),
                filled: true,
                fillColor: const Color(0xFFF1F8E9),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF4CAF50)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF81C784)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2E7D32), width: 2),
                ),
              ),
              onChanged: _filterDistricts,
            ),
            const SizedBox(height: 12),
            // Districts list
            Expanded(
              child: ListView.builder(
                itemCount: _filteredDistricts.length,
                itemBuilder: (context, index) {
                  final district = _filteredDistricts[index];
                  return ListTile(
                    title: Text(
                      district.nameNepali,
                      style: const TextStyle(color: Color(0xFF1B5E20), fontSize: 16),
                    ),
                    subtitle: Text(
                      district.province,
                      style: const TextStyle(color: Color(0xFF66BB6A), fontSize: 12),
                    ),
                    onTap: () => Navigator.pop(context, district),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('रद्द गर्नुहोस्', style: TextStyle(color: Color(0xFF2E7D32))),
        ),
      ],
    );
  }
}
