import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class AI12MonthPredictionPage extends StatefulWidget {
  final UserData user;

  const AI12MonthPredictionPage({Key? key, required this.user}) : super(key: key);

  @override
  State<AI12MonthPredictionPage> createState() => _AI12MonthPredictionPageState();
}

class _AI12MonthPredictionPageState extends State<AI12MonthPredictionPage> {
  bool _isLoading = false;
  dynamic _predictionResult;
  String? _error;
  int _selectedMonthIndex = 0; // Default to first month
  List<dynamic> _monthlyPredictions = [];

  @override
  void initState() {
    super.initState();
    _fetchAI12MonthPrediction();
  }

  UserData get user => widget.user;

  Future<void> _fetchAI12MonthPrediction() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getAI12MonthPrediction(user);

      setState(() {
        _predictionResult = result;

        // Extract monthly predictions from API response
        if (result is List && result.isNotEmpty) {
          _monthlyPredictions = result;
        } else if (result is Map && result.containsKey('response')) {
          _monthlyPredictions = result['response'] ?? [];
        } else {
          _monthlyPredictions = [];
        }

        _selectedMonthIndex = 0; // Reset to first month
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'AI १२ महिना भविष्यवाणी',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_monthlyPredictions.isNotEmpty) _buildMonthSelector(),
                if (_monthlyPredictions.isNotEmpty) const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_predictionResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'AI भविष्यवाणी तयार गर्दै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'कृपया पर्खनुहोस्, यसले केही समय लाग्न सक्छ',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF388E3C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.calendar_month,
                color: Color(0xFF2E7D32),
                size: 24,
              ),
              SizedBox(width: 12),
              Text(
                'महिना छान्नुहोस्',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Month selection grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 2.5,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: _monthlyPredictions.length,
            itemBuilder: (context, index) {
              final isSelected = index == _selectedMonthIndex;
              final monthData = _monthlyPredictions[index];

              // Extract month info from specifications
              String monthName = _getMonthName(index + 1);
              if (monthData is Map && monthData.containsKey('specifications')) {
                final specs = monthData['specifications'];
                if (specs is Map && specs.containsKey('start_month')) {
                  monthName = _translateMonthName(specs['start_month'].toString());
                }
              }

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedMonthIndex = index;
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF4CAF50)
                        : const Color(0xFFF1F8E9),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF2E7D32)
                          : const Color(0xFF4CAF50).withOpacity(0.3),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      monthName,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? Colors.white : const Color(0xFF2E7D32),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchAI12MonthPrediction,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'AI १२ महिना भविष्यवाणी',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_predictionResult != null)
            _buildPredictionContent(),
        ],
      ),
    );
  }

  Widget _buildPredictionContent() {
    // Show only selected month to prevent hanging
    if (_monthlyPredictions.isNotEmpty && _selectedMonthIndex < _monthlyPredictions.length) {
      final selectedMonthData = _monthlyPredictions[_selectedMonthIndex];

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMonthSpecifications(selectedMonthData),
          const SizedBox(height: 16),
          _buildMonthPredictions(selectedMonthData),
        ],
      );
    }

    // Handle Map format (general predictions)
    else if (_predictionResult is Map<String, dynamic>) {
      final data = _predictionResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all key-value pairs from the result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getPredictionIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _formatKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else if (_predictionResult is String) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFF1F8E9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Color(0xFF2E7D32),
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'AI भविष्यवाणी',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _predictionResult.toString(),
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                height: 1.4,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_predictionResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  Widget _buildMonthSpecifications(dynamic monthData) {
    if (monthData is! Map || !monthData.containsKey('specifications')) {
      return const SizedBox.shrink();
    }

    final specs = monthData['specifications'] as Map<String, dynamic>;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Color(0xFF2E7D32),
                size: 24,
              ),
              SizedBox(width: 12),
              Text(
                'महिना विवरण',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          ...specs.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      '${_formatSpecKey(entry.key)}:',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      entry.value.toString(),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildMonthPredictions(dynamic monthData) {
    if (monthData is! Map || !monthData.containsKey('predictions')) {
      return const SizedBox.shrink();
    }

    final predictions = monthData['predictions'] as Map<String, dynamic>;

    return Column(
      children: predictions.entries.map((entry) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFFF1F8E9),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF4CAF50).withOpacity(0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category Header
              Row(
                children: [
                  Icon(
                    _getPredictionCategoryIcon(entry.key),
                    color: const Color(0xFF2E7D32),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _formatPredictionCategory(entry.key),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Prediction Details
              if (entry.value is Map<String, dynamic>)
                _buildPredictionDetails(entry.value as Map<String, dynamic>),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPredictionDetails(Map<String, dynamic> predictionData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Probability and Outcome
        if (predictionData.containsKey('probability') || predictionData.containsKey('outcome'))
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                if (predictionData.containsKey('probability'))
                  Expanded(
                    child: Row(
                      children: [
                        const Icon(Icons.trending_up, size: 16, color: Color(0xFF2E7D32)),
                        const SizedBox(width: 4),
                        Text(
                          'सम्भावना: ${_translateProbability(predictionData['probability'])}',
                          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  ),
                if (predictionData.containsKey('outcome'))
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          predictionData['outcome'] == 'positive' ? Icons.thumb_up : Icons.thumb_down,
                          size: 16,
                          color: predictionData['outcome'] == 'positive' ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'परिणाम: ${_translateOutcome(predictionData['outcome'])}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: predictionData['outcome'] == 'positive' ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

        // Main Prediction Text
        if (predictionData.containsKey('bot_response'))
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.auto_awesome, size: 16, color: Color(0xFF2E7D32)),
                    SizedBox(width: 8),
                    Text(
                      'AI भविष्यवाणी',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  predictionData['bot_response'].toString(),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    height: 1.5,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Helper methods for month-wise predictions
  String _translateMonthName(String englishMonth) {
    switch (englishMonth.toLowerCase()) {
      case 'january': return 'जनवरी';
      case 'february': return 'फेब्रुअरी';
      case 'march': return 'मार्च';
      case 'april': return 'अप्रिल';
      case 'may': return 'मे';
      case 'june': return 'जुन';
      case 'july': return 'जुलाई';
      case 'august': return 'अगस्त';
      case 'september': return 'सेप्टेम्बर';
      case 'october': return 'अक्टोबर';
      case 'november': return 'नोभेम्बर';
      case 'december': return 'डिसेम्बर';
      default: return englishMonth;
    }
  }

  String _formatSpecKey(String key) {
    switch (key.toLowerCase()) {
      case 'start_date': return 'सुरु मिति';
      case 'start_month': return 'सुरु महिना';
      case 'end_date': return 'अन्त्य मिति';
      case 'end_month': return 'अन्त्य महिना';
      case 'dasha': return 'दशा';
      case 'dasha_end': return 'दशा अन्त्य';
      case 'dasha_inflection': return 'दशा परिवर्तन';
      default: return key.replaceAll('_', ' ');
    }
  }

  IconData _getPredictionCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'relationship': return Icons.favorite;
      case 'education': return Icons.school;
      case 'career': return Icons.work;
      case 'finance': return Icons.monetization_on;
      case 'house_and_vehicle': return Icons.home;
      case 'health': return Icons.health_and_safety;
      case 'family': return Icons.family_restroom;
      default: return Icons.auto_awesome;
    }
  }

  String _formatPredictionCategory(String category) {
    switch (category.toLowerCase()) {
      case 'relationship': return 'प्रेम सम्बन्ध';
      case 'education': return 'शिक्षा';
      case 'career': return 'करियर';
      case 'finance': return 'आर्थिक';
      case 'house_and_vehicle': return 'घर र सवारी';
      case 'health': return 'स्वास्थ्य';
      case 'family': return 'पारिवारिक';
      default: return category.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _translateProbability(dynamic probability) {
    if (probability == null) return 'अज्ञात';
    switch (probability.toString().toLowerCase()) {
      case 'high': return 'उच्च';
      case 'medium': return 'मध्यम';
      case 'low': return 'कम';
      default: return probability.toString();
    }
  }

  String _translateOutcome(dynamic outcome) {
    if (outcome == null) return 'अज्ञात';
    switch (outcome.toString().toLowerCase()) {
      case 'positive': return 'सकारात्मक';
      case 'negative': return 'नकारात्मक';
      case 'neutral': return 'तटस्थ';
      default: return outcome.toString();
    }
  }

  String _getMonthName(int monthNumber) {
    const monthNames = [
      'जनवरी', 'फेब्रुअरी', 'मार्च', 'अप्रिल', 'मे', 'जुन',
      'जुलाई', 'अगस्त', 'सेप्टेम्बर', 'अक्टोबर', 'नोभेम्बर', 'डिसेम्बर'
    ];

    if (monthNumber >= 1 && monthNumber <= 12) {
      return monthNames[monthNumber - 1];
    }
    return '${monthNumber}औं';
  }

  IconData _getPredictionFieldIcon(String key) {
    switch (key.toLowerCase()) {
      case 'prediction':
      case 'forecast':
      case 'monthly_prediction':
        return Icons.auto_awesome;
      case 'career':
      case 'profession':
        return Icons.work;
      case 'health':
        return Icons.health_and_safety;
      case 'wealth':
      case 'finance':
      case 'money':
        return Icons.monetization_on;
      case 'love':
      case 'relationship':
        return Icons.favorite;
      case 'family':
        return Icons.family_restroom;
      case 'education':
        return Icons.school;
      case 'travel':
        return Icons.flight;
      case 'lucky_days':
      case 'auspicious':
        return Icons.star;
      case 'challenges':
      case 'obstacles':
        return Icons.warning;
      case 'opportunities':
        return Icons.trending_up;
      case 'advice':
      case 'remedies':
        return Icons.lightbulb;
      case 'summary':
        return Icons.summarize;
      case 'overview':
        return Icons.visibility;
      default:
        return Icons.auto_awesome;
    }
  }

  String _formatPredictionKey(String key) {
    switch (key.toLowerCase()) {
      case 'prediction':
      case 'forecast':
      case 'monthly_prediction':
        return 'मासिक भविष्यवाणी';
      case 'career':
      case 'profession':
        return 'करियर';
      case 'health':
        return 'स्वास्थ्य';
      case 'wealth':
      case 'finance':
      case 'money':
        return 'आर्थिक';
      case 'love':
      case 'relationship':
        return 'प्रेम सम्बन्ध';
      case 'family':
        return 'पारिवारिक';
      case 'education':
        return 'शिक्षा';
      case 'travel':
        return 'यात्रा';
      case 'lucky_days':
      case 'auspicious':
        return 'भाग्यशाली दिनहरू';
      case 'challenges':
      case 'obstacles':
        return 'चुनौतीहरू';
      case 'opportunities':
        return 'अवसरहरू';
      case 'advice':
      case 'remedies':
        return 'सल्लाह';
      case 'summary':
        return 'सारांश';
      case 'overview':
        return 'सिंहावलोकन';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildPredictionValueWidget(dynamic value) {
    if (value == null) return const SizedBox.shrink();

    String displayValue = value.toString();

    return Text(
      displayValue,
      style: const TextStyle(
        fontSize: 15,
        color: Colors.black87,
        height: 1.4,
      ),
    );
  }

  IconData _getPredictionIcon(String key) {
    switch (key.toLowerCase()) {
      case 'prediction':
      case 'forecast':
        return Icons.auto_awesome;
      case 'month':
      case 'monthly':
        return Icons.calendar_month;
      case 'career':
      case 'profession':
        return Icons.work;
      case 'health':
        return Icons.health_and_safety;
      case 'relationships':
      case 'love':
        return Icons.favorite;
      case 'finance':
      case 'money':
        return Icons.monetization_on;
      case 'family':
        return Icons.family_restroom;
      case 'education':
        return Icons.school;
      case 'travel':
        return Icons.flight;
      case 'spiritual':
        return Icons.self_improvement;
      case 'lucky':
        return Icons.star;
      case 'challenges':
        return Icons.warning;
      case 'opportunities':
        return Icons.trending_up;
      case 'advice':
        return Icons.lightbulb;
      case 'summary':
        return Icons.summarize;
      case 'overview':
        return Icons.visibility;
      case 'january':
      case 'february':
      case 'march':
      case 'april':
      case 'may':
      case 'june':
      case 'july':
      case 'august':
      case 'september':
      case 'october':
      case 'november':
      case 'december':
        return Icons.calendar_today;
      default:
        return Icons.info;
    }
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'prediction':
        return 'भविष्यवाणी';
      case 'forecast':
        return 'पूर्वानुमान';
      case 'month':
        return 'महिना';
      case 'monthly':
        return 'मासिक';
      case 'career':
        return 'करियर';
      case 'profession':
        return 'व्यवसाय';
      case 'health':
        return 'स्वास्थ्य';
      case 'relationships':
        return 'सम्बन्धहरू';
      case 'love':
        return 'प्रेम';
      case 'finance':
        return 'आर्थिक';
      case 'money':
        return 'धन';
      case 'family':
        return 'पारिवारिक';
      case 'education':
        return 'शिक्षा';
      case 'travel':
        return 'यात्रा';
      case 'spiritual':
        return 'आध्यात्मिक';
      case 'lucky':
        return 'भाग्यशाली';
      case 'challenges':
        return 'चुनौतीहरू';
      case 'opportunities':
        return 'अवसरहरू';
      case 'advice':
        return 'सल्लाह';
      case 'summary':
        return 'सारांश';
      case 'overview':
        return 'सिंहावलोकन';
      case 'january':
        return 'जनवरी';
      case 'february':
        return 'फेब्रुअरी';
      case 'march':
        return 'मार्च';
      case 'april':
        return 'अप्रिल';
      case 'may':
        return 'मे';
      case 'june':
        return 'जुन';
      case 'july':
        return 'जुलाई';
      case 'august':
        return 'अगस्त';
      case 'september':
        return 'सेप्टेम्बर';
      case 'october':
        return 'अक्टोबर';
      case 'november':
        return 'नोभेम्बर';
      case 'december':
        return 'डिसेम्बर';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      case 'ai_prediction':
        return 'AI भविष्यवाणी';
      case 'analysis':
        return 'विश्लेषण';
      case 'description':
        return 'विवरण';
      case 'details':
        return 'विवरणहरू';
      case 'recommendations':
        return 'सिफारिसहरू';
      case 'suggestions':
        return 'सुझावहरू';
      case 'remedies':
        return 'उपायहरू';
      case 'strengths':
        return 'शक्तिहरू';
      case 'weaknesses':
        return 'कमजोरीहरू';
      case 'favorable':
        return 'अनुकूल';
      case 'unfavorable':
        return 'प्रतिकूल';
      case 'periods':
        return 'अवधिहरू';
      case 'timing':
        return 'समय';
      case 'events':
        return 'घटनाहरू';
      case 'changes':
        return 'परिवर्तनहरू';
      case 'growth':
        return 'वृद्धि';
      case 'progress':
        return 'प्रगति';
      case 'success':
        return 'सफलता';
      case 'achievements':
        return 'उपलब्धिहरू';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
