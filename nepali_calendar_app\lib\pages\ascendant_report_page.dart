import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class AscendantReportPage extends StatefulWidget {
  final UserData user;

  const AscendantReportPage({Key? key, required this.user}) : super(key: key);

  @override
  State<AscendantReportPage> createState() => _AscendantReportPageState();
}

class _AscendantReportPageState extends State<AscendantReportPage> {
  bool _isLoading = false;
  dynamic _ascendantReportResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchAscendantReport();
  }

  UserData get user => widget.user;

  Future<void> _fetchAscendantReport() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getAscendantReport(user);
      
      setState(() {
        _ascendantReportResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'लग्न रिपोर्ट',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'लग्न रिपोर्ट तयार गर्दै...',
                  featureName: 'लग्न रिपोर्ट',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchAscendantReport,
                  featureName: 'लग्न रिपोर्ट',
                ),
                if (_ascendantReportResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.home,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'लग्न रिपोर्ट लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchAscendantReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.home,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'लग्न विश्लेषण',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_ascendantReportResult != null)
            _buildAscendantReportContent(),
        ],
      ),
    );
  }

  Widget _buildAscendantReportContent() {
    // Handle both List and Map formats from API
    Map<String, dynamic> data;

    if (_ascendantReportResult is List && (_ascendantReportResult as List).isNotEmpty) {
      // API returns a list with single object
      data = (_ascendantReportResult as List)[0] as Map<String, dynamic>;
    } else if (_ascendantReportResult is Map<String, dynamic>) {
      // Direct map format
      data = _ascendantReportResult as Map<String, dynamic>;
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_ascendantReportResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display all key-value pairs from the result
        ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getReportIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _formatKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
  }

  Widget _buildStringResult() {
    if (_ascendantReportResult is String) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFF1F8E9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.description,
                  color: Color(0xFF2E7D32),
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'लग्न रिपोर्ट',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _ascendantReportResult.toString(),
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                height: 1.4,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.warning, color: Colors.orange, size: 32),
          const SizedBox(height: 12),
          const Text(
            'अप्रत्याशित डेटा ढाँचा',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'प्राप्त डेटा: ${_ascendantReportResult.toString()}',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  IconData _getReportIcon(String key) {
    switch (key.toLowerCase()) {
      case 'ascendant':
      case 'lagna':
        return Icons.home;
      case 'ascendant_lord':
      case 'ascendant_lord_location':
      case 'ascendant_lord_house_location':
      case 'ascendant_lord_strength':
        return Icons.account_balance;
      case 'general_prediction':
      case 'personalised_prediction':
        return Icons.auto_awesome;
      case 'verbal_location':
        return Icons.location_on;
      case 'symbol':
        return Icons.pets;
      case 'zodiac_characteristics':
        return Icons.star_border;
      case 'lucky_gem':
        return Icons.diamond;
      case 'day_for_fasting':
        return Icons.calendar_today;
      case 'gayatri_mantra':
        return Icons.self_improvement;
      case 'flagship_qualities':
      case 'good_qualities':
        return Icons.thumb_up;
      case 'bad_qualities':
        return Icons.thumb_down;
      case 'spirituality_advice':
        return Icons.spa;
      case 'personality':
      case 'character':
        return Icons.person;
      case 'career':
      case 'profession':
        return Icons.work;
      case 'health':
        return Icons.health_and_safety;
      case 'wealth':
      case 'finance':
        return Icons.monetization_on;
      case 'marriage':
      case 'relationship':
        return Icons.favorite;
      case 'education':
        return Icons.school;
      case 'family':
        return Icons.family_restroom;
      case 'spiritual':
        return Icons.self_improvement;
      case 'lucky':
        return Icons.star;
      case 'remedies':
        return Icons.healing;
      case 'strengths':
        return Icons.thumb_up;
      case 'weaknesses':
        return Icons.thumb_down;
      case 'general':
      case 'overview':
        return Icons.description;
      case 'predictions':
        return Icons.auto_awesome;
      case 'recommendations':
        return Icons.lightbulb;
      default:
        return Icons.info;
    }
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'ascendant':
        return 'लग्न';
      case 'lagna':
        return 'लग्न';
      case 'ascendant_lord':
        return 'लग्न स्वामी';
      case 'ascendant_lord_location':
        return 'लग्न स्वामी स्थान';
      case 'ascendant_lord_house_location':
        return 'लग्न स्वामी घर स्थान';
      case 'general_prediction':
        return 'सामान्य भविष्यवाणी';
      case 'personalised_prediction':
        return 'व्यक्तिगत भविष्यवाणी';
      case 'verbal_location':
        return 'मौखिक स्थान';
      case 'ascendant_lord_strength':
        return 'लग्न स्वामी शक्ति';
      case 'symbol':
        return 'प्रतीक';
      case 'zodiac_characteristics':
        return 'राशि विशेषताहरू';
      case 'lucky_gem':
        return 'भाग्यशाली रत्न';
      case 'day_for_fasting':
        return 'उपवासको दिन';
      case 'gayatri_mantra':
        return 'गायत्री मन्त्र';
      case 'flagship_qualities':
        return 'मुख्य गुणहरू';
      case 'spirituality_advice':
        return 'आध्यात्मिक सल्लाह';
      case 'good_qualities':
        return 'राम्रा गुणहरू';
      case 'bad_qualities':
        return 'नराम्रा गुणहरू';
      case 'personality':
        return 'व्यक्तित्व';
      case 'character':
        return 'चरित्र';
      case 'career':
        return 'करियर';
      case 'profession':
        return 'व्यवसाय';
      case 'health':
        return 'स्वास्थ्य';
      case 'wealth':
        return 'धन';
      case 'finance':
        return 'आर्थिक';
      case 'marriage':
        return 'विवाह';
      case 'relationship':
        return 'सम्बन्ध';
      case 'education':
        return 'शिक्षा';
      case 'family':
        return 'पारिवारिक';
      case 'spiritual':
        return 'आध्यात्मिक';
      case 'lucky':
        return 'भाग्यशाली';
      case 'remedies':
        return 'उपायहरू';
      case 'strengths':
        return 'शक्तिहरू';
      case 'weaknesses':
        return 'कमजोरीहरू';
      case 'general':
        return 'सामान्य';
      case 'overview':
        return 'सिंहावलोकन';
      case 'predictions':
        return 'भविष्यवाणीहरू';
      case 'recommendations':
        return 'सिफारिसहरू';
      case 'description':
        return 'विवरण';
      case 'meaning':
        return 'अर्थ';
      case 'significance':
        return 'महत्व';
      case 'effects':
        return 'प्रभावहरू';
      case 'characteristics':
        return 'विशेषताहरू';
      case 'traits':
        return 'गुणहरू';
      case 'nature':
        return 'प्रकृति';
      case 'temperament':
        return 'स्वभाव';
      case 'behavior':
        return 'व्यवहार';
      case 'attitude':
        return 'मनोवृत्ति';
      case 'approach':
        return 'दृष्टिकोण';
      case 'lifestyle':
        return 'जीवनशैली';
      case 'preferences':
        return 'प्राथमिकताहरू';
      case 'tendencies':
        return 'प्रवृत्तिहरू';
      case 'inclinations':
        return 'झुकावहरू';
      case 'compatibility':
        return 'मेल';
      case 'challenges':
        return 'चुनौतीहरू';
      case 'opportunities':
        return 'अवसरहरू';
      case 'potential':
        return 'सम्भावना';
      case 'talents':
        return 'प्रतिभाहरू';
      case 'skills':
        return 'सीपहरू';
      case 'abilities':
        return 'क्षमताहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
