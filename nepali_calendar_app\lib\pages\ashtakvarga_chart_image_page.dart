import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class AshtakvargaChartImagePage extends StatefulWidget {
  final UserData user;

  const AshtakvargaChartImagePage({Key? key, required this.user}) : super(key: key);

  @override
  State<AshtakvargaChartImagePage> createState() => _AshtakvargaChartImagePageState();
}

class _AshtakvargaChartImagePageState extends State<AshtakvargaChartImagePage> {
  bool _isLoading = false;
  dynamic _ashtakvargaChartResult;
  String? _error;
  String? _svgContent;
  String _selectedPlanet = 'total';

  final List<Map<String, String>> _planets = [
    {'code': 'total', 'name': 'कुल अष्टकवर्ग', 'description': 'सम्पूर्ण अष्टकवर्ग चार्ट', 'icon': '🌟'},
    {'code': 'Sun', 'name': 'सूर्य अष्टकवर्ग', 'description': 'सूर्य ग्रहको अष्टकवर्ग', 'icon': '☀️'},
    {'code': 'Moon', 'name': 'चन्द्र अष्टकवर्ग', 'description': 'चन्द्र ग्रहको अष्टकवर्ग', 'icon': '🌙'},
    {'code': 'Mars', 'name': 'मंगल अष्टकवर्ग', 'description': 'मंगल ग्रहको अष्टकवर्ग', 'icon': '🔴'},
    {'code': 'Mercury', 'name': 'बुध अष्टकवर्ग', 'description': 'बुध ग्रहको अष्टकवर्ग', 'icon': '💫'},
    {'code': 'Jupiter', 'name': 'बृहस्पति अष्टकवर्ग', 'description': 'बृहस्पति ग्रहको अष्टकवर्ग', 'icon': '🪐'},
    {'code': 'Venus', 'name': 'शुक्र अष्टकवर्ग', 'description': 'शुक्र ग्रहको अष्टकवर्ग', 'icon': '💖'},
    {'code': 'Saturn', 'name': 'शनि अष्टकवर्ग', 'description': 'शनि ग्रहको अष्टकवर्ग', 'icon': '⏳'},
  ];

  @override
  void initState() {
    super.initState();
    _fetchAshtakvargaChart();
  }

  UserData get user => widget.user;

  Future<void> _fetchAshtakvargaChart() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _ashtakvargaChartResult = null;
      _svgContent = null;
    });

    try {
      print('📱 Ashtakvarga Page: Starting API call for $_selectedPlanet');
      final result = await VedicAstroApiService.getAshtakvargaChartImage(user, _selectedPlanet);

      print('📱 Ashtakvarga Page: Received result type: ${result.runtimeType}');
      print('📱 Ashtakvarga Page: Result content: ${result.toString().substring(0, result.toString().length > 300 ? 300 : result.toString().length)}...');

      setState(() {
        _ashtakvargaChartResult = result;
        // Extract content from the result
        if (result is String) {
          print('📱 Ashtakvarga Page: Result is String, length: ${result.length}');
          _svgContent = result;
        } else if (result is Map) {
          print('📱 Ashtakvarga Page: Result is Map with keys: ${result.keys.toList()}');
          _svgContent = result['svg_content'] ??
                       result['chart_image'] ??
                       result['response'] ??
                       result.toString();
          print('📱 Ashtakvarga Page: Extracted content length: ${_svgContent?.length ?? 0}');
        } else {
          print('📱 Ashtakvarga Page: Unknown result type, converting to string');
          _svgContent = result.toString();
        }

        print('📱 Ashtakvarga Page: Final SVG content set: ${_svgContent != null ? "Yes (${_svgContent!.length} chars)" : "No"}');
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'अष्टकवर्ग चार्ट छविहरू',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                _buildPlanetSelector(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'अष्टकवर्ग चार्ट तयार गर्दै...',
                  featureName: 'अष्टकवर्ग चार्ट',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchAshtakvargaChart,
                  featureName: 'अष्टकवर्ग चार्ट',
                ),
                if (_svgContent != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.grid_3x3,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ग्रह छान्नुहोस्',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 16),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 2.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _planets.length,
            itemBuilder: (context, index) {
              final planet = _planets[index];
              final isSelected = _selectedPlanet == planet['code'];
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedPlanet = planet['code']!;
                  });
                  _fetchAshtakvargaChart();
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? const Color(0xFF4CAF50) 
                        : const Color(0xFF4CAF50).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: const Color(0xFF4CAF50),
                      width: 2,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            planet['icon']!,
                            style: const TextStyle(fontSize: 16),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              planet['name']!,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : const Color(0xFF2E7D32),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        planet['code']!,
                        style: TextStyle(
                          fontSize: 12,
                          color: isSelected ? Colors.white70 : const Color(0xFF388E3C),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }



  Widget _buildResultWidget() {
    final selectedPlanet = _planets.firstWhere((p) => p['code'] == _selectedPlanet);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                selectedPlanet['icon']!,
                style: const TextStyle(fontSize: 36),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      selectedPlanet['name']!,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    Text(
                      selectedPlanet['description']!,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          if (_svgContent != null)
            _buildAshtakvargaChartContent(),
        ],
      ),
    );
  }

  Widget _buildAshtakvargaChartContent() {
    if (_svgContent != null && _svgContent!.isNotEmpty) {
      try {
        // Debug: Show what we received
        print('Ashtakvarga Chart Content: ${_svgContent!.substring(0, _svgContent!.length > 100 ? 100 : _svgContent!.length)}...');

        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      const Icon(Icons.grid_on, color: Color(0xFF2E7D32), size: 28),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'अष्टकवर्ग चार्ट',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          _showFullScreenChart(_svgContent!);
                        },
                        icon: const Icon(Icons.fullscreen, color: Color(0xFF2E7D32)),
                        tooltip: 'पूर्ण स्क्रिनमा हेर्नुहोस्',
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // SVG Chart
                  Container(
                    height: 400,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: _buildChartContent(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  const Text(
                    'यो चार्टले तपाईंको अष्टकवर्ग स्कोर देखाउँछ',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF2E7D32),
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        );
      } catch (e) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 32),
              const SizedBox(height: 12),
              const Text(
                'चार्ट लोड गर्न सकिएन',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'त्रुटि: $e',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        );
      }
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_svgContent ?? "कुनै डेटा छैन"}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  void _showFullScreenChart(String svgContent) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: const Text(
              'अष्टकवर्ग चार्ट - पूर्ण स्क्रिन',
              style: TextStyle(color: Colors.white),
            ),
            actions: [
              IconButton(
                onPressed: () {
                  // Add save functionality if needed
                },
                icon: const Icon(Icons.download),
                tooltip: 'डाउनलोड गर्नुहोस्',
              ),
            ],
          ),
          body: Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4.0,
              child: Container(
                color: Colors.white,
                child: SvgPicture.string(
                  svgContent,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChartContent() {
    if (_svgContent == null || _svgContent!.isEmpty) {
      return const Center(
        child: Text('चार्ट डेटा उपलब्ध छैन'),
      );
    }

    // Debug: Show what we received
    print('📱 Ashtakvarga Content Length: ${_svgContent!.length}');
    print('📱 Ashtakvarga Content Start: ${_svgContent!.substring(0, _svgContent!.length > 100 ? 100 : _svgContent!.length)}...');

    // According to API docs, the response is SVG content as string
    // Check if it contains SVG tags
    if (_svgContent!.contains('<svg') && _svgContent!.contains('</svg>')) {
      print('📱 Ashtakvarga: Detected SVG content, rendering with flutter_svg');
      return SvgPicture.string(
        _svgContent!,
        fit: BoxFit.contain,
        width: double.infinity,
        height: 400,
        placeholderBuilder: (context) => const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 8),
              Text('चार्ट लोड गर्दै...'),
            ],
          ),
        ),
      );
    } else {
      print('📱 Ashtakvarga: No SVG tags found in content');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            const Text(
              'SVG फर्म्याट समस्या',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_svgContent!.length > 50 ? _svgContent!.substring(0, 50) + "..." : _svgContent!}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
  }
}
