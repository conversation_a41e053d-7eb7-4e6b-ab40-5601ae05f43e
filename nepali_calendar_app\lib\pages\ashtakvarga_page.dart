import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class AshtakvargaPage extends StatefulWidget {
  final UserData user;

  const AshtakvargaPage({Key? key, required this.user}) : super(key: key);

  @override
  State<AshtakvargaPage> createState() => _AshtakvargaPageState();
}

class _AshtakvargaPageState extends State<AshtakvargaPage> {
  bool _isLoading = false;
  dynamic _ashtakvargaResult;
  String? _error;
  String _selectedPlanet = 'Sun';

  final List<Map<String, String>> _planets = [
    {'code': 'Sun', 'name': 'सूर्य अष्टकवर्ग', 'description': 'सूर्य ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '☀️'},
    {'code': 'Moon', 'name': 'चन्द्र अष्टकवर्ग', 'description': 'चन्द्र ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '🌙'},
    {'code': 'Mars', 'name': 'मंगल अष्टकवर्ग', 'description': 'मंगल ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '🔴'},
    {'code': 'Mercury', 'name': 'बुध अष्टकवर्ग', 'description': 'बुध ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '💫'},
    {'code': 'Jupiter', 'name': 'बृहस्पति अष्टकवर्ग', 'description': 'बृहस्पति ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '🪐'},
    {'code': 'Venus', 'name': 'शुक्र अष्टकवर्ग', 'description': 'शुक्र ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '💖'},
    {'code': 'Saturn', 'name': 'शनि अष्टकवर्ग', 'description': 'शनि ग्रहको अष्टकवर्ग विश्लेषण', 'icon': '⏳'},
  ];

  @override
  void initState() {
    super.initState();
    _fetchAshtakvarga();
  }

  UserData get user => widget.user;

  Future<void> _fetchAshtakvarga() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getAshtakvarga(user, _selectedPlanet);
      
      setState(() {
        _ashtakvargaResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'अष्टकवर्ग विश्लेषण',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                _buildPlanetSelector(),
                const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_ashtakvargaResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.calculate,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ग्रह छान्नुहोस्',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 16),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _planets.map((planet) {
              final isSelected = _selectedPlanet == planet['code'];
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedPlanet = planet['code']!;
                  });
                  _fetchAshtakvarga();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? const Color(0xFF4CAF50) 
                        : const Color(0xFF4CAF50).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: const Color(0xFF4CAF50),
                      width: 2,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        planet['icon']!,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        planet['name']!,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : const Color(0xFF2E7D32),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    final selectedPlanet = _planets.firstWhere((p) => p['code'] == _selectedPlanet);

    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          const SizedBox(height: 20),
          Text(
            '${selectedPlanet['name']} लोड हुँदै...',
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchAshtakvarga,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    final selectedPlanet = _planets.firstWhere((p) => p['code'] == _selectedPlanet);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                selectedPlanet['icon']!,
                style: const TextStyle(fontSize: 36),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      selectedPlanet['name']!,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    Text(
                      selectedPlanet['description']!,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          if (_ashtakvargaResult != null)
            _buildAshtakvargaContent(),
        ],
      ),
    );
  }

  Widget _buildAshtakvargaContent() {
    if (_ashtakvargaResult is Map<String, dynamic>) {
      final data = _ashtakvargaResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all key-value pairs from the result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getAshtakvargaIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _formatKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else if (_ashtakvargaResult is String) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFF1F8E9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: Color(0xFF2E7D32),
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'अष्टकवर्ग विश्लेषण',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _ashtakvargaResult.toString(),
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                height: 1.4,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_ashtakvargaResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  IconData _getAshtakvargaIcon(String key) {
    switch (key.toLowerCase()) {
      case 'ashtakvarga':
      case 'total':
        return Icons.calculate;
      case 'scores':
      case 'points':
        return Icons.score;
      case 'houses':
      case 'bhava':
        return Icons.home;
      case 'zodiac':
      case 'rashi':
        return Icons.circle;
      case 'strength':
      case 'power':
        return Icons.fitness_center;
      case 'weakness':
        return Icons.warning;
      case 'favorable':
      case 'good':
        return Icons.thumb_up;
      case 'unfavorable':
      case 'bad':
        return Icons.thumb_down;
      case 'analysis':
        return Icons.analytics;
      case 'summary':
        return Icons.summarize;
      case 'details':
        return Icons.details;
      case 'chart':
        return Icons.grid_3x3;
      default:
        return Icons.info;
    }
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'ashtakvarga':
        return 'अष्टकवर्ग';
      case 'total':
        return 'कुल';
      case 'scores':
        return 'अंकहरू';
      case 'points':
        return 'बिन्दुहरू';
      case 'houses':
        return 'भावहरू';
      case 'bhava':
        return 'भाव';
      case 'zodiac':
        return 'राशि';
      case 'rashi':
        return 'राशि';
      case 'strength':
        return 'शक्ति';
      case 'power':
        return 'बल';
      case 'weakness':
        return 'कमजोरी';
      case 'favorable':
        return 'अनुकूल';
      case 'good':
        return 'राम्रो';
      case 'unfavorable':
        return 'प्रतिकूल';
      case 'bad':
        return 'नराम्रो';
      case 'analysis':
        return 'विश्लेषण';
      case 'summary':
        return 'सारांश';
      case 'details':
        return 'विवरण';
      case 'chart':
        return 'चार्ट';
      case 'description':
        return 'विवरण';
      case 'meaning':
        return 'अर्थ';
      case 'significance':
        return 'महत्व';
      case 'effects':
        return 'प्रभावहरू';
      case 'characteristics':
        return 'विशेषताहरू';
      case 'traits':
        return 'गुणहरू';
      case 'nature':
        return 'प्रकृति';
      case 'temperament':
        return 'स्वभाव';
      case 'behavior':
        return 'व्यवहार';
      case 'attitude':
        return 'मनोवृत्ति';
      case 'approach':
        return 'दृष्टिकोण';
      case 'lifestyle':
        return 'जीवनशैली';
      case 'preferences':
        return 'प्राथमिकताहरू';
      case 'tendencies':
        return 'प्रवृत्तिहरू';
      case 'inclinations':
        return 'झुकावहरू';
      case 'compatibility':
        return 'मेल';
      case 'challenges':
        return 'चुनौतीहरू';
      case 'opportunities':
        return 'अवसरहरू';
      case 'potential':
        return 'सम्भावना';
      case 'talents':
        return 'प्रतिभाहरू';
      case 'skills':
        return 'सीपहरू';
      case 'abilities':
        return 'क्षमताहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
