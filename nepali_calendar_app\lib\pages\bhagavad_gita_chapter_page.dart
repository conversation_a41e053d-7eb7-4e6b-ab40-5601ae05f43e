import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/bhagavad_gita_model.dart';
import '../services/bhagavad_gita_service.dart';
import 'direct_video_player_page.dart';

class BhagavadGitaChapterPage extends StatefulWidget {
  final BhagavadGitaChapter chapter;

  const BhagavadGitaChapterPage({Key? key, required this.chapter}) : super(key: key);

  @override
  State<BhagavadGitaChapterPage> createState() => _BhagavadGitaChapterPageState();
}

class _BhagavadGitaChapterPageState extends State<BhagavadGitaChapterPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                  Expanded(
                    child: Text(
                      'अध्याय ${widget.chapter.chapterNumber}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.share, color: Colors.white),
                    onPressed: () => _shareChapter(),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Chapter Header Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.orange.withOpacity(0.1),
                            Colors.orange.withOpacity(0.2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.orange.withOpacity(0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Chapter Number
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.orange.withOpacity(0.8),
                                  Colors.orange,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(40),
                            ),
                            child: Center(
                              child: Text(
                                '${widget.chapter.chapterNumber}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // Titles
                          Text(
                            widget.chapter.titleNepali,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.chapter.titleSanskrit,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontStyle: FontStyle.italic,
                              color: Colors.grey[700],
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.chapter.titleEnglish,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 20),
                          
                          // Stats
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildStatItem(
                                Icons.format_list_numbered,
                                '${widget.chapter.verseCount}',
                                'श्लोकहरू',
                              ),
                              _buildStatItem(
                                Icons.access_time,
                                widget.chapter.duration,
                                'अवधि',
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Play Video Buttons
                    Row(
                      children: [
                        // In-App Play Button (Primary)
                        Expanded(
                          flex: 2,
                          child: ElevatedButton.icon(
                            onPressed: () => _playInApp(),
                            icon: const Icon(Icons.play_circle_fill, size: 28),
                            label: const Text(
                              'अब हेर्नुहोस्',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // More Options Button
                        Expanded(
                          flex: 1,
                          child: OutlinedButton.icon(
                            onPressed: () => _playVideo(),
                            icon: const Icon(Icons.more_horiz, size: 20),
                            label: const Text(
                              'अन्य',
                              style: TextStyle(
                                fontSize: 14,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.orange,
                              side: const BorderSide(color: Colors.orange),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Description Section
                    _buildSection(
                      'विवरण',
                      widget.chapter.description,
                      Icons.description,
                    ),

                    const SizedBox(height: 20),

                    // Summary Section
                    _buildSection(
                      'सारांश',
                      widget.chapter.summary,
                      Icons.summarize,
                    ),

                    const SizedBox(height: 24),

                    // Navigation Buttons
                    Row(
                      children: [
                        if (widget.chapter.chapterNumber > 1)
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _navigateToChapter(widget.chapter.chapterNumber - 1),
                              icon: const Icon(Icons.arrow_back),
                              label: Text(
                                'अघिल्लो अध्याय',
                                style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
                              ),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.orange,
                                side: const BorderSide(color: Colors.orange),
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        if (widget.chapter.chapterNumber > 1 && widget.chapter.chapterNumber < 18)
                          const SizedBox(width: 12),
                        if (widget.chapter.chapterNumber < 18)
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _navigateToChapter(widget.chapter.chapterNumber + 1),
                              icon: const Icon(Icons.arrow_forward),
                              label: Text(
                                'अर्को अध्याय',
                                style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, color: Colors.orange, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
      ],
    );
  }

  Widget _buildSection(String title, String content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.orange, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.withOpacity(0.2)),
          ),
          child: Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
        ),
      ],
    );
  }

  void _playVideo() async {
    // Show options dialog for different ways to play video
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'भिडियो कसरी हेर्नुहुन्छ?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
            const SizedBox(height: 20),

            // In-App Player Option (Recommended)
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.play_circle_fill, color: Colors.white),
              ),
              title: const Text(
                'App मा नै हेर्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('सिफारिस गरिएको - तुरुन्तै सुरु हुन्छ'),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'सिफारिस',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _playInApp();
              },
            ),

            // YouTube App Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.play_circle_fill, color: Colors.white),
              ),
              title: const Text(
                'YouTube App मा खोल्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('सिफारिस गरिएको'),
              onTap: () {
                Navigator.pop(context);
                _launchYouTubeApp();
              },
            ),

            // Browser Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.web, color: Colors.white),
              ),
              title: const Text(
                'Browser मा खोल्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('Web browser मा हेर्नुहोस्'),
              onTap: () {
                Navigator.pop(context);
                _launchYouTubeBrowser();
              },
            ),

            // Search Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.search, color: Colors.white),
              ),
              title: const Text(
                'YouTube मा खोज्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('भगवद गीता अध्याय खोज्नुहोस्'),
              onTap: () {
                Navigator.pop(context);
                _searchOnYouTube();
              },
            ),

            // Copy Link Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.copy, color: Colors.white),
              ),
              title: const Text(
                'लिंक कपी गर्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('भिडियो लिंक कपी गर्नुहोस्'),
              onTap: () {
                Navigator.pop(context);
                _copyVideoLink();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _playInApp() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DirectVideoPlayerPage(chapter: widget.chapter),
      ),
    );
  }

  void _launchYouTubeApp() async {
    final urls = BhagavadGitaService.getVideoUrls(widget.chapter.youtubeVideoId);

    bool launched = false;
    for (String url in urls) {
      try {
        if (await canLaunchUrl(Uri.parse(url))) {
          await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
          launched = true;
          break;
        }
      } catch (e) {
        continue; // Try next URL
      }
    }

    if (!launched) {
      _showErrorMessage('YouTube खोल्न सकिएन। कृपया YouTube app install गर्नुहोस्।');
    }
  }

  void _launchYouTubeBrowser() async {
    final urls = [
      BhagavadGitaService.getYouTubeUrl(widget.chapter.youtubeVideoId),
      BhagavadGitaService.getYouTubeMobileUrl(widget.chapter.youtubeVideoId),
      'https://youtu.be/${widget.chapter.youtubeVideoId}',
    ];

    bool launched = false;
    for (String url in urls) {
      try {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        launched = true;
        break;
      } catch (e) {
        continue; // Try next URL
      }
    }

    if (!launched) {
      _showErrorMessage('Browser मा खोल्न सकिएन। Internet connection जाँच गर्नुहोस्।');
    }
  }

  void _searchOnYouTube() async {
    final searchQuery = 'भगवद गीता अध्याय ${widget.chapter.chapterNumber} ${widget.chapter.titleNepali}';
    final encodedQuery = Uri.encodeComponent(searchQuery);
    final searchUrls = [
      'youtube://results?search_query=$encodedQuery',
      'https://www.youtube.com/results?search_query=$encodedQuery',
      'https://m.youtube.com/results?search_query=$encodedQuery',
    ];

    bool launched = false;
    for (String url in searchUrls) {
      try {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        launched = true;
        break;
      } catch (e) {
        continue;
      }
    }

    if (!launched) {
      _showErrorMessage('YouTube खोज खोल्न सकिएन');
    }
  }

  void _copyVideoLink() {
    final url = BhagavadGitaService.getYouTubeUrl(widget.chapter.youtubeVideoId);
    // Copy to clipboard (you can use clipboard package)
    _showSuccessMessage('लिंक कपी भयो!\n$url');
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _shareChapter() {
    // Implement share functionality
    final text = '${widget.chapter.titleNepali}\n${BhagavadGitaService.getYouTubeUrl(widget.chapter.youtubeVideoId)}';
    // You can use share_plus package here
  }

  void _navigateToChapter(int chapterNumber) {
    final chapter = BhagavadGitaService.getChapter(chapterNumber);
    if (chapter != null) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => BhagavadGitaChapterPage(chapter: chapter),
        ),
      );
    }
  }
}
