import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/bhagavad_gita_model.dart';
import '../services/bhagavad_gita_service.dart';
import 'bhagavad_gita_chapter_page.dart';
import 'direct_video_player_page.dart';

class BhagavadGitaPage extends StatefulWidget {
  const BhagavadGitaPage({Key? key}) : super(key: key);

  @override
  State<BhagavadGitaPage> createState() => _BhagavadGitaPageState();
}

class _BhagavadGitaPageState extends State<BhagavadGitaPage> {
  final List<BhagavadGitaChapter> _chapters = BhagavadGitaService.getAllChapters();
  final PlaylistInfo _playlistInfo = BhagavadGitaService.getPlaylistInfo();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            // Header with gradient
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                  const Expanded(
                    child: Text(
                      'श्रीमद भगवद गीता',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.playlist_play, color: Colors.white),
                    onPressed: () => _openFullPlaylist(),
                  ),
                ],
              ),
            ),

            // Playlist Info Card
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.orange.withOpacity(0.1),
                    Colors.orange.withOpacity(0.2),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.auto_stories,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'पूर्ण संग्रह',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                            ),
                            Text(
                              '${_chapters.length} अध्यायहरू • ${BhagavadGitaService.getTotalDuration()}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _playlistInfo.description,
                    style: const TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _openFullPlaylist(),
                          icon: const Icon(Icons.play_circle_fill),
                          label: const Text(
                            'पूरै प्लेलिस्ट हेर्नुहोस्',
                            style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Chapters List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _chapters.length,
                itemBuilder: (context, index) {
                  final chapter = _chapters[index];
                  return _buildChapterCard(chapter);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChapterCard(BhagavadGitaChapter chapter) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.withOpacity(0.1)),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _openChapterDetail(chapter),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Chapter Number Circle
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withOpacity(0.8),
                      Colors.orange,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    '${chapter.chapterNumber}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              
              // Chapter Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      chapter.titleNepali,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      chapter.titleEnglish,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.format_list_numbered,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${chapter.verseCount} श्लोकहरू',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontFamily: 'NotoSansDevanagari',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          chapter.duration,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Action Buttons
              Column(
                children: [
                  // Play Button
                  GestureDetector(
                    onTap: () => _playChapterInApp(chapter),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Info Button
                  GestureDetector(
                    onTap: () => _openChapterDetail(chapter),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.info_outline,
                        color: Colors.orange,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _playChapterInApp(BhagavadGitaChapter chapter) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DirectVideoPlayerPage(chapter: chapter),
      ),
    );
  }

  void _openChapterDetail(BhagavadGitaChapter chapter) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BhagavadGitaChapterPage(chapter: chapter),
      ),
    );
  }

  void _openFullPlaylist() async {
    // Show options for opening playlist
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'प्लेलिस्ट कसरी हेर्नुहुन्छ?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
            const SizedBox(height: 20),

            // YouTube App Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.playlist_play, color: Colors.white),
              ),
              title: const Text(
                'YouTube App मा खोल्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('सिफारिस गरिएको'),
              onTap: () {
                Navigator.pop(context);
                _launchPlaylistApp();
              },
            ),

            // Browser Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.web, color: Colors.white),
              ),
              title: const Text(
                'Browser मा खोल्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              subtitle: const Text('Web browser मा हेर्नुहोस्'),
              onTap: () {
                Navigator.pop(context);
                _launchPlaylistBrowser();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _launchPlaylistApp() async {
    final appUrl = BhagavadGitaService.getPlaylistAppUrl();
    final webUrl = BhagavadGitaService.getPlaylistUrl();

    try {
      // Try YouTube app first
      if (await canLaunchUrl(Uri.parse(appUrl))) {
        await launchUrl(Uri.parse(appUrl), mode: LaunchMode.externalApplication);
      } else {
        // Fallback to web
        await launchUrl(Uri.parse(webUrl), mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      _showErrorMessage('YouTube प्लेलिस्ट खोल्न सकिएन');
    }
  }

  void _launchPlaylistBrowser() async {
    final url = BhagavadGitaService.getPlaylistUrl();

    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      _showErrorMessage('Browser मा खोल्न सकिएन');
    }
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
