import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class CharDashaSubPage extends StatefulWidget {
  final UserData user;

  const CharDashaSubPage({Key? key, required this.user}) : super(key: key);

  @override
  State<CharDashaSubPage> createState() => _CharDashaSubPageState();
}

class _CharDashaSubPageState extends State<CharDashaSubPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _charDashaSubResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchCharDashaSub();
  }

  UserData get user => widget.user;

  Future<void> _fetchCharDashaSub() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getCharDashaSub(user);
      
      setState(() {
        _charDashaSubResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'चर दशा उप',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchCharDashaSub,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'चर दशा उप विश्लेषण गर्दै...',
                  featureName: 'चर दशा उप',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchCharDashaSub,
                  featureName: 'चर दशा उप',
                ),
                if (_charDashaSubResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day} • ${user.birthTime}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 20),
          Text(
            'चर दशा उप विश्लेषण गरिँदै...',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF1B5E20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchCharDashaSub,
            icon: const Icon(Icons.refresh),
            label: const Text('पुनः प्रयास गर्नुहोस्'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.subdirectory_arrow_right,
                color: Color(0xFF2E7D32),
                size: 32,
              ),
              SizedBox(width: 12),
              Text(
                'चर दशा उप विवरण',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Display all the result data
          if (_charDashaSubResult != null)
            _buildCharDashaSubContent(),
        ],
      ),
    );
  }

  Widget _buildCharDashaSubContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display each key-value pair from the result
        ..._charDashaSubResult!.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatKey(entry.key),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _formatValue(entry.value),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF1B5E20),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'char_dasha_sub_list':
        return 'चर दशा उप सूची';
      case 'char_sub_dasha_list':
        return 'चर उप दशा सूची';
      case 'sub_dasha_list':
        return 'उप दशा सूची';
      case 'sub_dasha_order':
        return 'उप दशा क्रम';
      case 'sub_dasha_dates':
        return 'उप दशा मितिहरू';
      case 'sub_dasha_start_dates':
        return 'उप दशा शुरुवात मितिहरू';
      case 'sub_dasha_end_dates':
        return 'उप दशा समाप्ति मितिहरू';
      case 'main_dasha':
        return 'मुख्य दशा';
      case 'current_sub_dasha':
        return 'वर्तमान उप दशा';
      case 'next_sub_dasha':
        return 'अर्को उप दशा';
      case 'sub_period_duration':
        return 'उप अवधि समयावधि';
      case 'remaining_period':
        return 'बाँकी अवधि';
      case 'sign':
        return 'राशि';
      case 'house':
        return 'भाव';
      case 'lord':
        return 'स्वामी';
      case 'start_date':
        return 'शुरुवात मिति';
      case 'end_date':
        return 'समाप्ति मिति';
      case 'duration':
        return 'अवधि';
      case 'effects':
        return 'प्रभावहरू';
      case 'predictions':
        return 'भविष्यवाणी';
      case 'remedies':
        return 'उपायहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      // Handle nested arrays for sub-dasha sequences
      if (value.isNotEmpty && value[0] is List) {
        return value.asMap().entries.map((entry) {
          final index = entry.key;
          final subList = entry.value as List;
          return '${index + 1}. ${subList.map((item) => _translateSign(item.toString())).join(', ')}';
        }).join('\n\n');
      } else {
        return value.map((item) => '• ${_translateSign(item.toString())}').join('\n');
      }
    } else if (value is Map) {
      return value.entries
          .map((e) => '${_formatKey(e.key.toString())}: ${_formatValue(e.value)}')
          .join('\n');
    } else {
      return _translateSign(value.toString());
    }
  }

  String _translateSign(String sign) {
    switch (sign.toLowerCase()) {
      case 'aries':
        return 'मेष';
      case 'taurus':
        return 'वृषभ';
      case 'gemini':
        return 'मिथुन';
      case 'cancer':
        return 'कर्कट';
      case 'leo':
        return 'सिंह';
      case 'virgo':
        return 'कन्या';
      case 'libra':
        return 'तुला';
      case 'scorpio':
        return 'वृश्चिक';
      case 'sagittarius':
        return 'धनु';
      case 'capricorn':
        return 'मकर';
      case 'aquarius':
        return 'कुम्भ';
      case 'pisces':
        return 'मीन';
      default:
        return sign;
    }
  }
}
