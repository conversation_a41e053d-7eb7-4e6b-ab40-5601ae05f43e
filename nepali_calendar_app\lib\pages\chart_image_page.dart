import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter_svg/flutter_svg.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class ChartImagePage extends StatefulWidget {
  final UserData user;

  const ChartImagePage({Key? key, required this.user}) : super(key: key);

  @override
  State<ChartImagePage> createState() => _ChartImagePageState();
}

class _ChartImagePageState extends State<ChartImagePage> {
  bool _isLoading = false;
  dynamic _chartImageResult;
  String? _error;
  String? _svgContent;
  String _selectedChart = 'D1';

  final List<Map<String, String>> _charts = [
    {'code': 'D1', 'name': 'लग्न चार्ट', 'description': 'मुख्य जन्म चार्ट'},
    {'code': 'D3', 'name': 'द्रेष्काण चार्ट', 'description': 'भाइबहिनी र साहस'},
    {'code': 'D4', 'name': 'चतुर्थांश चार्ट', 'description': 'भाग्य र सम्पत्ति'},
    {'code': 'D5', 'name': 'पञ्चमांश चार्ट', 'description': 'बुद्धि र शिक्षा'},
    {'code': 'D7', 'name': 'सप्तमांश चार्ट', 'description': 'सन्तान'},
    {'code': 'D8', 'name': 'अष्टमांश चार्ट', 'description': 'आयु र मृत्यु'},
    {'code': 'D9', 'name': 'नवमांश चार्ट', 'description': 'विवाह र धर्म'},
    {'code': 'D10', 'name': 'दशमांश चार्ट', 'description': 'करियर र व्यवसाय'},
    {'code': 'D12', 'name': 'द्वादशमांश चार्ट', 'description': 'माता-पिता'},
    {'code': 'D16', 'name': 'षोडशमांश चार्ट', 'description': 'सवारी साधन'},
    {'code': 'D20', 'name': 'विंशमांश चार्ट', 'description': 'आध्यात्मिकता'},
    {'code': 'D24', 'name': 'चतुर्विंशमांश चार्ट', 'description': 'शिक्षा र ज्ञान'},
    {'code': 'D27', 'name': 'सप्तविंशमांश चार्ट', 'description': 'शक्ति र बल'},
    {'code': 'D30', 'name': 'त्रिंशमांश चार्ट', 'description': 'दुःख र कष्ट'},
    {'code': 'D40', 'name': 'चत्वारिंशमांश चार्ट', 'description': 'मातृ वंश'},
    {'code': 'D45', 'name': 'पञ्चचत्वारिंशमांश चार्ट', 'description': 'पितृ वंश'},
    {'code': 'D60', 'name': 'षष्ठ्यंश चार्ट', 'description': 'पूर्व जन्म'},
    {'code': 'D2', 'name': 'होरा चार्ट', 'description': 'धन र सम्पत्ति'},
  ];

  @override
  void initState() {
    super.initState();
    _fetchChartImage();
  }

  UserData get user => widget.user;

  Future<void> _fetchChartImage() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _chartImageResult = null;
      _svgContent = null;
    });

    try {
      print('📱 Chart Image Page: Starting API call for $_selectedChart');
      print('📱 Chart Image Page: User data - DOB: ${user.formattedDateForAPI}, TOB: ${user.formattedTimeForAPI}');

      final result = await VedicAstroApiService.getChartImage(user, _selectedChart);

      print('📱 Chart Image Page: ✅ API call successful!');
      print('📱 Chart Image Page: Received result type: ${result.runtimeType}');
      print('📱 Chart Image Page: Result content: ${result.toString().substring(0, result.toString().length > 300 ? 300 : result.toString().length)}...');

      setState(() {
        _chartImageResult = result;

        print('📱 Chart Image Page: Raw result type: ${result.runtimeType}');
        print('📱 Chart Image Page: Raw result content: ${result.toString()}');

        // Extract content from the result
        if (result is String) {
          print('📱 Chart Image Page: Result is String, length: ${result.length}');
          _svgContent = result;
        } else if (result is Map) {
          print('📱 Chart Image Page: Result is Map with keys: ${result.keys.toList()}');

          // Try to find the actual SVG content in the response
          if (result.containsKey('response')) {
            _svgContent = result['response'];
            print('📱 Chart Image Page: Found response key');
          } else if (result.containsKey('svg_content')) {
            _svgContent = result['svg_content'];
            print('📱 Chart Image Page: Found svg_content key');
          } else if (result.containsKey('chart_image')) {
            _svgContent = result['chart_image'];
            print('📱 Chart Image Page: Found chart_image key');
          } else {
            // If no specific key found, convert the whole result to string
            _svgContent = result.toString();
            print('📱 Chart Image Page: No specific key found, using toString()');
          }

          print('📱 Chart Image Page: Extracted content type: ${_svgContent.runtimeType}');
          print('📱 Chart Image Page: Extracted content length: ${_svgContent?.toString().length ?? 0}');
          print('📱 Chart Image Page: Extracted content preview: ${_svgContent?.toString().substring(0, _svgContent.toString().length > 200 ? 200 : _svgContent.toString().length)}...');
        } else {
          print('📱 Chart Image Page: Unknown result type, converting to string');
          _svgContent = result.toString();
        }

        print('📱 Chart Image Page: Final SVG content set: ${_svgContent != null ? "Yes (${_svgContent!.length} chars)" : "No"}');
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      print('📱 Chart Image Page: ❌ Error loading chart: $e');
      print('📱 Chart Image Page: Stack trace: $stackTrace');
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'कुन्डली चार्ट',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 12),
                _buildChartSelector(),
                const SizedBox(height: 12),
                if (_isLoading) LoadingWidget(
                  message: 'चार्ट इमेज तयार गर्दै...',
                  featureName: 'चार्ट इमेज',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchChartImage,
                  featureName: 'चार्ट इमेज',
                ),
                if (_svgContent != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16), // Reduced padding
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16), // Slightly smaller radius
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.auto_graph,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'चार्ट छान्नुहोस्',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 16),

          // Horizontal scrollable chart selector with scroll indicator
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.swipe,
                    color: Color(0xFF4CAF50),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'चार्ट छान्न बायाँ-दायाँ स्वाइप गर्नुहोस्',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF2E7D32),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                height: 80,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  itemCount: _charts.length + 1, // +1 for scroll indicator
                  itemBuilder: (context, index) {
                    // Show scroll indicator at the end
                    if (index == _charts.length) {
                      return Container(
                        width: 60,
                        margin: const EdgeInsets.only(right: 12),
                        decoration: BoxDecoration(
                          color: const Color(0xFF4CAF50).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: const Color(0xFF4CAF50).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.more_horiz,
                              color: Color(0xFF4CAF50),
                              size: 24,
                            ),
                            SizedBox(height: 4),
                            Text(
                              'अझै',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFF4CAF50),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    final chart = _charts[index];
                    final isSelected = _selectedChart == chart['code'];

                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedChart = chart['code']!;
                        });
                        _fetchChartImage();
                      },
                      child: Container(
                        width: 140,
                        margin: const EdgeInsets.only(right: 12),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: isSelected
                              ? const LinearGradient(
                                  colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                              : null,
                          color: isSelected ? null : const Color(0xFF4CAF50).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: const Color(0xFF4CAF50),
                            width: 2,
                          ),
                          boxShadow: isSelected ? [
                            BoxShadow(
                              color: const Color(0xFF4CAF50).withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ] : null,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              chart['name']!,
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : const Color(0xFF2E7D32),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              chart['description']!,
                              style: TextStyle(
                                fontSize: 10,
                                color: isSelected ? Colors.white70 : const Color(0xFF388E3C),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    final selectedChart = _charts.firstWhere((c) => c['code'] == _selectedChart);

    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          const SizedBox(height: 20),
          Text(
            '${selectedChart['name']} छवि लोड हुँदै...',
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchChartImage,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    final selectedChart = _charts.firstWhere((c) => c['code'] == _selectedChart);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compact header with chart info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              selectedChart['name']!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          if (_chartImageResult != null)
            _buildChartImageContent(),
        ],
      ),
    );
  }

  Widget _buildChartImageContent() {
    if (_svgContent != null && _svgContent!.isNotEmpty) {
      try {
        // Debug: Show what we received
        print('📱 Chart Image Content Length: ${_svgContent!.length}');
        print('📱 Chart Image Content Start: ${_svgContent!.substring(0, _svgContent!.length > 100 ? 100 : _svgContent!.length)}...');

        // According to API docs, the response is SVG content as string
        // Check if it contains SVG tags
        if (_svgContent!.contains('<svg') && _svgContent!.contains('</svg>')) {
          print('📱 Chart Image: Detected SVG content, rendering with flutter_svg');
          return _buildSvgChart(_svgContent!);
        } else {
          print('📱 Chart Image: No SVG tags found in content');
          return _buildErrorContent('SVG फर्म्याट समस्या');
        }
      } catch (e) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              const Text(
                'चार्ट लोड गर्न सकिएन',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red),
              ),
              const SizedBox(height: 8),
              Text(
                'त्रुटि: ${ErrorHandlerService.getErrorMessage(e)}',
                style: const TextStyle(fontSize: 14, color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'प्राप्त डेटा: ${_svgContent!.length > 50 ? _svgContent!.substring(0, 50) + "..." : _svgContent!}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: const Column(
          children: [
            Icon(Icons.warning, color: Colors.orange, size: 48),
            SizedBox(height: 16),
            Text(
              'चार्ट डेटा उपलब्ध छैन',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.orange),
            ),
            SizedBox(height: 8),
            Text(
              'कृपया पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 14, color: Colors.orange),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildImageChart(Uint8List bytes) {
    return GestureDetector(
      onTap: () => _showFullScreenImage(bytes),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.memory(
            bytes,
            fit: BoxFit.fitWidth,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                padding: const EdgeInsets.all(20),
                child: const Column(
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 48),
                    SizedBox(height: 8),
                    Text(
                      'चार्ट लोड गर्न सकिएन',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _showFullScreenImage(Uint8List imageBytes) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: const Text(
              'कुण्डली चार्ट - पूर्ण स्क्रिन',
              style: TextStyle(color: Colors.white),
            ),
            actions: [
              IconButton(
                onPressed: () {
                  // Add save functionality if needed
                },
                icon: const Icon(Icons.download),
                tooltip: 'डाउनलोड गर्नुहोस्',
              ),
            ],
          ),
          body: Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.memory(
                imageBytes,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSvgChart(String svgContent) {
    return GestureDetector(
      onTap: () => _showFullScreenSvg(svgContent),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: SvgPicture.string(
          svgContent,
          fit: BoxFit.fitWidth, // Full width, maintain aspect ratio
          allowDrawingOutsideViewBox: true,
        ),
      ),
    );
  }

  void _showFullScreenSvg(String svgContent) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: const Text(
              'कुण्डली चार्ट - पूर्ण स्क्रिन',
              style: TextStyle(color: Colors.white),
            ),
            actions: [
              IconButton(
                onPressed: () {
                  // Add save functionality if needed
                },
                icon: const Icon(Icons.download),
                tooltip: 'डाउनलोड गर्नुहोस्',
              ),
            ],
          ),
          body: Center(
            child: InteractiveViewer(
              panEnabled: true,
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 0.5,
              maxScale: 4.0,
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.all(16),
                child: SvgPicture.string(
                  svgContent,
                  fit: BoxFit.scaleDown, // Better scaling
                  allowDrawingOutsideViewBox: true, // Allow full drawing
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorContent(String message) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red),
          ),
          const SizedBox(height: 8),
          Text(
            'प्राप्त डेटा: ${_svgContent!.length > 50 ? _svgContent!.substring(0, 50) + "..." : _svgContent!}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
