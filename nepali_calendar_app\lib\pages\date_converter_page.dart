import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nepali_utils/nepali_utils.dart';

class DateConverterPage extends StatefulWidget {
  const DateConverterPage({Key? key}) : super(key: key);

  @override
  State<DateConverterPage> createState() => _DateConverterPageState();
}

class _DateConverterPageState extends State<DateConverterPage> with TickerProviderStateMixin {
  late TabController _tabController;
  
  // BS to AD Controllers
  final TextEditingController _bsYearController = TextEditingController();
  final TextEditingController _bsMonthController = TextEditingController();
  final TextEditingController _bsDayController = TextEditingController();
  
  // AD to BS Controllers
  final TextEditingController _adYearController = TextEditingController();
  final TextEditingController _adMonthController = TextEditingController();
  final TextEditingController _adDayController = TextEditingController();
  
  // Results
  String _bsToAdResult = '';
  String _adToBsResult = '';
  bool _bsToAdError = false;
  bool _adToBsError = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Set current date as default
    final now = DateTime.now();
    final nepaliDate = now.toNepaliDateTime();

    _adYearController.text = now.year.toString();
    _adMonthController.text = now.month.toString();
    _adDayController.text = now.day.toString();

    _bsYearController.text = nepaliDate.year.toString();
    _bsMonthController.text = nepaliDate.month.toString();
    _bsDayController.text = nepaliDate.day.toString();

    // Convert current dates
    _convertBsToAd();
    _convertAdToBs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _bsYearController.dispose();
    _bsMonthController.dispose();
    _bsDayController.dispose();
    _adYearController.dispose();
    _adMonthController.dispose();
    _adDayController.dispose();
    super.dispose();
  }

  void _convertBsToAd() {
    try {
      final year = int.parse(_bsYearController.text);
      final month = int.parse(_bsMonthController.text);
      final day = int.parse(_bsDayController.text);

      if (year < 2000 || year > 2100) {
        throw Exception('वर्ष २००० देखि २१०० सम्म मात्र समर्थित छ');
      }
      if (month < 1 || month > 12) {
        throw Exception('महिना १ देखि १२ सम्म हुनुपर्छ');
      }
      if (day < 1 || day > 32) {
        throw Exception('दिन १ देखि ३२ सम्म हुनुपर्छ');
      }

      final nepaliDate = NepaliDateTime(year, month, day);
      final adDate = nepaliDate.toDateTime();

      setState(() {
        _bsToAdResult = '${adDate.year}/${adDate.month.toString().padLeft(2, '0')}/${adDate.day.toString().padLeft(2, '0')}';
        _bsToAdError = false;
      });
    } catch (e) {
      setState(() {
        _bsToAdResult = 'त्रुटि: ${e.toString().replaceAll('Exception: ', '')}';
        _bsToAdError = true;
      });
    }
  }

  void _convertAdToBs() {
    try {
      final year = int.parse(_adYearController.text);
      final month = int.parse(_adMonthController.text);
      final day = int.parse(_adDayController.text);

      if (year < 1943 || year > 2043) {
        throw Exception('वर्ष १९४३ देखि २०४३ सम्म मात्र समर्थित छ');
      }
      if (month < 1 || month > 12) {
        throw Exception('महिना १ देखि १२ सम्म हुनुपर्छ');
      }
      if (day < 1 || day > 31) {
        throw Exception('दिन १ देखि ३१ सम्म हुनुपर्छ');
      }

      final adDate = DateTime(year, month, day);
      final nepaliDate = adDate.toNepaliDateTime();

      setState(() {
        _adToBsResult = '${nepaliDate.year}/${nepaliDate.month.toString().padLeft(2, '0')}/${nepaliDate.day.toString().padLeft(2, '0')}';
        _adToBsError = false;
      });
    } catch (e) {
      setState(() {
        _adToBsResult = 'त्रुटि: ${e.toString().replaceAll('Exception: ', '')}';
        _adToBsError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'मिति रूपान्तरण',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: const [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.arrow_forward, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'BS → AD',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.arrow_back, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'AD → BS',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildBsToAdConverter(),
            _buildAdToBsConverter(),
          ],
        ),
      ),
    );
  }

  Widget _buildBsToAdConverter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          // Input Section - Bigger
          _buildProConverterCard(
            title: 'बिक्रम संवत् (BS)',
            icon: Icons.calendar_today,
            color: const Color(0xFF2196F3),
            yearController: _bsYearController,
            monthController: _bsMonthController,
            dayController: _bsDayController,
            onConvert: _convertBsToAd,
          ),

          const SizedBox(height: 16),

          // Arrow Indicator - Bigger
          _buildProArrowIndicator(const Color(0xFF2196F3)),

          const SizedBox(height: 16),

          // Result Section - Bigger
          _buildProResultCard(
            title: 'अंग्रेजी मिति (AD)',
            result: _bsToAdResult,
            isError: _bsToAdError,
            color: const Color(0xFF4CAF50),
            icon: Icons.today,
          ),
        ],
      ),
    );
  }

  Widget _buildAdToBsConverter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          // Input Section - Bigger
          _buildProConverterCard(
            title: 'अंग्रेजी मिति (AD)',
            icon: Icons.today,
            color: const Color(0xFF4CAF50),
            yearController: _adYearController,
            monthController: _adMonthController,
            dayController: _adDayController,
            onConvert: _convertAdToBs,
          ),

          const SizedBox(height: 16),

          // Arrow Indicator - Bigger
          _buildProArrowIndicator(const Color(0xFF4CAF50)),

          const SizedBox(height: 16),

          // Result Section - Bigger
          _buildProResultCard(
            title: 'बिक्रम संवत् (BS)',
            result: _adToBsResult,
            isError: _adToBsError,
            color: const Color(0xFF2196F3),
            icon: Icons.calendar_today,
          ),
        ],
      ),
    );
  }

  Widget _buildConverterCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required TextEditingController yearController,
    required TextEditingController monthController,
    required TextEditingController dayController,
    required VoidCallback onConvert,
    required String yearHint,
    required String monthHint,
    required String dayHint,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            color.withOpacity(0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Enhanced Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  color.withOpacity(0.1),
                  color.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 15,
                          color: color.withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Input Section
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: color.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildEnhancedInputField(
                          controller: yearController,
                          hint: yearHint,
                          label: 'वर्ष',
                          color: color,
                          onChanged: (_) => onConvert(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildEnhancedInputField(
                          controller: monthController,
                          hint: monthHint,
                          label: 'महिना',
                          color: color,
                          onChanged: (_) => onConvert(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildEnhancedInputField(
                          controller: dayController,
                          hint: dayHint,
                          label: 'दिन',
                          color: color,
                          onChanged: (_) => onConvert(),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Input Guide
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: color.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.keyboard,
                        color: color,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'मिति टाइप गर्नुहोस् • स्वचालित रूपान्तरण हुनेछ',
                          style: TextStyle(
                            fontSize: 13,
                            color: color.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProConverterCard({
    required String title,
    required IconData icon,
    required Color color,
    required TextEditingController yearController,
    required TextEditingController monthController,
    required TextEditingController dayController,
    required VoidCallback onConvert,
  }) {
    return Container(
      height: 160,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.2), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.12),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Bigger header
          Container(
            height: 55,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              color: color.withOpacity(0.08),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: color, size: 22),
                ),
                const SizedBox(width: 14),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),

          // Bigger input section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(18),
              child: Row(
                children: [
                  Expanded(
                    child: _buildProInputField(
                      controller: yearController,
                      label: 'वर्ष',
                      color: color,
                      onChanged: (_) => onConvert(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildProInputField(
                      controller: monthController,
                      label: 'महिना',
                      color: color,
                      onChanged: (_) => onConvert(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildProInputField(
                      controller: dayController,
                      label: 'दिन',
                      color: color,
                      onChanged: (_) => onConvert(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactConverterCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required TextEditingController yearController,
    required TextEditingController monthController,
    required TextEditingController dayController,
    required VoidCallback onConvert,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, color.withOpacity(0.02)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Compact Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.15),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: color.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Compact Input Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildCompactInputField(
                      controller: yearController,
                      label: 'वर्ष',
                      color: color,
                      onChanged: (_) => onConvert(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildCompactInputField(
                      controller: monthController,
                      label: 'महिना',
                      color: color,
                      onChanged: (_) => onConvert(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildCompactInputField(
                      controller: dayController,
                      label: 'दिन',
                      color: color,
                      onChanged: (_) => onConvert(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String hint,
    required Color color,
    required Function(String) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: TextField(
        controller: controller,
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: color,
        ),
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: TextStyle(
            fontSize: 12,
            color: color.withOpacity(0.5),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        ),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildEnhancedInputField({
    required TextEditingController controller,
    required String hint,
    required String label,
    required Color color,
    required Function(String) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            decoration: InputDecoration(
              hintText: hint.split(' ')[1].replaceAll('(', '').replaceAll(')', ''),
              hintStyle: TextStyle(
                fontSize: 14,
                color: color.withOpacity(0.4),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(vertical: 8),
            ),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildProInputField({
    required TextEditingController controller,
    required String label,
    required Color color,
    required Function(String) onChanged,
  }) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2), width: 1.5),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: color.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          Expanded(
            child: TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProArrowIndicator(Color color) {
    return Container(
      height: 45,
      width: 120,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(22),
        border: Border.all(color: color.withOpacity(0.25), width: 1.5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.swap_vert, color: color, size: 22),
          const SizedBox(width: 8),
          Text(
            'रूपान्तरण',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProResultCard({
    required String title,
    required String result,
    required bool isError,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isError ? Colors.red.withOpacity(0.2) : color.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isError ? Colors.red.withOpacity(0.12) : color.withOpacity(0.12),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Bigger header
          Container(
            height: 55,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              color: isError ? Colors.red.withOpacity(0.08) : color.withOpacity(0.08),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: isError ? Colors.red.withOpacity(0.15) : color.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    isError ? Icons.error_outline : icon,
                    color: isError ? Colors.red : color,
                    size: 22,
                  ),
                ),
                const SizedBox(width: 14),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isError ? Colors.red : color,
                  ),
                ),
              ],
            ),
          ),

          // Bigger result display
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(18),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: double.infinity,
                    height: 65,
                    decoration: BoxDecoration(
                      color: isError ? Colors.red.withOpacity(0.05) : color.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isError ? Colors.red.withOpacity(0.2) : color.withOpacity(0.2),
                        width: 1.5,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        result.isEmpty ? 'परिणाम यहाँ देखाइनेछ' : result,
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: isError ? Colors.red : (result.isEmpty ? Colors.grey : color),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  if (!isError && result.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    _buildProCopyButton(result, color),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProCopyButton(String result, Color color) {
    return SizedBox(
      width: double.infinity,
      height: 42,
      child: ElevatedButton.icon(
        onPressed: () => _copyToClipboard(result, color),
        icon: const Icon(Icons.copy, size: 18),
        label: const Text(
          'कपी गर्नुहोस्',
          style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
      ),
    );
  }

  Widget _buildCompactInputField({
    required TextEditingController controller,
    required String label,
    required Color color,
    required Function(String) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              isDense: true,
            ),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildCompactArrowIndicator(Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.swap_vert, color: color, size: 20),
          const SizedBox(width: 6),
          Text(
            'रूपान्तरण',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArrowIndicator(Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.15),
            color.withOpacity(0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(50),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.swap_vert,
            color: color,
            size: 28,
          ),
          const SizedBox(height: 4),
          Text(
            'रूपान्तरण',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultCard({
    required String title,
    required String result,
    required bool isError,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            isError ? Colors.red.withOpacity(0.02) : color.withOpacity(0.02),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: isError ? Colors.red.withOpacity(0.3) : color.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: isError ? Colors.red.withOpacity(0.15) : color.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  isError ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
                  isError ? Colors.red.withOpacity(0.05) : color.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: isError ? Colors.red.withOpacity(0.2) : color.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Icon(
                    isError ? Icons.error_outline : icon,
                    color: isError ? Colors.red : color,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isError ? Colors.red : color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isError ? 'त्रुटि भएको छ' : 'रूपान्तरित मिति',
                        style: TextStyle(
                          fontSize: 14,
                          color: isError ? Colors.red.withOpacity(0.7) : color.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Result Display Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        isError ? Colors.red.withOpacity(0.05) : color.withOpacity(0.05),
                        isError ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: isError ? Colors.red.withOpacity(0.3) : color.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        isError ? Icons.warning_amber : Icons.check_circle_outline,
                        color: isError ? Colors.red : color,
                        size: 40,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        result.isEmpty ? 'परिणाम यहाँ देखाइनेछ' : result,
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: isError ? Colors.red : (result.isEmpty ? Colors.grey : color),
                          letterSpacing: 1.2,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // Copy Button Section
                if (!isError && result.isNotEmpty) ...[
                  const SizedBox(height: 20),
                  _buildCopyButton(result, color),
                ],

                // Additional Info Section
                const SizedBox(height: 16),
                _buildAdditionalInfo(result, isError, color),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactResultCard({
    required String title,
    required String result,
    required bool isError,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, isError ? Colors.red.withOpacity(0.02) : color.withOpacity(0.02)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isError ? Colors.red.withOpacity(0.3) : color.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isError ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Compact Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  isError ? Colors.red.withOpacity(0.1) : color.withOpacity(0.1),
                  isError ? Colors.red.withOpacity(0.05) : color.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: isError ? Colors.red.withOpacity(0.15) : color.withOpacity(0.15),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    isError ? Icons.error_outline : icon,
                    color: isError ? Colors.red : color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isError ? Colors.red : color,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Compact Result Display
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isError ? Colors.red.withOpacity(0.05) : color.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: isError ? Colors.red.withOpacity(0.2) : color.withOpacity(0.2),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          result.isEmpty ? 'परिणाम यहाँ देखाइनेछ' : result,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isError ? Colors.red : (result.isEmpty ? Colors.grey : color),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (!isError && result.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          _buildCompactCopyButton(result, color),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactCopyButton(String result, Color color) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _copyToClipboard(result, color),
        icon: const Icon(Icons.copy, size: 16),
        label: const Text(
          'कपी गर्नुहोस्',
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 2,
        ),
      ),
    );
  }

  Widget _buildCopyButton(String result, Color color) {
    return Container(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _copyToClipboard(result, color),
        icon: const Icon(Icons.copy, size: 20),
        label: const Text(
          'कपी गर्नुहोस्',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 5,
          shadowColor: color.withOpacity(0.3),
        ),
      ),
    );
  }

  Widget _buildAdditionalInfo(String result, bool isError, Color color) {
    if (isError || result.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Colors.grey.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey,
              size: 18,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isError ? 'कृपया सही मिति प्रविष्ट गर्नुहोस्' : 'मिति प्रविष्ट गर्नुहोस्',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: color,
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'रूपान्तरण सफल भयो • कपी गर्न बटन थिच्नुहोस्',
              style: TextStyle(
                fontSize: 14,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard(String text, Color color) {
    Clipboard.setData(ClipboardData(text: text));

    // Show success snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Text(
              'मिति कपी भयो: $text',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 2),
      ),
    );

    // Add haptic feedback
    HapticFeedback.lightImpact();
  }
}
