import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dictionary_model.dart';
import '../services/dictionary_service.dart';

class DictionaryPage extends StatefulWidget {
  const DictionaryPage({super.key});

  @override
  State<DictionaryPage> createState() => _DictionaryPageState();
}

class _DictionaryPageState extends State<DictionaryPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  List<DictionaryEntry> _searchResults = [];
  List<DictionarySearchHistory> _searchHistory = [];
  List<String> _suggestions = [];
  
  bool _isLoading = false;
  String? _error;
  bool _showHistory = true;

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      setState(() {
        _suggestions = DictionaryService.getWordSuggestions(query);
        _showHistory = false;
      });
    } else {
      setState(() {
        _suggestions = [];
        _showHistory = true;
        _searchResults = [];
        _error = null;
      });
    }
  }

  Future<void> _loadSearchHistory() async {
    try {
      final history = await DictionaryService.getSearchHistory();
      setState(() {
        _searchHistory = history;
      });
    } catch (e) {
      print('Failed to load search history: $e');
    }
  }

  Future<void> _searchWord(String word) async {
    if (word.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _searchResults = [];
      _showHistory = false;
      _suggestions = [];
    });

    _searchController.text = word;
    _searchFocusNode.unfocus();

    try {
      final results = await DictionaryService.searchWord(word);
      setState(() {
        _searchResults = results;
        _isLoading = false;
      });
      
      // Reload search history
      await _loadSearchHistory();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _clearHistory() async {
    await DictionaryService.clearSearchHistory();
    await _loadSearchHistory();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          '🔍 शब्दकोश',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              setState(() {
                _showHistory = !_showHistory;
                if (_showHistory) {
                  _searchResults = [];
                  _error = null;
                  _suggestions = [];
                }
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'clear_history') {
                _showClearHistoryDialog();
              } else if (value == 'clear_cache') {
                _clearCache();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_history',
                child: Row(
                  children: [
                    Icon(Icons.delete_outline),
                    SizedBox(width: 8),
                    Text('इतिहास मेटाउनुहोस्'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_cache',
                child: Row(
                  children: [
                    Icon(Icons.cached),
                    SizedBox(width: 8),
                    Text('क्यास मेटाउनुहोस्'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFF2196F3),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
            ),
            child: Column(
              children: [
                // Search Bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    focusNode: _searchFocusNode,
                    decoration: InputDecoration(
                      hintText: 'अंग्रेजी शब्द खोज्नुहोस्...',
                      hintStyle: const TextStyle(
                        color: Colors.grey,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                      prefixIcon: const Icon(Icons.search, color: Color(0xFF2196F3)),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear, color: Colors.grey),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchResults = [];
                                  _error = null;
                                  _showHistory = true;
                                  _suggestions = [];
                                });
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                    ),
                    onSubmitted: _searchWord,
                    textInputAction: TextInputAction.search,
                  ),
                ),
                const SizedBox(height: 12),
                // Search Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _searchWord(_searchController.text),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF2196F3),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text(
                            'खोज्नुहोस्',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
          
          // Content Area
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'शब्द खोजिदै...',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_searchResults.isNotEmpty) {
      return _buildSearchResults();
    }

    if (_suggestions.isNotEmpty) {
      return _buildSuggestions();
    }

    if (_showHistory) {
      return _buildSearchHistory();
    }

    return _buildWelcomeScreen();
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                fontFamily: 'NotoSansDevanagari',
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _searchWord(_searchController.text),
              child: const Text(
                'फेरि प्रयास गर्नुहोस्',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeScreen() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 80,
              color: Colors.blue[300],
            ),
            const SizedBox(height: 24),
            const Text(
              'अंग्रेजी शब्दकोशमा स्वागत छ!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'कुनै पनि अंग्रेजी शब्दको अर्थ, उच्चारण र उदाहरण खोज्नुहोस्',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'इतिहास मेटाउनुहोस्',
          style: TextStyle(fontFamily: 'NotoSansDevanagari'),
        ),
        content: const Text(
          'के तपाईं सबै खोज इतिहास मेटाउन चाहनुहुन्छ?',
          style: TextStyle(fontFamily: 'NotoSansDevanagari'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'रद्द गर्नुहोस्',
              style: TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearHistory();
            },
            child: const Text(
              'मेटाउनुहोस्',
              style: TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _clearCache() async {
    await DictionaryService.clearCache();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'क्यास मेटाइयो',
          style: TextStyle(fontFamily: 'NotoSansDevanagari'),
        ),
      ),
    );
  }

  Widget _buildSuggestions() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = _suggestions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const Icon(Icons.lightbulb_outline, color: Colors.orange),
            title: Text(
              suggestion,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _searchWord(suggestion),
          ),
        );
      },
    );
  }

  Widget _buildSearchHistory() {
    if (_searchHistory.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.history,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              const Text(
                'कुनै खोज इतिहास छैन',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'हालैका खोजहरू',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
              TextButton(
                onPressed: _showClearHistoryDialog,
                child: const Text(
                  'सबै मेटाउनुहोस्',
                  style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _searchHistory.length,
            itemBuilder: (context, index) {
              final history = _searchHistory[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: const Icon(Icons.history, color: Colors.blue),
                  title: Text(
                    history.word,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    _formatDateTime(history.searchTime),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _searchWord(history.word),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final entry = _searchResults[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Word Header
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        entry.word.toUpperCase(),
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2196F3),
                        ),
                      ),
                    ),
                    if (entry.phonetic.isNotEmpty)
                      IconButton(
                        icon: const Icon(Icons.volume_up),
                        onPressed: () {
                          // Could implement text-to-speech here
                          HapticFeedback.lightImpact();
                        },
                      ),
                  ],
                ),

                // Phonetic
                if (entry.phonetic.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text(
                      entry.phonetic,
                      style: const TextStyle(
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),

                // Meanings
                ...entry.meanings.map((meaning) => _buildMeaning(meaning)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMeaning(DictionaryMeaning meaning) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Part of Speech
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: const Color(0xFF2196F3).withOpacity(0.3),
              ),
            ),
            child: Text(
              _translatePartOfSpeech(meaning.partOfSpeech),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2196F3),
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ),
          const SizedBox(height: 12),

          // Definitions
          ...meaning.definitions.asMap().entries.map((entry) {
            final index = entry.key;
            final definition = entry.value;
            return _buildDefinition(index + 1, definition);
          }),

          // Synonyms
          if (meaning.synonyms.isNotEmpty)
            _buildWordList('समानार्थी शब्दहरू:', meaning.synonyms, Colors.green),

          // Antonyms
          if (meaning.antonyms.isNotEmpty)
            _buildWordList('विपरीत शब्दहरू:', meaning.antonyms, Colors.red),
        ],
      ),
    );
  }

  Widget _buildDefinition(int number, DictionaryDefinition definition) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Color(0xFF2196F3),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '$number',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  definition.definition,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ),

          // Example
          if (definition.example != null && definition.example!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(left: 36, top: 8),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.format_quote,
                      size: 16,
                      color: Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        definition.example!,
                        style: const TextStyle(
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildWordList(String title, List<String> words, Color color) {
    if (words.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: words.map((word) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: color.withOpacity(0.3)),
              ),
              child: Text(
                word,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  String _translatePartOfSpeech(String partOfSpeech) {
    switch (partOfSpeech.toLowerCase()) {
      case 'noun': return 'संज्ञा';
      case 'verb': return 'क्रिया';
      case 'adjective': return 'विशेषण';
      case 'adverb': return 'क्रियाविशेषण';
      case 'pronoun': return 'सर्वनाम';
      case 'preposition': return 'पूर्वसर्ग';
      case 'conjunction': return 'संयोजक';
      case 'interjection': return 'विस्मयादिबोधक';
      case 'exclamation': return 'विस्मयादिबोधक';
      default: return partOfSpeech;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} दिन अगाडि';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} घण्टा अगाडि';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else {
      return 'अहिले';
    }
  }
}
