import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class DivisionalChartsPage extends StatefulWidget {
  final UserData user;

  const DivisionalChartsPage({Key? key, required this.user}) : super(key: key);

  @override
  State<DivisionalChartsPage> createState() => _DivisionalChartsPageState();
}

class _DivisionalChartsPageState extends State<DivisionalChartsPage> {
  bool _isLoading = false;
  dynamic _divisionalChartsResult;
  String? _error;
  String _selectedChart = 'D1';
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _resultKey = GlobalKey();

  final List<Map<String, String>> _charts = [
    {'code': 'D1', 'name': 'लग्न चार्ट', 'description': 'मुख्य जन्म चार्ट'},
    {'code': 'D2', 'name': 'होरा चार्ट', 'description': 'धन र सम्पत्ति'},
    {'code': 'D3', 'name': 'द्रेष्काण चार्ट', 'description': 'भाइबहिनी र साहस'},
    {'code': 'D4', 'name': 'चतुर्थांश चार्ट', 'description': 'भाग्य र सम्पत्ति'},
    {'code': 'D5', 'name': 'पञ्चमांश चार्ट', 'description': 'बुद्धि र शिक्षा'},
    {'code': 'D7', 'name': 'सप्तमांश चार्ट', 'description': 'सन्तान'},
    {'code': 'D8', 'name': 'अष्टमांश चार्ट', 'description': 'आयु र मृत्यु'},
    {'code': 'D9', 'name': 'नवमांश चार्ट', 'description': 'विवाह र धर्म'},
    {'code': 'D10', 'name': 'दशमांश चार्ट', 'description': 'करियर र व्यवसाय'},
    {'code': 'D12', 'name': 'द्वादशमांश चार्ट', 'description': 'माता-पिता'},
    {'code': 'D16', 'name': 'षोडशमांश चार्ट', 'description': 'सवारी साधन'},
    {'code': 'D20', 'name': 'विंशमांश चार्ट', 'description': 'आध्यात्मिकता'},
    {'code': 'D24', 'name': 'चतुर्विंशमांश चार्ट', 'description': 'शिक्षा र ज्ञान'},
    {'code': 'D27', 'name': 'सप्तविंशमांश चार्ट', 'description': 'शक्ति र बल'},
    {'code': 'D30', 'name': 'त्रिंशमांश चार्ट', 'description': 'दुःख र कष्ट'},
    {'code': 'D40', 'name': 'चत्वारिंशमांश चार्ट', 'description': 'मातृ वंश'},
    {'code': 'D45', 'name': 'पञ्चचत्वारिंशमांश चार्ट', 'description': 'पितृ वंश'},
    {'code': 'D60', 'name': 'षष्ठ्यंश चार्ट', 'description': 'पूर्व जन्म'},
  ];

  @override
  void initState() {
    super.initState();
    _fetchDivisionalCharts();
  }

  UserData get user => widget.user;

  Future<void> _fetchDivisionalCharts() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getDivisionalCharts(user, _selectedChart);
      
      setState(() {
        _divisionalChartsResult = result;
        _isLoading = false;
      });

      // Scroll to result after data loads
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_resultKey.currentContext != null) {
          Scrollable.ensureVisible(
            _resultKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'विभागीय चार्टहरू',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                _buildChartSelector(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'विभागीय चार्ट तयार गर्दै...',
                  featureName: 'विभागीय चार्ट',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchDivisionalCharts,
                  featureName: 'विभागीय चार्ट',
                ),
                if (_divisionalChartsResult != null)
                  Container(
                    key: _resultKey,
                    child: _buildResultWidget(),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.grid_view,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'चार्ट छान्नुहोस्',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 16),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _charts.length,
            itemBuilder: (context, index) {
              final chart = _charts[index];
              final isSelected = _selectedChart == chart['code'];
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedChart = chart['code']!;
                  });
                  _fetchDivisionalCharts();
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? const Color(0xFF4CAF50) 
                        : const Color(0xFF4CAF50).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: const Color(0xFF4CAF50),
                      width: 2,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        chart['name']!,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : const Color(0xFF2E7D32),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        chart['code']!,
                        style: TextStyle(
                          fontSize: 12,
                          color: isSelected ? Colors.white70 : const Color(0xFF388E3C),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    final selectedChart = _charts.firstWhere((c) => c['code'] == _selectedChart);

    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          const SizedBox(height: 20),
          Text(
            '${selectedChart['name']} लोड हुँदै...',
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchDivisionalCharts,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    final selectedChart = _charts.firstWhere((c) => c['code'] == _selectedChart);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.grid_view,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      selectedChart['name']!,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    Text(
                      selectedChart['description']!,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          if (_divisionalChartsResult != null)
            _buildDivisionalChartsContent(),
        ],
      ),
    );
  }

  Widget _buildDivisionalChartsContent() {
    if (_divisionalChartsResult is Map<String, dynamic>) {
      final data = _divisionalChartsResult as Map<String, dynamic>;
      final planets = <Map<String, dynamic>>[];

      // Extract planet data (numbered keys 0-9)
      for (int i = 0; i <= 9; i++) {
        if (data[i.toString()] != null) {
          planets.add(data[i.toString()]);
        }
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart Info Section
          if (data['chart'] != null || data['chart_name'] != null)
            _buildChartInfoSection(data),

          const SizedBox(height: 20),

          // Planets Section
          if (planets.isNotEmpty)
            _buildPlanetsSection(planets),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_divisionalChartsResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildChartInfoSection(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFF1976D2).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'चार्ट जानकारी',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1976D2),
            ),
          ),
          const SizedBox(height: 16),

          if (data['chart'] != null)
            _buildDetailRow('चार्ट कोड', data['chart'].toString()),
          if (data['chart_name'] != null)
            _buildDetailRow('चार्ट नाम', data['chart_name'].toString()),
        ],
      ),
    );
  }

  Widget _buildPlanetsSection(List<Map<String, dynamic>> planets) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF1F8E9),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ग्रह स्थितिहरू',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 16),

          // Grid layout for planets
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: planets.length,
            itemBuilder: (context, index) {
              final planet = planets[index];
              return _buildPlanetCard(planet, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetCard(Map<String, dynamic> planet, int index) {
    final planetName = _translatePlanet(planet['full_name']?.toString() ?? '');
    final zodiac = _translateZodiac(planet['zodiac']?.toString() ?? '');
    final house = planet['house']?.toString() ?? '';
    final degree = planet['local_degree']?.toStringAsFixed(2) ?? '';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getPlanetColor(index).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPlanetColor(index).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Planet name with icon
          Row(
            children: [
              Icon(
                _getPlanetIcon(planet['full_name']?.toString() ?? ''),
                color: _getPlanetColor(index),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  planetName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getPlanetColor(index),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Planet details
          Text(
            'राशि: $zodiac',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'भाव: $house',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'डिग्री: $degree°',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),

          // Retrograde indicator
          if (planet['retro'] == true)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'वक्री',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1976D2),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1565C0),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPlanetColor(int index) {
    final colors = [
      const Color(0xFFE53935), // Red
      const Color(0xFFFF9800), // Orange
      const Color(0xFF2196F3), // Blue
      const Color(0xFFE91E63), // Pink
      const Color(0xFF4CAF50), // Green
      const Color(0xFF9C27B0), // Purple
      const Color(0xFF795548), // Brown
      const Color(0xFF607D8B), // Blue Grey
      const Color(0xFF3F51B5), // Indigo
      const Color(0xFF00BCD4), // Cyan
    ];
    return colors[index % colors.length];
  }

  IconData _getPlanetIcon(String planetName) {
    switch (planetName.toLowerCase()) {
      case 'sun':
        return Icons.wb_sunny;
      case 'moon':
        return Icons.nightlight_round;
      case 'mars':
        return Icons.fitness_center;
      case 'mercury':
        return Icons.psychology;
      case 'jupiter':
        return Icons.school;
      case 'venus':
        return Icons.favorite;
      case 'saturn':
        return Icons.schedule;
      case 'rahu':
        return Icons.arrow_upward;
      case 'ketu':
        return Icons.arrow_downward;
      case 'ascendant':
        return Icons.home;
      default:
        return Icons.circle;
    }
  }

  String _translatePlanet(String planet) {
    switch (planet.toLowerCase()) {
      case 'sun':
        return 'सूर्य';
      case 'moon':
        return 'चन्द्र';
      case 'mars':
        return 'मंगल';
      case 'mercury':
        return 'बुध';
      case 'jupiter':
        return 'बृहस्पति';
      case 'venus':
        return 'शुक्र';
      case 'saturn':
        return 'शनि';
      case 'rahu':
        return 'राहु';
      case 'ketu':
        return 'केतु';
      case 'ascendant':
        return 'लग्न';
      default:
        return planet;
    }
  }

  String _translateZodiac(String zodiac) {
    switch (zodiac.toLowerCase()) {
      case 'aries':
        return 'मेष';
      case 'taurus':
        return 'वृषभ';
      case 'gemini':
        return 'मिथुन';
      case 'cancer':
        return 'कर्कट';
      case 'leo':
        return 'सिंह';
      case 'virgo':
        return 'कन्या';
      case 'libra':
        return 'तुला';
      case 'scorpio':
        return 'वृश्चिक';
      case 'sagittarius':
        return 'धनु';
      case 'capricorn':
        return 'मकर';
      case 'aquarius':
        return 'कुम्भ';
      case 'pisces':
        return 'मीन';
      default:
        return zodiac;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
