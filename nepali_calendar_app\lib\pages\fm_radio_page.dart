import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/radio_station.dart';
import '../services/radio_service.dart';

class FMRadioPage extends StatefulWidget {
  const FMRadioPage({super.key});

  @override
  State<FMRadioPage> createState() => _FMRadioPageState();
}

class _FMRadioPageState extends State<FMRadioPage> with TickerProviderStateMixin {
  final AudioPlayer _audioPlayer = AudioPlayer();
  List<RadioStation> _radioStations = [];
  List<RadioStation> _favoriteStations = [];
  List<RadioStation> _filteredStations = [];
  List<String> _favoriteIds = [];
  RadioStation? _currentStation;
  bool _isPlaying = false;
  bool _isLoading = false;
  bool _isInitializing = true;
  bool _hasError = false;
  String _errorMessage = '';
  String _searchQuery = '';
  int _selectedTabIndex = 0;
  late TabController _tabController;
  late AnimationController _rotationController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _initializeRadio();
    _setupAudioPlayer();
  }

  Future<void> _initializeRadio() async {
    setState(() {
      _isInitializing = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // Load stations from GitHub repository like the original
      await RadioService.loadStations();
      await _loadRadioStations();
      setState(() {
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'रेडियो स्टेशनहरू लोड गर्न सकिएन। कृपया इन्टरनेट जडान जाँच गर्नुहोस्।';
      });
      print('Error initializing radio: $e');
    } finally {
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _tabController.dispose();
    _rotationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _setupAudioPlayer() {
    _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      setState(() {
        _isPlaying = state == PlayerState.playing;
        _isLoading = false;
      });

      if (_isPlaying) {
        _rotationController.repeat();
      } else {
        _rotationController.stop();
      }
    });
  }



  Future<void> _loadRadioStations() async {
    setState(() {
      _radioStations = RadioService.getAllRadioStations();
      _filteredStations = _radioStations;
    });
    await _loadFavorites();
  }

  void _filterStations(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredStations = _radioStations;
      } else {
        _filteredStations = _radioStations.where((station) {
          return station.name.toLowerCase().contains(query.toLowerCase()) ||
                 (station.address?.toLowerCase().contains(query.toLowerCase()) ?? false);
        }).toList();
      }
    });
  }

  Future<void> _loadFavorites() async {
    final favoriteIds = await RadioService.getFavoriteStationIds();
    final favoriteStations = await RadioService.getFavoriteStations();
    setState(() {
      _favoriteIds = favoriteIds;
      _favoriteStations = favoriteStations;
    });
  }

  Future<void> _playStation(RadioStation station) async {
    if (_currentStation?.id == station.id && _isPlaying) {
      await _stopRadio();
      return;
    }

    setState(() {
      _isLoading = true;
      _currentStation = station;
    });

    // Show loading message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  '${station.name} जडान गर्दै...',
                  style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
                ),
              ),
            ],
          ),
          backgroundColor: const Color(0xFF7B1FA2),
          duration: const Duration(seconds: 10),
        ),
      );
    }

    try {
      await _audioPlayer.stop();

      // Add timeout for connection
      await _audioPlayer.play(UrlSource(station.streamUrl)).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw Exception('Connection timeout');
        },
      );

      // Hide loading snackbar and show success
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.radio, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${station.name} सफलतापूर्वक चलाइयो',
                    style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFF2E7D32),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _currentStation = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.error_outline, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'रेडियो चलाउन सकिएन',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontFamily: 'NotoSansDevanagari',
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'स्टेशन: ${station.name}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
                const Text(
                  'इन्टरनेट जडान जाँच गर्नुहोस्',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade700,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'फेरि प्रयास',
              textColor: Colors.white,
              onPressed: () => _playStation(station),
            ),
          ),
        );
      }
    }
  }

  Future<void> _stopRadio() async {
    await _audioPlayer.stop();
    setState(() {
      _isPlaying = false;
      _isLoading = false;
    });
    _rotationController.stop();
  }

  Future<void> _toggleFavorite(RadioStation station) async {
    final isFav = _favoriteIds.contains(station.id);
    if (isFav) {
      await RadioService.removeFromFavorites(station.id);
    } else {
      await RadioService.addToFavorites(station.id);
    }
    await _loadFavorites();
  }

  @override
  Widget build(BuildContext context) {
    // Show loading screen during initialization
    if (_isInitializing) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'एफ-एम रेडियो',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
          backgroundColor: const Color(0xFF6A1B9A),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A1B9A)),
                strokeWidth: 3,
              ),
              SizedBox(height: 24),
              Text(
                'रेडियो स्टेशनहरू लोड गर्दै...',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'NotoSansDevanagari',
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'कृपया प्रतीक्षा गर्नुहोस्',
                style: TextStyle(
                  fontSize: 14,
                  fontFamily: 'NotoSansDevanagari',
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Show error screen if loading failed
    if (_hasError) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'एफ-एम रेडियो',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
          backgroundColor: const Color(0xFF6A1B9A),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red.shade400,
                ),
                const SizedBox(height: 24),
                Text(
                  _errorMessage,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    fontFamily: 'NotoSansDevanagari',
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _initializeRadio,
                  icon: const Icon(Icons.refresh),
                  label: const Text(
                    'फेरि प्रयास गर्नुहोस्',
                    style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6A1B9A),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFF6A1B9A),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4A148C),
              Color(0xFF6A1B9A),
              Color(0xFF8E24AA),
              Color(0xFFF3E5F5),
            ],
            stops: [0.0, 0.2, 0.4, 1.0],
          ),
        ),
        child: Column(
          children: [
            // Small top gap
            const SizedBox(height: 20),
            // Search Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: TextField(
                controller: _searchController,
                onChanged: _filterStations,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'रेडियो स्टेशन खोज्नुहोस्...',
                  hintStyle: const TextStyle(
                    color: Colors.white70,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                  prefixIcon: const Icon(Icons.search, color: Colors.white70),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.white70),
                          onPressed: () {
                            _searchController.clear();
                            _filterStations('');
                          },
                        )
                      : null,
                  filled: true,
                  fillColor: Colors.white.withOpacity(0.2),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
              ),
            ),
            // Tab Bar
            TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(
                  icon: Icon(Icons.radio),
                  text: 'सबै रेडियो',
                ),
                Tab(
                  icon: Icon(Icons.favorite),
                  text: 'मनपर्ने',
                ),
              ],
            ),
            // Current Playing Station Card
            if (_currentStation != null) _buildCurrentPlayingCard(),
            
            // Radio Stations List
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildRadioStationsList(_filteredStations),
                  _buildRadioStationsList(_favoriteStations),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildCurrentPlayingCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Color(0xFFF8F5FF),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
        ],
        border: Border.all(
          color: const Color(0xFF8E24AA).withOpacity(0.2),
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          // Rotating Radio Icon
          AnimatedBuilder(
            animation: _rotationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationController.value * 2 * 3.14159,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF8E24AA).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    _isPlaying ? Icons.radio : Icons.radio_button_unchecked,
                    color: const Color(0xFF6A1B9A),
                    size: 32,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 16),
          
          // Station Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentStation!.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF6A1B9A),
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_currentStation!.getFreqString()} • ${_currentStation!.address ?? 'Nepal'}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isPlaying ? 'बजिरहेको छ...' : 'रोकिएको छ',
                  style: TextStyle(
                    fontSize: 12,
                    color: _isPlaying ? const Color(0xFF2E7D32) : const Color(0xFFD32F2F),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          // Play/Stop Button
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF6A1B9A),
              borderRadius: BorderRadius.circular(15),
            ),
            child: IconButton(
              onPressed: _isLoading ? null : () => _playStation(_currentStation!),
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(
                      _isPlaying ? Icons.stop : Icons.play_arrow,
                      color: Colors.white,
                      size: 28,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioStationsList(List<RadioStation> stations) {
    if (stations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isNotEmpty ? Icons.search_off : Icons.radio_button_unchecked,
              size: 64,
              color: Colors.white54,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'खोजिएको रेडियो स्टेशन फेला परेन'
                  : 'कुनै रेडियो स्टेशन फेला परेन',
              style: const TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '"$_searchQuery" को लागि कुनै परिणाम छैन',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: stations.length,
      itemBuilder: (context, index) {
        final station = stations[index];
        final isCurrentStation = _currentStation?.id == station.id;
        final isFavorite = _favoriteIds.contains(station.id);

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: isCurrentStation
                  ? [const Color(0xFFE1BEE7), Colors.white]
                  : [Colors.white, const Color(0xFFF8F5FF)],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isCurrentStation
                  ? const Color(0xFF8E24AA)
                  : const Color(0xFF8E24AA).withOpacity(0.1),
              width: isCurrentStation ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 8,
                offset: const Offset(0, 4),
                spreadRadius: 1,
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            leading: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF8E24AA).withOpacity(0.1),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                Icons.radio,
                color: const Color(0xFF6A1B9A),
                size: 24,
              ),
            ),
            title: Text(
              station.name,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isCurrentStation ? const Color(0xFF6A1B9A) : const Color(0xFF2E2E2E),
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '${station.getFreqString()} • ${station.address ?? 'Nepal'}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                if (station.address != null)
                  Text(
                    station.address!,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.grey,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Favorite Button
                IconButton(
                  onPressed: () => _toggleFavorite(station),
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? Colors.red : Colors.grey,
                    size: 20,
                  ),
                ),
                // Play Button
                Container(
                  decoration: BoxDecoration(
                    color: isCurrentStation && _isPlaying
                        ? const Color(0xFFD32F2F)
                        : const Color(0xFF6A1B9A),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    onPressed: () => _playStation(station),
                    icon: _isLoading && isCurrentStation
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Icon(
                            isCurrentStation && _isPlaying ? Icons.stop : Icons.play_arrow,
                            color: Colors.white,
                            size: 20,
                          ),
                  ),
                ),
              ],
            ),
            onTap: () => _playStation(station),
          ),
        );
      },
    );
  }
}
