import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class GemSuggestionPage extends StatefulWidget {
  final UserData user;

  const GemSuggestionPage({Key? key, required this.user}) : super(key: key);

  @override
  State<GemSuggestionPage> createState() => _GemSuggestionPageState();
}

class _GemSuggestionPageState extends State<GemSuggestionPage> {
  bool _isLoading = false;
  dynamic _gemSuggestionResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchGemSuggestion();
  }

  UserData get user => widget.user;

  Future<void> _fetchGemSuggestion() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getGemSuggestion(user);
      
      setState(() {
        _gemSuggestionResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'रत्न सुझाव',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'रत्न सुझाव विश्लेषण गर्दै...',
                  featureName: 'रत्न सुझाव',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchGemSuggestion,
                  featureName: 'रत्न सुझाव',
                ),
                if (_gemSuggestionResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.diamond,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'रत्न सुझाव लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchGemSuggestion,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.diamond,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'रत्न सुझाव',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_gemSuggestionResult != null)
            _buildGemSuggestionContent(),
        ],
      ),
    );
  }

  Widget _buildGemSuggestionContent() {
    if (_gemSuggestionResult is Map<String, dynamic>) {
      final data = _gemSuggestionResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all key-value pairs from the result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _formatKey(entry.key),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else if (_gemSuggestionResult is List) {
      final gems = _gemSuggestionResult as List;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary header
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'कुल ${gems.length} रत्न सुझावहरू',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Display each gem suggestion
          ...gems.asMap().entries.map((entry) {
            final index = entry.key;
            final gem = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getGemColor(index),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'रत्न ${index + 1}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildValueWidget(gem),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_gemSuggestionResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  Color _getGemColor(int index) {
    final colors = [
      const Color(0xFFE53935), // Red
      const Color(0xFF1976D2), // Blue
      const Color(0xFF388E3C), // Green
      const Color(0xFFFF9800), // Orange
      const Color(0xFF9C27B0), // Purple
      const Color(0xFF795548), // Brown
      const Color(0xFF607D8B), // Blue Grey
      const Color(0xFFE91E63), // Pink
      const Color(0xFF3F51B5), // Indigo
    ];
    return colors[index % colors.length];
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'gem_name':
        return 'रत्न नाम';
      case 'gem_type':
        return 'रत्न प्रकार';
      case 'planet':
        return 'ग्रह';
      case 'benefits':
        return 'फाइदाहरू';
      case 'wearing_day':
        return 'लगाउने दिन';
      case 'wearing_time':
        return 'लगाउने समय';
      case 'metal':
        return 'धातु';
      case 'finger':
        return 'औंला';
      case 'weight':
        return 'तौल';
      case 'color':
        return 'रंग';
      case 'price_range':
        return 'मूल्य दायरा';
      case 'quality':
        return 'गुणस्तर';
      case 'origin':
        return 'उत्पत्ति';
      case 'substitute':
        return 'विकल्प';
      case 'mantra':
        return 'मन्त्र';
      case 'precautions':
        return 'सावधानीहरू';
      case 'effects':
        return 'प्रभावहरू';
      case 'recommendation':
        return 'सिफारिस';
      case 'description':
        return 'विवरण';
      case 'properties':
        return 'गुणहरू';
      case 'healing_properties':
        return 'निको पार्ने गुणहरू';
      case 'astrological_significance':
        return 'ज्योतिषीय महत्व';
      case 'care_instructions':
        return 'हेरचाह निर्देशनहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
