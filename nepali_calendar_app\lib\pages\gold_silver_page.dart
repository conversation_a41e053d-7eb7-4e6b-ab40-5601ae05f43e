import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/gold_silver_model.dart';
import '../services/gold_silver_service.dart';

class GoldSilverPage extends StatefulWidget {
  const GoldSilverPage({super.key});

  @override
  State<GoldSilverPage> createState() => _GoldSilverPageState();
}

class _GoldSilverPageState extends State<GoldSilverPage> {
  GoldSilverData? _goldSilverData;
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Force refresh to get latest data
    _fetchGoldSilverRates(forceRefresh: true);
  }

  Future<void> _fetchGoldSilverRates({bool forceRefresh = false}) async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      if (forceRefresh) _goldSilverData = null;
    });

    try {
      final data = await GoldSilverService.fetchGoldSilverRates(forceRefresh: forceRefresh);
      if (data.goldRates.isEmpty && data.silverRates.isEmpty) {
        setState(() {
          _errorMessage = 'Google Sheets बाट कुनै डाटा फेला परेन। कृपया Google Sheets जाँच गर्नुहोस्।';
          _isLoading = false;
        });
      } else {
        setState(() {
          _goldSilverData = data;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Google Sheets बाट डाटा लोड गर्न सकिएन। Internet जडान जाँच गर्नुहोस्।';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.amber[700],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.monetization_on, color: Colors.white, size: 24),
            const SizedBox(width: 8),
            const Text(
              'सुन-चादी बजारभाउ',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontFamily: 'NotoSansDevanagari',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => _fetchGoldSilverRates(forceRefresh: true),
            tooltip: 'रिफ्रेश गर्नुहोस्',
          ),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.amber[700]!,
                Colors.amber[600]!,
                Colors.orange[600]!,
              ],
            ),
          ),
        ),
      ),
      body: _isLoading
          ? Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.amber[50]!,
                    Colors.orange[50]!,
                  ],
                ),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.amber,
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 24),
                    Text(
                      'सुन-चादीको भाउ लोड गर्दै...',
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: 'NotoSansDevanagari',
                        fontWeight: FontWeight.w600,
                        color: Colors.amber,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'कृपया प्रतीक्षा गर्नुहोस्',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'NotoSansDevanagari',
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            )
          : _errorMessage.isNotEmpty
              ? Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.red[50]!,
                        Colors.orange[50]!,
                      ],
                    ),
                  ),
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.cloud_off,
                            size: 80,
                            color: Colors.red[400],
                          ),
                          const SizedBox(height: 24),
                          Text(
                            _errorMessage,
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'NotoSansDevanagari',
                              fontWeight: FontWeight.w600,
                              color: Colors.red[700],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Google Sheets URL जाँच गर्नुहोस्:\n${GoldSilverService.getGoogleSheetsUrl()}',
                            style: TextStyle(
                              fontSize: 12,
                              fontFamily: 'NotoSansDevanagari',
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton.icon(
                                onPressed: () => _fetchGoldSilverRates(forceRefresh: true),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.amber[700],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                ),
                                icon: const Icon(Icons.refresh),
                                label: const Text(
                                  'फेरि प्रयास',
                                  style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                                ),
                              ),
                              const SizedBox(width: 12),
                              ElevatedButton.icon(
                                onPressed: () async {
                                  final url = GoldSilverService.getGoogleSheetsUrl();
                                  if (await canLaunchUrl(Uri.parse(url))) {
                                    await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                ),
                                icon: const Icon(Icons.open_in_browser),
                                label: const Text(
                                  'Sheets खोल्नुहोस्',
                                  style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              : RefreshIndicator(
                  onRefresh: () => _fetchGoldSilverRates(forceRefresh: true),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Last Updated Info
                        if (_goldSilverData != null)
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.green[50]!,
                                  Colors.blue[50]!,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(color: Colors.green[200]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.green.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 5,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.update, color: Colors.green[600], size: 20),
                                    const SizedBox(width: 8),
                                    Text(
                                      'लाइभ डाटा',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green[700],
                                        fontFamily: 'NotoSansDevanagari',
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'अन्तिम अपडेट: ${GoldSilverService.getTimeAgo(_goldSilverData!.lastUpdated)}',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.green[700],
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                                Text(
                                  GoldSilverService.getFormattedDate(_goldSilverData!.lastUpdated),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.green[600],
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.green[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'Google Sheets बाट',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.green[700],
                                      fontFamily: 'NotoSansDevanagari',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                        const SizedBox(height: 20),

                        // Gold Rates Section
                        if (_goldSilverData != null) ...[
                          _buildSectionHeader('सुनको भाउ', Icons.monetization_on, Colors.amber[700]!),
                          const SizedBox(height: 12),
                          if (_goldSilverData!.goldRates.isNotEmpty)
                            ..._goldSilverData!.goldRates.map((rate) => _buildRateCard(rate, Colors.amber))
                          else
                            ...GoldSilverData.sampleData().goldRates.map((rate) => _buildRateCard(rate, Colors.amber)),
                          const SizedBox(height: 20),
                        ],

                        // Silver Rates Section
                        if (_goldSilverData != null) ...[
                          _buildSectionHeader('चादीको भाउ', Icons.circle, Colors.grey[600]!),
                          const SizedBox(height: 12),
                          if (_goldSilverData!.silverRates.isNotEmpty)
                            ..._goldSilverData!.silverRates.map((rate) => _buildRateCard(rate, Colors.grey))
                          else
                            ...GoldSilverData.sampleData().silverRates.map((rate) => _buildRateCard(rate, Colors.grey)),
                        ],

                        // Show message if no real data
                        if (_goldSilverData != null &&
                            _goldSilverData!.goldRates.isEmpty &&
                            _goldSilverData!.silverRates.isEmpty) ...[
                          const SizedBox(height: 20),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.orange[50],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.orange[200]!),
                            ),
                            child: Column(
                              children: [
                                Icon(Icons.info_outline, color: Colors.orange[600], size: 32),
                                const SizedBox(height: 8),
                                Text(
                                  'Google Sheets बाट डाटा लोड गर्न सकिएन',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange[700],
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'माथिको डाटा नमूना डाटा हो',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.orange[600],
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
      ],
    );
  }

  Widget _buildRateCard(GoldSilverRate rate, MaterialColor color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color[50]!.withOpacity(0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color[200]!.withOpacity(0.3),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withOpacity(0.8),
            spreadRadius: -2,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
        border: Border.all(color: color[200]!, width: 1.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [color[400]!, color[600]!],
                    ),
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: color[300]!.withOpacity(0.5),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    rate.metal == 'सुन' ? Icons.monetization_on : Icons.circle,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${rate.metal} ${rate.purity}',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: color[800],
                          fontFamily: 'NotoSansDevanagari',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: color[100],
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(color: color[300]!),
                        ),
                        child: Text(
                          rate.unit,
                          style: TextStyle(
                            fontSize: 12,
                            color: color[700],
                            fontFamily: 'NotoSansDevanagari',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Prices
            Row(
              children: [
                Expanded(
                  child: _buildPriceBox(
                    'किन्ने भाउ',
                    rate.buyingPrice,
                    Colors.green,
                    Icons.trending_down,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPriceBox(
                    'बेच्ने भाउ',
                    rate.sellingPrice,
                    Colors.red,
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Price difference
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.compare_arrows, color: Colors.orange[700], size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'फरक: रु. ${GoldSilverService.getPriceDifference(rate.buyingPrice, rate.sellingPrice).toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange[700],
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceBox(String label, double price, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'रु. ${price.toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
