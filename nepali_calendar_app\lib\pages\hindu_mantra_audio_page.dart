import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/mantra_model.dart';
import '../services/mantra_service.dart';

class HinduMantraAudioPage extends StatefulWidget {
  const HinduMantraAudioPage({super.key});

  @override
  State<HinduMantraAudioPage> createState() => _HinduMantraAudioPageState();
}

class _HinduMantraAudioPageState extends State<HinduMantraAudioPage> {
  late WebViewController _webViewController;
  bool _isLoading = true;
  Mantra _currentMantra = MantraService.getAllMantras().first;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(_getYouTubeAudioEmbedUrl()));
  }

  String _getYouTubeAudioEmbedUrl() {
    final videoId = MantraService.getVideoIdForMantra(_currentMantra.id);
    // Use audio-focused YouTube embed parameters
    return 'https://www.youtube.com/embed/$videoId?autoplay=1&rel=0&showinfo=0&modestbranding=1&controls=1&fs=0&playsinline=1&iv_load_policy=3';
  }

  void _playMantra(Mantra mantra) {
    setState(() {
      _currentMantra = mantra;
      _isLoading = true;
    });
    _webViewController.loadRequest(Uri.parse(_getYouTubeAudioEmbedUrl()));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A2E),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'हिन्दू मंत्र अडियो',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontFamily: 'NotoSansDevanagari',
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => _webViewController.reload(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Audio Player - Top 25% of screen
          Expanded(
            flex: 2,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  WebViewWidget(controller: _webViewController),
                  if (_isLoading)
                    Container(
                      color: const Color(0xFF1A1A2E),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              color: Colors.orange,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'मन्त्र लोड गर्दै...',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Mantra Details - Bottom 75% of screen
          Expanded(
            flex: 6,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: Column(
                children: [
                  // Current Mantra Info
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Mantra Title
                        Text(
                          _currentMantra.titleNepali,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'NotoSansDevanagari',
                            color: Color(0xFF1A1A2E),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _currentMantra.titleSanskrit,
                          style: TextStyle(
                            fontSize: 18,
                            fontStyle: FontStyle.italic,
                            color: Colors.grey[600],
                            fontFamily: 'NotoSansDevanagari',
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        
                        // Stats Row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildStatChip(
                              Icons.access_time,
                              _currentMantra.duration,
                              Colors.blue,
                            ),
                            _buildStatChip(
                              Icons.music_note,
                              _currentMantra.category,
                              Colors.green,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // All Mantras List
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: MantraService.getAllMantras().length,
                      itemBuilder: (context, index) {
                        final mantra = MantraService.getAllMantras()[index];
                        final isCurrentMantra = mantra.id == _currentMantra.id;
                        
                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          decoration: BoxDecoration(
                            color: isCurrentMantra ? Colors.orange.withOpacity(0.1) : Colors.white,
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: isCurrentMantra ? Colors.orange : Colors.grey.withOpacity(0.3),
                              width: isCurrentMantra ? 2 : 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.all(16),
                            leading: Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: isCurrentMantra 
                                    ? [Colors.orange, Colors.deepOrange]
                                    : [Colors.grey[300]!, Colors.grey[400]!],
                                ),
                                borderRadius: BorderRadius.circular(25),
                              ),
                              child: Center(
                                child: Icon(
                                  isCurrentMantra ? Icons.play_arrow : Icons.music_note,
                                  color: isCurrentMantra ? Colors.white : Colors.black54,
                                  size: 24,
                                ),
                              ),
                            ),
                            title: Text(
                              mantra.titleNepali,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: isCurrentMantra ? FontWeight.bold : FontWeight.w600,
                                fontFamily: 'NotoSansDevanagari',
                                color: isCurrentMantra ? Colors.orange : Colors.black,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 4),
                                Text(
                                  mantra.titleSanskrit,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  mantra.description,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[500],
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                            trailing: Text(
                              mantra.duration,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            onTap: isCurrentMantra ? null : () => _playMantra(mantra),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18, color: color),
          const SizedBox(width: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
        ],
      ),
    );
  }
}
