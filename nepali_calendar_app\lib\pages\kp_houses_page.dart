import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class KPHousesPage extends StatefulWidget {
  final UserData user;

  const KPHousesPage({Key? key, required this.user}) : super(key: key);

  @override
  State<KPHousesPage> createState() => _KPHousesPageState();
}

class _KPHousesPageState extends State<KPHousesPage> {
  bool _isLoading = false;
  dynamic _kpHousesResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchKPHouses();
  }

  UserData get user => widget.user;

  Future<void> _fetchKPHouses() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getKPHouses(user);
      
      setState(() {
        _kpHousesResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'केपी भावहरू',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'KP हाउस विश्लेषण गर्दै...',
                  featureName: 'KP हाउस',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchKPHouses,
                  featureName: 'KP हाउस',
                ),
                if (_kpHousesResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.home,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'केपी भावहरू लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchKPHouses,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.home,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'केपी भाव प्रणाली',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_kpHousesResult != null)
            _buildKPHousesContent(),
        ],
      ),
    );
  }

  Widget _buildKPHousesContent() {
    // Handle different possible response formats
    if (_kpHousesResult is Map<String, dynamic>) {
      final data = _kpHousesResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all key-value pairs from the result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _formatKey(entry.key),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else if (_kpHousesResult is List) {
      final houses = _kpHousesResult as List;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary header
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'कुल ${houses.length} केपी भावहरू',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Display each house
          ...houses.asMap().entries.map((entry) {
            final index = entry.key;
            final house = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7D32),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${index + 1}औं भाव',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildValueWidget(house),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_kpHousesResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  _getIconForKey(entry.key),
                  color: const Color(0xFF2E7D32),
                  size: 24,
                ),
                const SizedBox(width: 12),
                SizedBox(
                  width: 140,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: const Color(0xFF4CAF50).withOpacity(0.2),
                      ),
                    ),
                    child: _buildValueWidget(entry.value),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'house':
        return 'भाव';
      case 'house_number':
        return 'भाव संख्या';
      case 'sign':
        return 'राशि';
      case 'sign_lord':
        return 'राशि स्वामी';
      case 'degree':
        return 'डिग्री';
      case 'planets':
        return 'ग्रहहरू';
      case 'cusp':
        return 'कस्प';
      case 'sub_lord':
        return 'उप स्वामी';
      case 'sub_sub_lord':
        return 'उप-उप स्वामी';
      case 'star_lord':
        return 'नक्षत्र स्वामी';
      case 'nakshatra':
        return 'नक्षत्र';
      case 'pada':
        return 'पाद';
      case 'rashi':
        return 'राशि';
      case 'lord':
        return 'स्वामी';
      case 'significator':
        return 'कारक';
      case 'nature':
        return 'प्रकृति';
      case 'element':
        return 'तत्व';
      case 'quality':
        return 'गुण';
      case 'ruling_planet':
        return 'शासक ग्रह';
      case 'exaltation':
        return 'उच्च';
      case 'debilitation':
        return 'नीच';
      case 'moolatrikona':
        return 'मूलत्रिकोण';
      case 'own_sign':
        return 'स्वराशि';
      case 'friendly_signs':
        return 'मित्र राशि';
      case 'enemy_signs':
        return 'शत्रु राशि';
      case 'neutral_signs':
        return 'तटस्थ राशि';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  IconData _getIconForKey(String key) {
    switch (key.toLowerCase()) {
      case 'house_1':
      case 'house_2':
      case 'house_3':
      case 'house_4':
      case 'house_5':
      case 'house_6':
      case 'house_7':
      case 'house_8':
      case 'house_9':
      case 'house_10':
      case 'house_11':
      case 'house_12':
        return Icons.home;
      case 'lord':
      case 'ruling_planet':
        return Icons.public;
      case 'sign':
      case 'rashi':
        return Icons.star;
      case 'degree':
        return Icons.straighten;
      case 'nakshatra':
        return Icons.brightness_7;
      case 'sub_lord':
        return Icons.subdirectory_arrow_right;
      case 'significator':
        return Icons.psychology;
      case 'cuspal_degree':
        return Icons.timeline;
      default:
        return Icons.info;
    }
  }
}
