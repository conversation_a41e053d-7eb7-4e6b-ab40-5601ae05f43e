import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class KPPlanetsPage extends StatefulWidget {
  final UserData user;

  const KPPlanetsPage({Key? key, required this.user}) : super(key: key);

  @override
  State<KPPlanetsPage> createState() => _KPPlanetsPageState();
}

class _KPPlanetsPageState extends State<KPPlanetsPage> {
  bool _isLoading = false;
  dynamic _kpPlanetsResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchKPPlanets();
  }

  UserData get user => widget.user;

  Future<void> _fetchKPPlanets() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getKPPlanets(user);
      
      setState(() {
        _kpPlanetsResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'केपी ग्रहहरू',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_kpPlanetsResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.public,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'केपी ग्रहहरू लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchKPPlanets,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.public,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'केपी ग्रह स्थिति',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_kpPlanetsResult != null)
            _buildKPPlanetsContent(),
        ],
      ),
    );
  }

  Widget _buildKPPlanetsContent() {
    if (_kpPlanetsResult is Map<String, dynamic>) {
      final data = _kpPlanetsResult as Map<String, dynamic>;

      // Extract special values
      final ascendant = data['ascendant'];
      final midheaven = data['midheaven'];

      // Filter out non-planet entries
      final planets = Map<String, dynamic>.from(data)
        ..remove('ascendant')
        ..remove('midheaven');

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary header
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'कुल ${planets.length} ग्रहहरूको केपी स्थिति',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Special values (Ascendant and Midheaven)
          if (ascendant != null || midheaven != null)
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFE3F2FD),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF1976D2).withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'विशेष बिन्दुहरू',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                  const SizedBox(height: 12),
                  if (ascendant != null)
                    _buildSpecialPoint('लग्न (Ascendant)', ascendant.toString()),
                  if (midheaven != null)
                    _buildSpecialPoint('मध्य आकाश (Midheaven)', midheaven.toString()),
                ],
              ),
            ),

          // Display each planet
          ...planets.entries.map((entry) {
            final planetData = entry.value as Map<String, dynamic>;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Planet header
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _getPlanetColor(planetData['name'] ?? ''),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          _translatePlanet(planetData['full_name'] ?? planetData['name'] ?? ''),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      if (planetData['retro'] == true)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.orange.withOpacity(0.5)),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.refresh, color: Colors.orange, size: 14),
                              SizedBox(width: 4),
                              Text(
                                'वक्री',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Planet details
                  _buildPlanetDetail('राशि', _translateZodiac(planetData['zodiac'] ?? '')),
                  _buildPlanetDetail('भाव', '${planetData['house'] ?? ''}औं भाव'),
                  _buildPlanetDetail('डिग्री', '${planetData['local_degree']?.toStringAsFixed(2) ?? ''}°'),
                  _buildPlanetDetail('नक्षत्र', _translateNakshatra(planetData['pseudo_nakshatra'] ?? '')),
                  _buildPlanetDetail('नक्षत्र पाद', '${planetData['pseudo_nakshatra_pada'] ?? ''}'),
                  _buildPlanetDetail('नक्षत्र स्वामी', _translatePlanet(planetData['pseudo_nakshatra_lord'] ?? '')),
                  _buildPlanetDetail('उप स्वामी', _translatePlanet(planetData['sub_lord'] ?? '')),
                  _buildPlanetDetail('उप-उप स्वामी', _translatePlanet(planetData['sub_sub_lord'] ?? '')),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_kpPlanetsResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildSpecialPoint(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1976D2),
              ),
            ),
          ),
          Expanded(
            child: Text(
              '${double.tryParse(value)?.toStringAsFixed(2) ?? value}°',
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1565C0),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetDetail(String label, String value) {
    if (value.isEmpty || value == 'null' || value == '') return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPlanetColor(String planetName) {
    switch (planetName.toLowerCase()) {
      case 'su':
      case 'sun':
        return const Color(0xFFFF9800); // Orange for Sun
      case 'mo':
      case 'moon':
        return const Color(0xFF2196F3); // Blue for Moon
      case 'ma':
      case 'mars':
        return const Color(0xFFE53935); // Red for Mars
      case 'me':
      case 'mercury':
        return const Color(0xFF4CAF50); // Green for Mercury
      case 'ju':
      case 'jupiter':
        return const Color(0xFFFFEB3B); // Yellow for Jupiter
      case 've':
      case 'venus':
        return const Color(0xFFE91E63); // Pink for Venus
      case 'sa':
      case 'saturn':
        return const Color(0xFF9C27B0); // Purple for Saturn
      case 'ra':
      case 'rahu':
        return const Color(0xFF795548); // Brown for Rahu
      case 'ke':
      case 'ketu':
        return const Color(0xFF607D8B); // Blue Grey for Ketu
      case 'as':
      case 'ascendant':
        return const Color(0xFF3F51B5); // Indigo for Ascendant
      default:
        return const Color(0xFF2E7D32); // Default green
    }
  }

  String _translatePlanet(String planet) {
    switch (planet.toLowerCase()) {
      case 'sun':
        return 'सूर्य';
      case 'moon':
        return 'चन्द्र';
      case 'mars':
        return 'मंगल';
      case 'mercury':
        return 'बुध';
      case 'jupiter':
        return 'बृहस्पति';
      case 'venus':
        return 'शुक्र';
      case 'saturn':
        return 'शनि';
      case 'rahu':
        return 'राहु';
      case 'ketu':
        return 'केतु';
      case 'ascendant':
        return 'लग्न';
      default:
        return planet;
    }
  }

  String _translateZodiac(String zodiac) {
    switch (zodiac.toLowerCase()) {
      case 'aries':
        return 'मेष';
      case 'taurus':
        return 'वृषभ';
      case 'gemini':
        return 'मिथुन';
      case 'cancer':
        return 'कर्कट';
      case 'leo':
        return 'सिंह';
      case 'virgo':
        return 'कन्या';
      case 'libra':
        return 'तुला';
      case 'scorpio':
        return 'वृश्चिक';
      case 'sagittarius':
        return 'धनु';
      case 'capricorn':
        return 'मकर';
      case 'aquarius':
        return 'कुम्भ';
      case 'pisces':
        return 'मीन';
      default:
        return zodiac;
    }
  }

  String _translateNakshatra(String nakshatra) {
    switch (nakshatra.toLowerCase()) {
      case 'ashwini':
        return 'अश्विनी';
      case 'bharani':
        return 'भरणी';
      case 'krittika':
        return 'कृत्तिका';
      case 'rohini':
        return 'रोहिणी';
      case 'mrigashira':
        return 'मृगशिरा';
      case 'ardra':
        return 'आर्द्रा';
      case 'punarvasu':
        return 'पुनर्वसु';
      case 'pushya':
        return 'पुष्य';
      case 'ashlesha':
        return 'आश्लेषा';
      case 'magha':
        return 'मघा';
      case 'purva phalguni':
        return 'पूर्व फाल्गुनी';
      case 'uttara phalguni':
        return 'उत्तर फाल्गुनी';
      case 'hasta':
        return 'हस्त';
      case 'chitra':
        return 'चित्रा';
      case 'svati':
        return 'स्वाति';
      case 'vishakha':
        return 'विशाखा';
      case 'anuradha':
        return 'अनुराधा';
      case 'jyeshtha':
        return 'ज्येष्ठा';
      case 'mula':
        return 'मूल';
      case 'purva ashadha':
        return 'पूर्व आषाढा';
      case 'uttara ashadha':
      case 'uttrashadha':
        return 'उत्तर आषाढा';
      case 'shravana':
        return 'श्रवण';
      case 'dhanishta':
        return 'धनिष्ठा';
      case 'shatabhisha':
        return 'शतभिषा';
      case 'purva bhadrapada':
        return 'पूर्व भाद्रपद';
      case 'uttara bhadrapada':
        return 'उत्तर भाद्रपद';
      case 'revati':
        return 'रेवती';
      default:
        return nakshatra;
    }
  }
}
