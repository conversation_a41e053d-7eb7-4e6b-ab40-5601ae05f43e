import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/user_data_service.dart';
import 'mangal_dosh_page.dart';
import 'manglik_dosh_page.dart';
import 'kaalsarp_dosh_page.dart';
import 'papasamaya_page.dart';
import 'pitra_dosh_page.dart';
import 'mahadasha_page.dart';
import 'mahadasha_predictions_page.dart';
import 'antardasha_page.dart';
import 'char_dasha_current_page.dart';
import 'char_dasha_main_page.dart';
import 'char_dasha_sub_page.dart';
import 'current_mahadasha_page.dart';
import 'current_mahadasha_full_page.dart';
import 'paryantar_dasha_page.dart';
import 'specific_dasha_page.dart';
import 'yogini_dasha_main_page.dart';
import 'yogini_dasha_sub_page.dart';
import 'find_sun_sign_page.dart';
import 'find_moon_sign_page.dart';
import 'find_ascendant_page.dart';
import 'current_sade_sati_page.dart';
import 'extended_kundli_details_page.dart';
import 'shad_bala_page.dart';
import 'sade_sati_table_page.dart';
import 'friendship_table_page.dart';
import 'kp_houses_page.dart';
import 'kp_planets_page.dart';
import 'gem_suggestion_page.dart';
import 'numero_table_page.dart';
import 'rudraksh_suggestion_page.dart';
import 'varshapal_details_page.dart';
import 'varshapal_month_chart_page.dart';
import 'varshapal_year_chart_page.dart';
import 'arutha_padas_page.dart';
import 'yoga_list_page.dart';
import 'planet_details_page.dart';
import 'ascendant_report_page.dart';
import 'planet_report_page.dart';
import 'personal_characteristics_page.dart';
import 'divisional_charts_page.dart';
import 'chart_image_page.dart';
import 'ashtakvarga_chart_image_page.dart';
import 'ashtakvarga_page.dart';
import 'binnashtakvarga_page.dart';
import 'ai_12_month_prediction_page.dart';
import 'planetary_aspects_page.dart';
import 'planets_in_houses_page.dart';
import 'north_match_page.dart';
import 'north_match_astro_details_page.dart';
import 'south_match_page.dart';
import 'south_match_astro_details_page.dart';
import 'aggregate_match_page.dart';
import 'rajju_vedha_match_page.dart';
import 'papasamaya_match_page.dart';
import 'nakshatra_match_page.dart';
import 'western_match_page.dart';
import 'bulk_south_match_page.dart';
import 'festivals_page.dart';
import 'add_user_page.dart';


class KundaliFeaturesPage extends StatelessWidget {
  final UserData user;

  const KundaliFeaturesPage({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          user.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            color: Colors.white,
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editUser(context);
                  break;
                case 'delete':
                  _deleteUser(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('सम्पादन गर्नुहोस्', style: TextStyle(color: Color(0xFF1B5E20))),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('मेटाउनुहोस्', style: TextStyle(color: Color(0xFF1B5E20))),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildUserInfoCard(),
              const SizedBox(height: 20),
              _buildCategorizedFeatures(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 32,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            user.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${user.gender} • ${user.ageInYears} वर्ष',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF388E3C),
            ),
          ),
          const SizedBox(height: 12),
          // Compact single line info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    Text(
                      '${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  user.district,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildCategorizedFeatures(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCategorySection(
          'कुन्डली',
          'Kundali',
          Icons.account_circle,
          Colors.green,
          _getHoroscopeFeatures(),
        ),
        const SizedBox(height: 30),
        _buildCategorySection(
          'दोष विश्लेषण',
          'Dosha Analysis',
          Icons.warning_amber,
          Colors.red,
          _getDoshaFeatures(),
        ),
        const SizedBox(height: 30),
        _buildCategorySection(
          'दशा प्रणाली',
          'Dasha System',
          Icons.timeline,
          Colors.purple,
          _getDashaFeatures(),
        ),
        const SizedBox(height: 30),
        _buildCategorySection(
          'विस्तृत कुन्डली',
          'Extended Horoscope',
          Icons.auto_awesome,
          Colors.blue,
          _getExtendedHoroscopeFeatures(),
        ),
        const SizedBox(height: 30),
        _buildCategorySection(
          'मिलान प्रणाली',
          'Matching System',
          Icons.favorite,
          Colors.pink,
          _getMatchingFeatures(),
        ),
      ],
    );
  }

  Widget _buildCategorySection(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    List<Map<String, dynamic>> features,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: color.withOpacity(0.7),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Features Grid
          Padding(
            padding: const EdgeInsets.all(20),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: features.length,
              itemBuilder: (context, index) {
                final feature = features[index];
                return _buildFeatureCard(
                  context,
                  feature['title'] as String,
                  feature['subtitle'] as String,
                  feature['icon'] as IconData,
                  feature['color'] as Color,
                  feature['page'] as Widget? Function(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getDoshaFeatures() {
    return [
      {
        'title': 'मंगल दोष',
        'subtitle': 'मंगल दोष जाँच',
        'icon': Icons.warning_amber,
        'color': Colors.red,
        'page': () => MangalDoshPage(user: user),
      },
      {
        'title': 'कालसर्प दोष',
        'subtitle': 'कालसर्प दोष जाँच',
        'icon': Icons.dangerous,
        'color': Colors.deepOrange,
        'page': () => KaalsarpDoshPage(user: user),
      },
      {
        'title': 'मांगलिक दोष',
        'subtitle': 'मांगलिक दोष जाँच',
        'icon': Icons.warning,
        'color': Colors.orange,
        'page': () => ManglikDoshPage(user: user),
      },
      {
        'title': 'पितृ दोष',
        'subtitle': 'पितृ दोष जाँच',
        'icon': Icons.family_restroom,
        'color': Colors.brown,
        'page': () => PitraDoshPage(user: user),
      },
      {
        'title': 'पापसमय',
        'subtitle': 'पापसमय जाँच',
        'icon': Icons.schedule,
        'color': Colors.purple,
        'page': () => PapasamayaPage(user: user),
      },
    ];
  }

  List<Map<String, dynamic>> _getDashaFeatures() {
    return [

      {
        'title': 'महादशा',
        'subtitle': 'महादशा विश्लेषण',
        'icon': Icons.timeline,
        'color': Colors.purple,
        'page': () => MahadashaPage(user: user),
      },
      {
        'title': 'महादशा भविष्यवाणी',
        'subtitle': 'दशा भविष्यवाणी',
        'icon': Icons.psychology,
        'color': Colors.indigo,
        'page': () => MahadashaPredictionsPage(user: user),
      },
      {
        'title': 'अन्तर्दशा',
        'subtitle': 'अन्तर्दशा विश्लेषण',
        'icon': Icons.access_time,
        'color': Colors.blue,
        'page': () => AntardashaPage(user: user),
      },
      {
        'title': 'चर दशा वर्तमान',
        'subtitle': 'वर्तमान चर दशा',
        'icon': Icons.schedule_outlined,
        'color': Colors.teal,
        'page': () => CharDashaCurrentPage(user: user),
      },
      {
        'title': 'चर दशा मुख्य',
        'subtitle': 'मुख्य चर दशा',
        'icon': Icons.view_list,
        'color': Colors.cyan,
        'page': () => CharDashaMainPage(user: user),
      },

      {
        'title': 'वर्तमान महादशा पूर्ण',
        'subtitle': 'पूर्ण महादशा विवरण',
        'icon': Icons.timeline_outlined,
        'color': Colors.pink,
        'page': () => CurrentMahadashaFullPage(user: user),
      },
      {
        'title': 'वर्तमान महादशा',
        'subtitle': 'हालको महादशा',
        'icon': Icons.access_time_filled,
        'color': Colors.deepPurple,
        'page': () => CurrentMahadashaPage(user: user),
      },

      {
        'title': 'विशिष्ट दशा',
        'subtitle': 'कस्टम दशा विश्लेषण',
        'icon': Icons.analytics,
        'color': Colors.deepOrange,
        'page': () => SpecificDashaPage(user: user),
      },
      {
        'title': 'योगिनी दशा मुख्य',
        'subtitle': 'मुख्य योगिनी दशा',
        'icon': Icons.self_improvement,
        'color': Colors.purple,
        'page': () => YoginiDashaMainPage(user: user),
      },

    ];
  }

  List<Map<String, dynamic>> _getExtendedHoroscopeFeatures() {
    return [

      {
        'title': 'चन्द्र राशि',
        'subtitle': 'चन्द्र राशि खोज्नुहोस्',
        'icon': Icons.nightlight_round,
        'color': Colors.blue,
        'page': () => FindMoonSignPage(user: user),
      },
      {
        'title': 'सूर्य राशि',
        'subtitle': 'सूर्य राशि खोज्नुहोस्',
        'icon': Icons.wb_sunny,
        'color': Colors.orange,
        'page': () => FindSunSignPage(user: user),
      },
      {
        'title': 'लग्न राशि',
        'subtitle': 'लग्न राशि खोज्नुहोस्',
        'icon': Icons.north_east,
        'color': Colors.purple,
        'page': () => FindAscendantPage(user: user),
      },
      {
        'title': 'वर्तमान साढेसाती',
        'subtitle': 'साढेसाती स्थिति',
        'icon': Icons.schedule,
        'color': Colors.brown,
        'page': () => CurrentSadeSatiPage(user: user),
      },
      {
        'title': 'विस्तृत कुण्डली विवरण',
        'subtitle': 'कुण्डली विस्तृत जानकारी',
        'icon': Icons.description,
        'color': Colors.indigo,
        'page': () => ExtendedKundliDetailsPage(user: user),
      },
      {
        'title': 'षड्बल',
        'subtitle': 'ग्रह बल विश्लेषण',
        'icon': Icons.balance,
        'color': Colors.green,
        'page': () => ShadBalaPage(user: user),
      },
      {
        'title': 'साढेसाती तालिका',
        'subtitle': 'साढेसाती विस्तृत तालिका',
        'icon': Icons.table_chart,
        'color': Colors.red,
        'page': () => SadeSatiTablePage(user: user),
      },
      {
        'title': 'मित्रता तालिका',
        'subtitle': 'ग्रह मित्रता विश्लेषण',
        'icon': Icons.people,
        'color': Colors.lightBlue,
        'page': () => FriendshipTablePage(user: user),
      },
      {
        'title': 'केपी भावहरू',
        'subtitle': 'केपी प्रणाली भाव',
        'icon': Icons.home,
        'color': Colors.teal,
        'page': () => KPHousesPage(user: user),
      },
      {
        'title': 'केपी ग्रहहरू',
        'subtitle': 'केपी प्रणाली ग्रह',
        'icon': Icons.public,
        'color': Colors.cyan,
        'page': () => KPPlanetsPage(user: user),
      },
      {
        'title': 'रत्न सुझाव',
        'subtitle': 'व्यक्तिगत रत्न सिफारिस',
        'icon': Icons.diamond,
        'color': Colors.pink,
        'page': () => GemSuggestionPage(user: user),
      },
      {
        'title': 'नुमेरो तालिका',
        'subtitle': 'अंक ज्योतिष तालिका',
        'icon': Icons.calculate,
        'color': Colors.deepPurple,
        'page': () => NumeroTablePage(user: user),
      },
      {
        'title': 'रुद्राक्ष सुझाव',
        'subtitle': 'रुद्राक्ष सिफारिस',
        'icon': Icons.spa,
        'color': Colors.brown,
        'page': () => RudrakshSuggestionPage(user: user),
      },
      {
        'title': 'वर्षफल विवरण',
        'subtitle': 'वार्षिक ज्योतिष विश्लेषण',
        'icon': Icons.event_note,
        'color': Colors.deepOrange,
        'page': () => VarshapalDetailsPage(user: user),
      },

      {
        'title': 'वर्षफल वार्षिक चार्ट',
        'subtitle': 'वार्षिक राशि चक्र चार्ट',
        'icon': Icons.pie_chart,
        'color': Colors.lightGreen,
        'page': () => VarshapalYearChartPage(user: user),
      },
      {
        'title': 'अरुध पदहरू',
        'subtitle': 'विशेष लग्न गणना',
        'icon': Icons.location_on,
        'color': Colors.red,
        'page': () => AruthaPadasPage(user: user),
      },

      {
        'title': 'योग सूची',
        'subtitle': 'राज योग र अन्य योगहरू',
        'icon': Icons.list,
        'color': Colors.purple,
        'page': () => YogaListPage(user: user),
      },
    ];
  }

  List<Map<String, dynamic>> _getHoroscopeFeatures() {
    return [

      {
        'title': 'कुन्डली चार्ट',
        'subtitle': 'ज्योतिष चार्ट',
        'icon': Icons.image,
        'color': Colors.brown,
        'page': () => ChartImagePage(user: user),
      },
      {
        'title': 'ग्रह विवरण',
        'subtitle': 'विस्तृत ग्रह स्थिति',
        'icon': Icons.public,
        'color': Colors.blue,
        'page': () => PlanetDetailsPage(user: user),
      },
      {
        'title': 'लग्न रिपोर्ट',
        'subtitle': 'लग्न आधारित विश्लेषण',
        'icon': Icons.home,
        'color': Colors.green,
        'page': () => AscendantReportPage(user: user),
      },
      {
        'title': 'ग्रह रिपोर्ट',
        'subtitle': 'व्यक्तिगत ग्रह विश्लेषण',
        'icon': Icons.assessment,
        'color': Colors.orange,
        'page': () => PlanetReportPage(user: user),
      },
      {
        'title': 'व्यक्तिगत विशेषताहरू',
        'subtitle': 'व्यक्तित्व विश्लेषण',
        'icon': Icons.psychology,
        'color': Colors.teal,
        'page': () => PersonalCharacteristicsPage(user: user),
      },
      {
        'title': 'विभागीय चार्टहरू',
        'subtitle': 'विभिन्न ज्योतिष चार्ट',
        'icon': Icons.grid_view,
        'color': Colors.indigo,
        'page': () => DivisionalChartsPage(user: user),
      },
      {
        'title': 'अष्टकवर्ग चार्ट छवि',
        'subtitle': 'अष्टकवर्ग चार्ट',
        'icon': Icons.grid_3x3,
        'color': Colors.deepPurple,
        'page': () => AshtakvargaChartImagePage(user: user),
      },
      {
        'title': 'अष्टकवर्ग',
        'subtitle': 'अष्टकवर्ग विश्लेषण',
        'icon': Icons.calculate,
        'color': Colors.cyan,
        'page': () => AshtakvargaPage(user: user),
      },
      {
        'title': 'बिन्नाष्टकवर्ग',
        'subtitle': 'बिन्नाष्टकवर्ग तालिका',
        'icon': Icons.table_chart,
        'color': Colors.amber,
        'page': () => BinnashtakvargaPage(user: user),
      },
      {
        'title': 'AI १२ महिना भविष्यवाणी',
        'subtitle': 'AI भविष्यवाणी',
        'icon': Icons.auto_awesome,
        'color': Colors.purple,
        'page': () => AI12MonthPredictionPage(user: user),
      },
      {
        'title': 'ग्रह दृष्टि',
        'subtitle': 'ग्रह दृष्टि विश्लेषण',
        'icon': Icons.visibility,
        'color': Colors.blueGrey,
        'page': () => PlanetaryAspectsPage(user: user),
      },
      {
        'title': 'भावमा ग्रहहरू',
        'subtitle': 'ग्रह भाव स्थिति',
        'icon': Icons.home_work,
        'color': Colors.lightBlue,
        'page': () => PlanetsInHousesPage(user: user),
      },

    ];
  }

  List<Map<String, dynamic>> _getMatchingFeatures() {
    return [
      {
        'title': 'उत्तर मिलान',
        'subtitle': 'अष्टकूट मिलान प्रणाली',
        'icon': Icons.north,
        'color': Colors.blue,
        'page': () => const NorthMatchPage(),
      },
      {
        'title': 'उत्तर मिलान विस्तृत',
        'subtitle': 'अष्टकूट + ज्योतिषीय विवरण',
        'icon': Icons.auto_awesome,
        'color': Colors.deepPurple,
        'page': () => const NorthMatchAstroDetailsPage(),
      },
      {
        'title': 'दक्षिण मिलान',
        'subtitle': 'दशकूट मिलान प्रणाली',
        'icon': Icons.south,
        'color': Colors.orange,
        'page': () => const SouthMatchPage(),
      },
      {
        'title': 'दक्षिण विस्तृत',
        'subtitle': 'दशकूट + ज्योतिष विश्लेषण',
        'icon': Icons.south_east,
        'color': Colors.teal,
        'page': () => const SouthMatchAstroDetailsPage(),
      },
      {
        'title': 'समग्र मिलान',
        'subtitle': 'सम्पूर्ण मिलान विश्लेषण',
        'icon': Icons.analytics,
        'color': Colors.indigo,
        'page': () => const AggregateMatchPage(),
      },
      {
        'title': 'रज्जु वेध मिलान',
        'subtitle': 'विशेष दोष विश्लेषण',
        'icon': Icons.link,
        'color': Colors.brown,
        'page': () => const RajjuVedhaMatchPage(),
      },
      {
        'title': 'पापसमय मिलान',
        'subtitle': 'अशुभ समय विश्लेषण',
        'icon': Icons.schedule,
        'color': Colors.grey,
        'page': () => const PapasamayaMatchPage(),
      },
      {
        'title': 'नक्षत्र मिलान',
        'subtitle': 'नक्षत्र आधारित मिलान',
        'icon': Icons.star,
        'color': Colors.amber,
        'page': () => const NakshatraMatchPage(),
      },
      {
        'title': 'पश्चिमी मिलान',
        'subtitle': 'पश्चिमी ज्योतिष मिलान',
        'icon': Icons.public,
        'color': Colors.blueGrey,
        'page': () => const WesternMatchPage(),
      },

      {
        'title': 'बल्क दक्षिण मिलान',
        'subtitle': 'धेरै दक्षिण मिलान एकसाथ',
        'icon': Icons.groups,
        'color': Colors.orangeAccent,
        'page': () => const BulkSouthMatchPage(),
      },
    ];
  }

  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    Widget? Function() pageBuilder,
  ) {
    return GestureDetector(
      onTap: () {
        final page = pageBuilder();
        if (page != null) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => page),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('यो सेवा छिट्टै उपलब्ध हुनेछ'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withOpacity(0.2),
              color.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 36,
              color: color,
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 11,
                color: color.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _editUser(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddUserPage(editUser: user),
      ),
    );

    if (result == true) {
      // Go back to user list to refresh
      Navigator.pop(context, true);
    }
  }

  void _deleteUser(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: const Text(
          'प्रयोगकर्ता मेटाउनुहोस्',
          style: TextStyle(color: Colors.red),
        ),
        content: Text(
          'के तपाईं ${user.name} को जानकारी मेटाउन चाहनुहुन्छ?\n\nयो कार्य फिर्ता गर्न सकिँदैन।',
          style: const TextStyle(color: Color(0xFF1B5E20)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('रद्द गर्नुहोस्', style: TextStyle(color: Color(0xFF2E7D32))),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('मेटाउनुहोस्', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await UserDataService.deleteUserData(user.id);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('प्रयोगकर्ता मेटाइयो'),
            backgroundColor: Colors.green,
          ),
        );
        // Go back to main Kundali page
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('प्रयोगकर्ता मेटाउन असफल'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
