import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class MahadashaPredictionsPage extends StatefulWidget {
  final UserData user;

  const MahadashaPredictionsPage({Key? key, required this.user}) : super(key: key);

  @override
  State<MahadashaPredictionsPage> createState() => _MahadashaPredictionsPageState();
}

class _MahadashaPredictionsPageState extends State<MahadashaPredictionsPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _predictionsResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchMahadashaPredictions();
  }

  UserData get user => widget.user;

  Future<void> _fetchMahadashaPredictions() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getMahadashaPredictions(user);
      
      setState(() {
        _predictionsResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'महादशा भविष्यवाणी',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchMahadashaPredictions,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_predictionsResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day} • ${user.birthTime}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 20),
          Text(
            'महादशा भविष्यवाणी गरिँदै...',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF1B5E20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchMahadashaPredictions,
            icon: const Icon(Icons.refresh),
            label: const Text('पुनः प्रयास गर्नुहोस्'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.psychology,
                color: Color(0xFF2E7D32),
                size: 32,
              ),
              SizedBox(width: 12),
              Text(
                'महादशा भविष्यवाणी',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Display all the prediction data
          if (_predictionsResult != null)
            _buildPredictionsContent(),
        ],
      ),
    );
  }

  Widget _buildPredictionsContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display each key-value pair from the result
        ..._predictionsResult!.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getIconForKey(entry.key),
                      color: const Color(0xFF2E7D32),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _formatKey(entry.key),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF4CAF50).withOpacity(0.2),
                    ),
                  ),
                  child: _buildFormattedValue(entry.key, entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'current_mahadasha':
        return 'वर्तमान महादशा';
      case 'current_antardasha':
        return 'वर्तमान अन्तर्दशा';
      case 'current_pratyantardasha':
        return 'वर्तमान प्रत्यन्तर्दशा';
      case 'mahadasha_lord':
        return 'महादशा स्वामी';
      case 'antardasha_lord':
        return 'अन्तर्दशा स्वामी';
      case 'pratyantardasha_lord':
        return 'प्रत्यन्तर्दशा स्वामी';
      case 'mahadasha_start':
        return 'महादशा शुरुवात';
      case 'mahadasha_end':
        return 'महादशा समाप्ति';
      case 'antardasha_start':
        return 'अन्तर्दशा शुरुवात';
      case 'antardasha_end':
        return 'अन्तर्दशा समाप्ति';
      case 'predictions':
        return 'भविष्यवाणी';
      case 'general_predictions':
        return 'सामान्य भविष्यवाणी';
      case 'career_predictions':
        return 'करियर भविष्यवाणी';
      case 'health_predictions':
        return 'स्वास्थ्य भविष्यवाणी';
      case 'relationship_predictions':
        return 'सम्बन्ध भविष्यवाणी';
      case 'financial_predictions':
        return 'आर्थिक भविष्यवाणी';
      case 'remedies':
        return 'उपायहरू';
      case 'favorable_periods':
        return 'अनुकूल समयावधि';
      case 'challenging_periods':
        return 'चुनौतीपूर्ण समयावधि';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildFormattedValue(String key, dynamic value) {
    if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.6,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is Map) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((e) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_formatKey(e.key.toString())}:',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2E7D32),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  e.value.toString(),
                  style: const TextStyle(
                    fontSize: 18,
                    color: Color(0xFF1B5E20),
                    height: 1.6,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.6,
        ),
      );
    }
  }

  IconData _getIconForKey(String key) {
    switch (key.toLowerCase()) {
      case 'current_mahadasha':
      case 'mahadasha_lord':
        return Icons.timeline;
      case 'current_antardasha':
      case 'antardasha_lord':
        return Icons.schedule;
      case 'current_pratyantardasha':
      case 'pratyantardasha_lord':
        return Icons.access_time;
      case 'predictions':
      case 'general_predictions':
        return Icons.psychology;
      case 'career_predictions':
        return Icons.work;
      case 'health_predictions':
        return Icons.health_and_safety;
      case 'relationship_predictions':
        return Icons.favorite;
      case 'financial_predictions':
        return Icons.account_balance_wallet;
      case 'remedies':
        return Icons.healing;
      case 'favorable_periods':
        return Icons.thumb_up;
      case 'challenging_periods':
        return Icons.warning;
      case 'bot_response':
        return Icons.analytics;
      default:
        return Icons.info;
    }
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      return value.map((item) => '• $item').join('\n');
    } else if (value is Map) {
      return value.entries
          .map((e) => '${_formatKey(e.key.toString())}: ${e.value}')
          .join('\n');
    } else {
      return value.toString();
    }
  }
}
