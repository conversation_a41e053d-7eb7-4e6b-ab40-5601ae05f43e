import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class MangalDoshPage extends StatefulWidget {
  final UserData user;

  const MangalDoshPage({super.key, required this.user});

  @override
  State<MangalDoshPage> createState() => _MangalDoshPageState();
}

class _MangalDoshPageState extends State<MangalDoshPage> {
  Map<String, dynamic>? _mangalDoshData;
  bool _isLoading = false;
  String? _error;

  // API configuration now managed by GitHubApiManager

  @override
  void initState() {
    super.initState();
    _fetchMangalDosh();
  }

  Future<void> _fetchMangalDosh() async {
    setState(() {
      _isLoading = true;
      _error = null;
      _mangalDoshData = null;
    });

    try {
      final result = await VedicAstroApiService.getMangalDosh(widget.user);

      setState(() {
        _mangalDoshData = {'response': result, 'status': 200};
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0D1B2A),
      appBar: AppBar(
        title: const Text(
          'मंगल दोष जाँच',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1B263B),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchMangalDosh,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF0D1B2A), Color(0xFF1B263B)],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildUserCard(),
              const SizedBox(height: 20),
              if (_isLoading)
                LoadingWidget(
                  message: 'मंगल दोष विश्लेषण गर्दै...',
                  featureName: 'मंगल दोष',
                )
              else if (_error != null)
                ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchMangalDosh,
                  featureName: 'मंगल दोष',
                )
              else if (_mangalDoshData != null)
                _buildResultCard()
              else
                _buildEmptyCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.withOpacity(0.1),
            Colors.purple.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: Colors.amber,
            child: Text(
              widget.user.name.isNotEmpty ? widget.user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.user.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'जन्म: ${widget.user.birthDateBS.year}/${widget.user.birthDateBS.month}/${widget.user.birthDateBS.day} • ${widget.user.birthTime}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
                Text(
                  '${widget.user.birthPlace}, ${widget.user.district}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(15),
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(color: Colors.red),
          SizedBox(height: 20),
          Text(
            'मंगल दोष जाँच गरिदै...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 50),
          const SizedBox(height: 16),
          Text(
            _error!,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchMangalDosh,
            icon: const Icon(Icons.refresh),
            label: const Text('पुनः प्रयास गर्नुहोस्'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyCard() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(15),
      ),
      child: const Column(
        children: [
          Icon(Icons.psychology, size: 60, color: Colors.amber),
          SizedBox(height: 20),
          Text(
            'मंगल दोष जाँच सुरु गर्नुहोस्',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultCard() {
    final response = _mangalDoshData!['response'];
    final isDoshaPresent = response['is_dosha_present'] ?? false;
    final score = response['score'] ?? 0;
    final botResponse = response['bot_response'] ?? '';
    final factors = response['factors'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main result
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDoshaPresent 
                ? [Colors.red.withOpacity(0.2), Colors.orange.withOpacity(0.2)]
                : [Colors.green.withOpacity(0.2), Colors.teal.withOpacity(0.2)],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isDoshaPresent ? Colors.red : Colors.green,
              width: 2,
            ),
          ),
          child: Column(
            children: [
              Icon(
                isDoshaPresent ? Icons.warning : Icons.check_circle,
                color: isDoshaPresent ? Colors.red : Colors.green,
                size: 60,
              ),
              const SizedBox(height: 16),
              Text(
                isDoshaPresent ? 'मंगल दोष छ' : 'मंगल दोष छैन',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isDoshaPresent ? Colors.red : Colors.green,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'स्कोर: $score%',
                style: TextStyle(
                  fontSize: 18,
                  color: isDoshaPresent ? Colors.red : Colors.green,
                ),
              ),
              if (botResponse.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    botResponse,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // Factors
        if (factors.isNotEmpty) ...[
          const SizedBox(height: 24),
          const Text(
            'विस्तृत विवरण:',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.amber,
            ),
          ),
          const SizedBox(height: 16),
          ...factors.entries.map((entry) => _buildFactorCard(entry.key, entry.value)),
        ],
      ],
    );
  }

  Widget _buildFactorCard(String title, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title.toUpperCase(),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.amber,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
