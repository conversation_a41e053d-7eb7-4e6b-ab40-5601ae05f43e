import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/user_data_service.dart';

class NakshatraMatchPage extends StatefulWidget {
  const NakshatraMatchPage({Key? key}) : super(key: key);

  @override
  State<NakshatraMatchPage> createState() => _NakshatraMatchPageState();
}

class _NakshatraMatchPageState extends State<NakshatraMatchPage> {
  bool _isLoading = false;
  dynamic _nakshatraResult;
  String? _error;
  UserData? _selectedBoy;
  UserData? _selectedGirl;
  List<UserData> _allUsers = [];
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _resultKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final users = await UserDataService.loadUserDataList();
      setState(() {
        _allUsers = users;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading users: $e';
      });
    }
  }

  Future<void> _fetchNakshatraMatch() async {
    if (_selectedBoy == null || _selectedGirl == null) {
      setState(() {
        _error = 'कृपया केटा र केटी दुवै छान्नुहोस्';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getNakshatraMatch(_selectedBoy!, _selectedGirl!);

      setState(() {
        _nakshatraResult = result;
        _isLoading = false;
      });

      // Auto-scroll to result after data loads
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_resultKey.currentContext != null) {
          Scrollable.ensureVisible(
            _resultKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'नक्षत्र मिलान',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildInfoCard(),
                const SizedBox(height: 12),
                _buildUserSelectionCard(),
                const SizedBox(height: 12),
                if (_selectedBoy != null && _selectedGirl != null)
                  _buildAnalyzeButton(),
                const SizedBox(height: 12),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_nakshatraResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.star,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'नक्षत्र मिलान',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'जन्म नक्षत्र आधारित मिलान',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelectionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'व्यक्ति छान्नुहोस्',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 12),

          // Boy and Girl Selection in Same Row
          Row(
            children: [
              // Boy Selection
              Expanded(
                child: _buildUserSelector(
                  title: 'केटा छान्नुहोस्',
                  icon: Icons.male,
                  selectedUser: _selectedBoy,
                  onUserSelected: (user) {
                    setState(() {
                      _selectedBoy = user;
                      _nakshatraResult = null; // Clear previous results
                    });
                  },
                ),
              ),

              const SizedBox(width: 12),

              // Girl Selection
              Expanded(
                child: _buildUserSelector(
                  title: 'केटी छान्नुहोस्',
                  icon: Icons.female,
                  selectedUser: _selectedGirl,
                  onUserSelected: (user) {
                    setState(() {
                      _selectedGirl = user;
                      _nakshatraResult = null; // Clear previous results
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelector({
    required String title,
    required IconData icon,
    required UserData? selectedUser,
    required Function(UserData?) onUserSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: const Color(0xFF2E7D32), size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFF4CAF50)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<UserData>(
              value: selectedUser,
              hint: Text(
                'छान्नुहोस्...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              isExpanded: true,
              items: _allUsers.map((user) {
                return DropdownMenuItem<UserData>(
                  value: user,
                  child: Text(
                    '${user.name} (${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day})',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1B5E20),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onUserSelected,
              dropdownColor: Colors.white,
              icon: const Icon(
                Icons.arrow_drop_down,
                color: Color(0xFF4CAF50),
              ),
            ),
          ),
        ),

        if (selectedUser != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF2E7D32), size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedUser.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B5E20),
                        ),
                      ),
                      Text(
                        'जन्म: ${selectedUser.birthDateBS.year}/${selectedUser.birthDateBS.month}/${selectedUser.birthDateBS.day} ${selectedUser.birthTime}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      Text(
                        'स्थान: ${selectedUser.district}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAnalyzeButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _fetchNakshatraMatch,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 3,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.star, size: 24),
            const SizedBox(width: 12),
            Text(
              'नक्षत्र मिलान विश्लेषण गर्नुहोस्',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'नक्षत्र मिलान विश्लेषण गर्दै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'कृपया पर्खनुहोस्',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF388E3C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchNakshatraMatch,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with couple info
          _buildCoupleHeader(),
          const SizedBox(height: 20),

          if (_nakshatraResult != null)
            _buildNakshatraContent(),
        ],
      ),
    );
  }

  Widget _buildCoupleHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE8F5E8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Boy info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.male, color: Color(0xFF2196F3), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedBoy?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedBoy?.birthDateBS.year}/${_selectedBoy?.birthDateBS.month}/${_selectedBoy?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // Star icon for Nakshatra
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.star,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Girl info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.female, color: Color(0xFFE91E63), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedGirl?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedGirl?.birthDateBS.year}/${_selectedGirl?.birthDateBS.month}/${_selectedGirl?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNakshatraContent() {
    if (_nakshatraResult is Map<String, dynamic>) {
      final data = _nakshatraResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Score
          if (data['score'] != null)
            _buildScoreCard(data['score'], data['bot_response']),

          const SizedBox(height: 16),

          // Individual Koot Analysis
          ...data.entries.where((entry) =>
            entry.key != 'score' &&
            entry.key != 'bot_response'
          ).map((entry) {
            if (entry.value is Map<String, dynamic>) {
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F8E9),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
                ),
                child: _buildKootCard(entry.key, entry.value as Map<String, dynamic>),
              );
            } else {
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF1F8E9),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.info,
                          color: Color(0xFF2E7D32),
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _translateKootName(entry.key),
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2E7D32),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      entry.value.toString(),
                      style: const TextStyle(
                        fontSize: 18,
                        color: Color(0xFF1B5E20),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              );
            }
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_nakshatraResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }
  IconData _getKootIcon(String kootName) {
    switch (kootName.toLowerCase()) {
      case 'dina':
        return Icons.wb_sunny;
      case 'gana':
        return Icons.group;
      case 'mahendra':
        return Icons.child_care;
      case 'sthree':
        return Icons.account_balance_wallet;
      case 'yoni':
        return Icons.favorite;
      case 'rasi':
        return Icons.circle;
      case 'rasiathi':
        return Icons.handshake;
      case 'vasya':
        return Icons.psychology;
      case 'rajju':
        return Icons.link;
      case 'vedha':
        return Icons.shield;
      default:
        return Icons.star;
    }
  }

  String _translateKootName(String kootName) {
    switch (kootName.toLowerCase()) {
      case 'dina':
        return 'दिन कूट';
      case 'gana':
        return 'गण कूट';
      case 'mahendra':
        return 'महेन्द्र कूट';
      case 'sthree':
        return 'स्त्री दीर्घ कूट';
      case 'yoni':
        return 'योनि कूट';
      case 'rasi':
        return 'राशि कूट';
      case 'rasiathi':
        return 'राश्याधिपति कूट';
      case 'vasya':
        return 'वश्य कूट';
      case 'rajju':
        return 'रज्जु कूट';
      case 'vedha':
        return 'वेध कूट';
      default:
        return kootName.toUpperCase();
    }
  }

  String _translateKey(String key) {
    switch (key.toLowerCase()) {
      case 'boy_star':
        return 'केटाको नक्षत्र';
      case 'girl_star':
        return 'केटीको नक्षत्र';
      case 'boy_gana':
        return 'केटाको गण';
      case 'girl_gana':
        return 'केटीको गण';
      case 'boy_yoni':
        return 'केटाको योनि';
      case 'girl_yoni':
        return 'केटीको योनि';
      case 'boy_rasi':
        return 'केटाको राशि';
      case 'girl_rasi':
        return 'केटीको राशि';
      case 'boy_lord':
        return 'केटाको स्वामी';
      case 'girl_lord':
        return 'केटीको स्वामी';
      case 'boy_rajju':
        return 'केटाको रज्जु';
      case 'girl_rajju':
        return 'केटीको रज्जु';
      case 'description':
        return 'विवरण';
      case 'full_score':
        return 'पूर्ण अंक';
      case 'score':
        return 'अंक';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildScoreCard(dynamic score, dynamic botResponse) {
    final scoreValue = double.tryParse(score.toString()) ?? 0;
    final percentage = (scoreValue / 10) * 100; // Assuming max score is 10

    Color scoreColor;
    String compatibility;
    if (percentage >= 80) {
      scoreColor = const Color(0xFF4CAF50);
      compatibility = 'उत्कृष्ट मिलान';
    } else if (percentage >= 65) {
      scoreColor = const Color(0xFF8BC34A);
      compatibility = 'राम्रो मिलान';
    } else if (percentage >= 45) {
      scoreColor = const Color(0xFFFF9800);
      compatibility = 'मध्यम मिलान';
    } else {
      scoreColor = const Color(0xFFFF5722);
      compatibility = 'कम मिलान';
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: scoreColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: scoreColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.star, color: scoreColor, size: 32),
              const SizedBox(width: 12),
              Text(
                'स्कोर: $scoreValue/10',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: scoreColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            compatibility,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: scoreColor,
            ),
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
            minHeight: 8,
          ),
          if (botResponse != null) ...[
            const SizedBox(height: 12),
            Text(
              botResponse.toString(),
              style: TextStyle(
                fontSize: 16,
                color: scoreColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildKootCard(String kootName, Map<String, dynamic> kootData) {
    final score = kootData['score'] ?? kootData[kootName] ?? 0;
    final fullScore = kootData['full_score'] ?? 1;
    final description = kootData['description'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              _getKootIcon(kootName),
              color: const Color(0xFF2E7D32),
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _translateKootName(kootName),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '$score/$fullScore',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Display all koot details
        ...kootData.entries.where((entry) =>
          entry.key != 'score' &&
          entry.key != 'full_score' &&
          entry.key != kootName
        ).map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_translateKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    entry.value.toString(),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1B5E20),
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),

        if (description.isNotEmpty) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFE3F2FD),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Color(0xFF1976D2),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF1565C0),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
