import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/mantra_model.dart';
import '../services/mantra_service.dart';

class NewHinduMantraAudioPage extends StatefulWidget {
  const NewHinduMantraAudioPage({super.key});

  @override
  State<NewHinduMantraAudioPage> createState() => _NewHinduMantraAudioPageState();
}

class _NewHinduMantraAudioPageState extends State<NewHinduMantraAudioPage> 
    with TickerProviderStateMixin {
  late AudioPlayer _audioPlayer;
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  
  List<Mantra> _mantras = [];
  Mantra? _currentMantra;
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  bool _isLooping = true; // Default to loop mode

  @override
  void initState() {
    super.initState();
    _initializeAudio();
    _initializeAnimations();
    _loadMantras();
  }

  void _initializeAudio() {
    _audioPlayer = AudioPlayer();
    _audioPlayer.setReleaseMode(ReleaseMode.loop); // Set to loop by default
    
    _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      if (mounted) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
          _isLoading = state == PlayerState.playing ? false : _isLoading;
        });
        
        if (_isPlaying) {
          _rotationController.repeat();
          _pulseController.repeat(reverse: true);
        } else {
          _rotationController.stop();
          _pulseController.stop();
        }
      }
    });

    _audioPlayer.onDurationChanged.listen((Duration duration) {
      if (mounted) {
        setState(() {
          _duration = duration;
        });
      }
    });

    _audioPlayer.onPositionChanged.listen((Duration position) {
      if (mounted) {
        setState(() {
          _position = position;
        });
      }
    });
  }

  void _initializeAnimations() {
    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
  }

  void _loadMantras() {
    setState(() {
      _mantras = MantraService.getAllMantras();
      if (_mantras.isNotEmpty) {
        _currentMantra = _mantras.first;
      }
    });
  }

  Future<void> _playMantra(Mantra mantra) async {
    try {
      setState(() {
        _isLoading = true;
        _currentMantra = mantra;
      });

      await _audioPlayer.stop();
      await _audioPlayer.play(AssetSource(mantra.audioAssetPath));
      
      HapticFeedback.lightImpact();
    } catch (e) {
      print('Error playing mantra: $e');
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'मन्त्र चलाउन सकिएन',
              style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _togglePlayPause() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        if (_currentMantra != null) {
          await _audioPlayer.resume();
        }
      }
      HapticFeedback.lightImpact();
    } catch (e) {
      print('Error toggling play/pause: $e');
    }
  }

  Future<void> _toggleLoop() async {
    setState(() {
      _isLooping = !_isLooping;
    });
    
    await _audioPlayer.setReleaseMode(_isLooping ? ReleaseMode.loop : ReleaseMode.stop);
    HapticFeedback.lightImpact();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isLooping ? 'लूप मोड सक्रिय' : 'लूप मोड निष्क्रिय',
            style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
          ),
          duration: const Duration(seconds: 1),
          backgroundColor: _isLooping ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        title: const Text(
          '🕉️ हिन्दू मन्त्र',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Colors.white,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(
              _isLooping ? Icons.repeat : Icons.repeat_one,
              color: _isLooping ? Colors.orange : Colors.white54,
            ),
            onPressed: _toggleLoop,
            tooltip: _isLooping ? 'लूप बन्द गर्नुहोस्' : 'लूप सुरु गर्नुहोस्',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F0F23),
            ],
          ),
        ),
        child: Column(
          children: [
            // Current Playing Section
            Expanded(
              flex: 3,
              child: _buildCurrentPlayingSection(),
            ),
            
            // Mantras List
            Expanded(
              flex: 2,
              child: _buildMantrasListSection(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentPlayingSection() {
    if (_currentMantra == null) {
      return const Center(
        child: Text(
          'कुनै मन्त्र चयन गरिएको छैन',
          style: TextStyle(
            color: Colors.white54,
            fontSize: 18,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Mantra Icon with Animation
          AnimatedBuilder(
            animation: _rotationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationController.value * 2 * 3.14159,
                child: AnimatedBuilder(
                  animation: _pulseController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (_pulseController.value * 0.1),
                      child: Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.orange.withOpacity(0.8),
                              Colors.deepOrange.withOpacity(0.6),
                              Colors.red.withOpacity(0.4),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.orange.withOpacity(0.5),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            _currentMantra!.iconEmoji,
                            style: const TextStyle(fontSize: 60),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
          
          const SizedBox(height: 24),
          
          // Mantra Title
          Text(
            _currentMantra!.titleNepali,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          // Sanskrit Text
          Text(
            _currentMantra!.titleSanskrit,
            style: TextStyle(
              color: Colors.orange[300],
              fontSize: 16,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 24),
          
          // Play Controls
          _buildPlayControls(),
          
          const SizedBox(height: 16),
          
          // Progress Bar
          _buildProgressBar(),
        ],
      ),
    );
  }

  Widget _buildPlayControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Previous Button
        IconButton(
          onPressed: _playPreviousMantra,
          icon: const Icon(Icons.skip_previous, color: Colors.white, size: 32),
        ),

        const SizedBox(width: 16),

        // Play/Pause Button
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                Colors.orange,
                Colors.deepOrange,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.4),
                blurRadius: 15,
                spreadRadius: 2,
              ),
            ],
          ),
          child: IconButton(
            onPressed: _isLoading ? null : _togglePlayPause,
            icon: _isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 36,
                  ),
          ),
        ),

        const SizedBox(width: 16),

        // Next Button
        IconButton(
          onPressed: _playNextMantra,
          icon: const Icon(Icons.skip_next, color: Colors.white, size: 32),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Column(
      children: [
        // Time Display
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _formatDuration(_position),
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
            Text(
              _formatDuration(_duration),
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // Progress Slider
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Colors.orange,
            inactiveTrackColor: Colors.white24,
            thumbColor: Colors.orange,
            overlayColor: Colors.orange.withOpacity(0.2),
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            trackHeight: 4,
          ),
          child: Slider(
            value: _duration.inMilliseconds > 0
                ? _position.inMilliseconds / _duration.inMilliseconds
                : 0.0,
            onChanged: (value) {
              final newPosition = Duration(
                milliseconds: (value * _duration.inMilliseconds).round(),
              );
              _audioPlayer.seek(newPosition);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMantrasListSection() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A2E),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30),
          topRight: Radius.circular(30),
        ),
      ),
      child: Column(
        children: [
          // Section Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Text(
                  'मन्त्र सूची',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${_mantras.length} मन्त्र',
                    style: const TextStyle(
                      color: Colors.orange,
                      fontSize: 12,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Mantras List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _mantras.length,
              itemBuilder: (context, index) {
                final mantra = _mantras[index];
                final isCurrentMantra = _currentMantra?.id == mantra.id;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  decoration: BoxDecoration(
                    color: isCurrentMantra
                        ? Colors.orange.withOpacity(0.1)
                        : Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(16),
                    border: isCurrentMantra
                        ? Border.all(color: Colors.orange.withOpacity(0.5))
                        : null,
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(16),
                    leading: Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: isCurrentMantra
                              ? [Colors.orange, Colors.deepOrange]
                              : [Colors.white24, Colors.white12],
                        ),
                      ),
                      child: Center(
                        child: Text(
                          mantra.iconEmoji,
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                    title: Text(
                      mantra.titleNepali,
                      style: TextStyle(
                        color: isCurrentMantra ? Colors.orange : Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 4),
                        Text(
                          mantra.titleEnglish,
                          style: TextStyle(
                            color: isCurrentMantra ? Colors.orange[300] : Colors.white54,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          mantra.benefits,
                          style: TextStyle(
                            color: Colors.white38,
                            fontSize: 11,
                            fontFamily: 'NotoSansDevanagari',
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    trailing: isCurrentMantra && _isPlaying
                        ? const Icon(
                            Icons.graphic_eq,
                            color: Colors.orange,
                            size: 24,
                          )
                        : const Icon(
                            Icons.play_circle_outline,
                            color: Colors.white54,
                            size: 24,
                          ),
                    onTap: () => _playMantra(mantra),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _playPreviousMantra() {
    if (_mantras.isEmpty) return;

    final currentIndex = _mantras.indexWhere((m) => m.id == _currentMantra?.id);
    final previousIndex = currentIndex > 0 ? currentIndex - 1 : _mantras.length - 1;

    _playMantra(_mantras[previousIndex]);
  }

  void _playNextMantra() {
    if (_mantras.isEmpty) return;

    final currentIndex = _mantras.indexWhere((m) => m.id == _currentMantra?.id);
    final nextIndex = currentIndex < _mantras.length - 1 ? currentIndex + 1 : 0;

    _playMantra(_mantras[nextIndex]);
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
