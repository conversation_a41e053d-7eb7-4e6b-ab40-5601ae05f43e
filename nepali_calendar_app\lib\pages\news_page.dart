import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/news_article.dart';
import '../services/news_service.dart';

class NewsPage extends StatefulWidget {
  const NewsPage({Key? key}) : super(key: key);

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<NewsArticle> _allNews = [];
  List<NewsArticle> _filteredNews = [];
  bool _isLoading = true;
  String _selectedCategory = 'सबै';
  String _selectedSource = 'सबै';

  final List<String> _sources = ['सबै'] + NewsService.getSources();
  final List<String> _categories = NewsService.getCategories();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this); // News List and Categories
    _loadNews();
  }

  Future<void> _loadNews() async {
    setState(() {
      _isLoading = true;
      _allNews = [];
      _filteredNews = [];
    });

    try {
      // Use progressive loading - show news as they load
      final news = await NewsService.getAllNews(
        onProgressUpdate: (progressNews) {
          if (mounted) {
            setState(() {
              _allNews = progressNews;
              _filterNews();
            });
          }
        },
      );

      if (mounted) {
        setState(() {
          _allNews = news;
          _filteredNews = news;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterNews() {
    setState(() {
      _filteredNews = _allNews.where((article) {
        bool matchesSource = _selectedSource == 'सबै' || article.source == _selectedSource;
        bool matchesCategory = _selectedCategory == 'सबै' || article.category == _selectedCategory;
        return matchesSource && matchesCategory;
      }).toList();
    });
  }

  Future<void> _refreshNews() async {
    // Clear cache and reload fresh news
    await NewsService.clearCache();
    await _loadNews();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF6A1B9A), Color(0xFF8E24AA)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                  const Expanded(
                    child: Text(
                      'नेपाली समाचार',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh, color: Colors.white),
                    onPressed: _isLoading ? null : _refreshNews,
                    tooltip: 'ताजा समाचार लोड गर्नुहोस्',
                  ),
                ],
              ),
            ),

            // Filters
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[100],
              child: Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedSource,
                      decoration: const InputDecoration(
                        labelText: 'स्रोत',
                        labelStyle: TextStyle(fontFamily: 'NotoSansDevanagari'),
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: _sources.map((source) => DropdownMenuItem(
                        value: source,
                        child: Text(
                          source,
                          style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
                        ),
                      )).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSource = value!;
                        });
                        _filterNews();
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'श्रेणी',
                        labelStyle: TextStyle(fontFamily: 'NotoSansDevanagari'),
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: _categories.map((category) => DropdownMenuItem(
                        value: category,
                        child: Text(
                          category,
                          style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
                        ),
                      )).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                        _filterNews();
                      },
                    ),
                  ),
                ],
              ),
            ),

            // News List
            Expanded(
              child: _isLoading && _filteredNews.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Color(0xFF6A1B9A)),
                          SizedBox(height: 16),
                          Text(
                            'समाचार लोड गर्दै...',
                            style: TextStyle(
                              fontSize: 16,
                              color: Color(0xFF6A1B9A),
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'कृपया प्रतीक्षा गर्नुहोस्',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                        ],
                      ),
                    )
                  : _filteredNews.isEmpty
                      ? const Center(
                          child: Text(
                            'कुनै समाचार फेला परेन',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                        )
                      : Column(
                          children: [
                            // Show loading indicator at top when refreshing
                            if (_isLoading && _filteredNews.isNotEmpty)
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(8),
                                color: const Color(0xFF6A1B9A).withOpacity(0.1),
                                child: const Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Color(0xFF6A1B9A),
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'नयाँ समाचार लोड गर्दै...',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF6A1B9A),
                                        fontFamily: 'NotoSansDevanagari',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            // News list
                            Expanded(
                              child: ListView.builder(
                                itemCount: _filteredNews.length,
                                itemBuilder: (context, index) {
                                  final article = _filteredNews[index];
                                  return _buildNewsCard(article);
                                },
                              ),
                            ),
                          ],
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewsCard(NewsArticle article) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _openArticle(article),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Source and time
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6A1B9A).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      article.source,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF6A1B9A),
                        fontWeight: FontWeight.w600,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatTime(article.publishedAt),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Title
              Text(
                article.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  fontFamily: 'NotoSansDevanagari',
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // Description
              Text(
                article.description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontFamily: 'NotoSansDevanagari',
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Category and read more
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      article.category,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.orange,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ),
                  const Spacer(),
                  const Text(
                    'पूरा पढ्नुहोस् →',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF6A1B9A),
                      fontWeight: FontWeight.w600,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} घण्टा अगाडि';
    } else {
      return '${difference.inDays} दिन अगाडि';
    }
  }

  void _openArticle(NewsArticle article) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArticleDetailPage(article: article),
      ),
    );
  }
}

class ArticleDetailPage extends StatelessWidget {
  final NewsArticle article;

  const ArticleDetailPage({Key? key, required this.article}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // App Bar with image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            backgroundColor: const Color(0xFF6A1B9A),
            iconTheme: const IconThemeData(color: Colors.white),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.3),
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: article.imageUrl.isNotEmpty
                    ? Image.network(
                        article.imageUrl,
                        fit: BoxFit.cover,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            color: Colors.grey[200],
                            child: Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                                strokeWidth: 2,
                                color: const Color(0xFF6A1B9A),
                              ),
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: const Color(0xFF6A1B9A),
                            child: const Center(
                              child: Icon(
                                Icons.article,
                                size: 80,
                                color: Colors.white,
                              ),
                            ),
                          );
                        },
                        // Add caching headers
                        headers: const {
                          'Cache-Control': 'max-age=3600',
                        },
                      )
                    : Container(
                        color: const Color(0xFF6A1B9A),
                        child: const Center(
                          child: Icon(
                            Icons.article,
                            size: 80,
                            color: Colors.white,
                          ),
                        ),
                      ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.open_in_browser, color: Colors.white),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ArticleWebView(article: article),
                    ),
                  );
                },
              ),
            ],
          ),

          // Article content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Source and category
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6A1B9A),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          article.source,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'NotoSansDevanagari',
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          article.category,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'NotoSansDevanagari',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Title
                  Text(
                    article.title,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      fontFamily: 'NotoSansDevanagari',
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Time
                  Row(
                    children: [
                      const Icon(Icons.access_time, size: 16, color: Colors.grey),
                      const SizedBox(width: 6),
                      Text(
                        _formatTime(article.publishedAt),
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                          fontFamily: 'NotoSansDevanagari',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Content
                  Text(
                    article.description,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                      fontFamily: 'NotoSansDevanagari',
                      height: 1.6,
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Read full article button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ArticleWebView(article: article),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6A1B9A),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      icon: const Icon(Icons.open_in_browser),
                      label: const Text(
                        'वेबसाइटमा पूरा पढ्नुहोस्',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'NotoSansDevanagari',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} घण्टा अगाडि';
    } else {
      return '${difference.inDays} दिन अगाडि';
    }
  }
}

class ArticleWebView extends StatefulWidget {
  final NewsArticle article;

  const ArticleWebView({Key? key, required this.article}) : super(key: key);

  @override
  State<ArticleWebView> createState() => _ArticleWebViewState();
}

class _ArticleWebViewState extends State<ArticleWebView> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) async {
            // Inject CSS to fix mobile viewport and prevent horizontal scrolling
            try {
              await _controller.runJavaScript('''
                // Remove existing viewport meta tags
                var existingMetas = document.querySelectorAll('meta[name="viewport"]');
                existingMetas.forEach(function(meta) { meta.remove(); });

                // Add proper mobile viewport
                var meta = document.createElement('meta');
                meta.name = 'viewport';
                meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no';
                document.getElementsByTagName('head')[0].appendChild(meta);

                // Comprehensive CSS fixes
                var style = document.createElement('style');
                style.innerHTML = \`
                  html, body {
                    overflow-x: hidden !important;
                    width: 100% !important;
                    max-width: 100% !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    box-sizing: border-box !important;
                  }

                  * {
                    max-width: 100% !important;
                    box-sizing: border-box !important;
                  }

                  img, video, iframe, embed, object {
                    max-width: 100% !important;
                    height: auto !important;
                    object-fit: contain !important;
                  }

                  table {
                    width: 100% !important;
                    table-layout: fixed !important;
                    word-wrap: break-word !important;
                  }

                  pre, code {
                    white-space: pre-wrap !important;
                    word-wrap: break-word !important;
                    overflow-x: hidden !important;
                  }

                  .container, .wrapper, .main, .content,
                  div[class*="container"], div[class*="wrapper"],
                  div[class*="main"], div[class*="content"] {
                    max-width: 100% !important;
                    overflow-x: hidden !important;
                    width: 100% !important;
                  }

                  /* Hide horizontal scrollbars */
                  ::-webkit-scrollbar:horizontal {
                    display: none !important;
                  }

                  /* Force word wrapping */
                  p, div, span, h1, h2, h3, h4, h5, h6 {
                    word-wrap: break-word !important;
                    overflow-wrap: break-word !important;
                  }
                \`;
                document.head.appendChild(style);

                // Force reflow
                document.body.style.display = 'none';
                document.body.offsetHeight;
                document.body.style.display = '';
              ''');
            } catch (e) {
              // Ignore JavaScript injection errors
            }

            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.article.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.article.source,
          style: const TextStyle(
            fontFamily: 'NotoSansDevanagari',
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6A1B9A),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () {
              _controller.reload();
              setState(() {
                _isLoading = true;
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            Container(
              color: Colors.white,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      color: Color(0xFF6A1B9A),
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      '${widget.article.source} लोड गर्दै...',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF6A1B9A),
                        fontWeight: FontWeight.w600,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'कृपया प्रतीक्षा गर्नुहोस्',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
