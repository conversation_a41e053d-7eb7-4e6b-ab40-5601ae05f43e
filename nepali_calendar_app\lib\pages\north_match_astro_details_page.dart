import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/user_data_service.dart';

class NorthMatchAstroDetailsPage extends StatefulWidget {
  const NorthMatchAstroDetailsPage({Key? key}) : super(key: key);

  @override
  State<NorthMatchAstroDetailsPage> createState() => _NorthMatchAstroDetailsPageState();
}

class _NorthMatchAstroDetailsPageState extends State<NorthMatchAstroDetailsPage> {
  bool _isLoading = false;
  dynamic _astroDetailsResult;
  String? _error;
  UserData? _selectedBoy;
  UserData? _selectedGirl;
  List<UserData> _allUsers = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final users = await UserDataService.loadUserDataList();
      setState(() {
        _allUsers = users;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading users: $e';
      });
    }
  }

  Future<void> _fetchAstroDetails() async {
    if (_selectedBoy == null || _selectedGirl == null) {
      setState(() {
        _error = 'कृपया केटा र केटी दुवै छान्नुहोस्';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getNorthMatchWithAstroDetails(_selectedBoy!, _selectedGirl!);
      
      setState(() {
        _astroDetailsResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'विस्तृत ज्योतिष मिलान',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildInfoCard(),
                const SizedBox(height: 12),
                _buildUserSelectionCard(),
                const SizedBox(height: 12),
                if (_selectedBoy != null && _selectedGirl != null)
                  _buildAnalyzeButton(),
                const SizedBox(height: 12),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_astroDetailsResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'विस्तृत ज्योतिष मिलान',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'अष्टकूट + ज्योतिषीय विवरण सहित',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelectionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'व्यक्ति छान्नुहोस्',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 20),
          
          // Boy Selection
          _buildUserSelector(
            title: 'केटा छान्नुहोस्',
            icon: Icons.male,
            selectedUser: _selectedBoy,
            onUserSelected: (user) {
              setState(() {
                _selectedBoy = user;
                _astroDetailsResult = null; // Clear previous results
              });
            },
          ),
          
          const SizedBox(height: 20),
          
          // Girl Selection
          _buildUserSelector(
            title: 'केटी छान्नुहोस्',
            icon: Icons.female,
            selectedUser: _selectedGirl,
            onUserSelected: (user) {
              setState(() {
                _selectedGirl = user;
                _astroDetailsResult = null; // Clear previous results
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelector({
    required String title,
    required IconData icon,
    required UserData? selectedUser,
    required Function(UserData?) onUserSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: const Color(0xFF2E7D32), size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFF4CAF50)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<UserData>(
              value: selectedUser,
              hint: Text(
                'छान्नुहोस्...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              isExpanded: true,
              items: _allUsers.map((user) {
                return DropdownMenuItem<UserData>(
                  value: user,
                  child: Text(
                    '${user.name} (${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day})',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1B5E20),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onUserSelected,
              dropdownColor: Colors.white,
              icon: const Icon(
                Icons.arrow_drop_down,
                color: Color(0xFF4CAF50),
              ),
            ),
          ),
        ),
        
        if (selectedUser != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF2E7D32), size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedUser.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B5E20),
                        ),
                      ),
                      Text(
                        'जन्म: ${selectedUser.birthDateBS.year}/${selectedUser.birthDateBS.month}/${selectedUser.birthDateBS.day} ${selectedUser.birthTime}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      Text(
                        'स्थान: ${selectedUser.district}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAnalyzeButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _fetchAstroDetails,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 3,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.auto_awesome, size: 24),
            const SizedBox(width: 12),
            Text(
              'विस्तृत ज्योतिष विश्लेषण गर्नुहोस्',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'विस्तृत ज्योतिष विश्लेषण गर्दै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'कृपया पर्खनुहोस्, यसले केही समय लाग्न सक्छ',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF388E3C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchAstroDetails,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with couple info
          _buildCoupleHeader(),
          const SizedBox(height: 20),

          if (_astroDetailsResult != null)
            _buildAstroDetailsContent(),
        ],
      ),
    );
  }

  Widget _buildCoupleHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE8F5E8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Boy info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.male, color: Color(0xFF2196F3), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedBoy?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedBoy?.birthDateBS.year}/${_selectedBoy?.birthDateBS.month}/${_selectedBoy?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // Star icon for astro details
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Girl info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.female, color: Color(0xFFE91E63), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedGirl?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedGirl?.birthDateBS.year}/${_selectedGirl?.birthDateBS.month}/${_selectedGirl?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAstroDetailsContent() {
    if (_astroDetailsResult is Map<String, dynamic>) {
      final data = _astroDetailsResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all sections from the detailed result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getDetailIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _formatDetailKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_astroDetailsResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_translateKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  IconData _getDetailIcon(String key) {
    switch (key.toLowerCase()) {
      case 'ashtakoot':
      case 'compatibility':
        return Icons.favorite;
      case 'boy_details':
      case 'boy_info':
        return Icons.male;
      case 'girl_details':
      case 'girl_info':
        return Icons.female;
      case 'planetary_positions':
      case 'planets':
        return Icons.public;
      case 'houses':
      case 'house_positions':
        return Icons.home_work;
      case 'aspects':
        return Icons.visibility;
      case 'yogas':
        return Icons.self_improvement;
      case 'doshas':
        return Icons.warning;
      case 'recommendations':
        return Icons.lightbulb;
      case 'analysis':
        return Icons.analytics;
      case 'summary':
        return Icons.summarize;
      case 'detailed_analysis':
        return Icons.auto_awesome;
      case 'birth_chart':
        return Icons.circle;
      case 'navamsa':
        return Icons.crop_square;
      case 'dasha':
        return Icons.timeline;
      case 'transit':
        return Icons.swap_horiz;
      case 'strength':
        return Icons.fitness_center;
      case 'weakness':
        return Icons.trending_down;
      case 'remedies':
        return Icons.healing;
      case 'gemstones':
        return Icons.diamond;
      case 'mantras':
        return Icons.music_note;
      case 'fasting':
        return Icons.restaurant;
      case 'charity':
        return Icons.volunteer_activism;
      case 'colors':
        return Icons.palette;
      case 'directions':
        return Icons.explore;
      case 'numbers':
        return Icons.numbers;
      case 'metals':
        return Icons.hardware;
      default:
        return Icons.info;
    }
  }

  String _formatDetailKey(String key) {
    switch (key.toLowerCase()) {
      case 'ashtakoot':
        return 'अष्टकूट मिलान';
      case 'compatibility':
        return 'मिलान विश्लेषण';
      case 'boy_details':
      case 'boy_info':
        return 'केटाको ज्योतिषीय विवरण';
      case 'girl_details':
      case 'girl_info':
        return 'केटीको ज्योतिषीय विवरण';
      case 'planetary_positions':
      case 'planets':
        return 'ग्रह स्थिति';
      case 'houses':
      case 'house_positions':
        return 'भाव स्थिति';
      case 'aspects':
        return 'ग्रह दृष्टि';
      case 'yogas':
        return 'योगहरू';
      case 'doshas':
        return 'दोषहरू';
      case 'recommendations':
        return 'सिफारिसहरू';
      case 'analysis':
        return 'विश्लेषण';
      case 'summary':
        return 'सारांश';
      case 'detailed_analysis':
        return 'विस्तृत विश्लेषण';
      case 'birth_chart':
        return 'जन्म कुण्डली';
      case 'navamsa':
        return 'नवांश चार्ट';
      case 'dasha':
        return 'दशा विश्लेषण';
      case 'transit':
        return 'गोचर फल';
      case 'strength':
        return 'शक्तिहरू';
      case 'weakness':
        return 'कमजोरीहरू';
      case 'remedies':
        return 'उपायहरू';
      case 'gemstones':
        return 'रत्नहरू';
      case 'mantras':
        return 'मन्त्रहरू';
      case 'fasting':
        return 'व्रत';
      case 'charity':
        return 'दान';
      case 'colors':
        return 'रंगहरू';
      case 'directions':
        return 'दिशाहरू';
      case 'numbers':
        return 'अंकहरू';
      case 'metals':
        return 'धातुहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _translateKey(String key) {
    switch (key.toLowerCase()) {
      case 'points':
        return 'अंक';
      case 'description':
        return 'विवरण';
      case 'name':
        return 'नाम';
      case 'birth_date':
        return 'जन्म मिति';
      case 'birth_time':
        return 'जन्म समय';
      case 'birth_place':
        return 'जन्म स्थान';
      case 'sun_sign':
        return 'सूर्य राशि';
      case 'moon_sign':
        return 'चन्द्र राशि';
      case 'ascendant':
        return 'लग्न राशि';
      case 'nakshatra':
        return 'नक्षत्र';
      case 'pada':
        return 'पाद';
      case 'tithi':
        return 'तिथि';
      case 'karana':
        return 'करण';
      case 'yoga':
        return 'योग';
      case 'varna':
        return 'वर्ण';
      case 'vashya':
        return 'वश्य';
      case 'tara':
        return 'तारा';
      case 'yoni':
        return 'योनि';
      case 'graha_maitri':
        return 'ग्रह मैत्री';
      case 'gana':
        return 'गण';
      case 'bhakoot':
        return 'भकूट';
      case 'nadi':
        return 'नाडी';
      case 'total_points':
        return 'कुल अंक';
      case 'compatibility':
        return 'मिलान';
      case 'result':
        return 'परिणाम';
      case 'status':
        return 'स्थिति';
      case 'percentage':
        return 'प्रतिशत';
      case 'recommendation':
        return 'सिफारिस';
      case 'analysis':
        return 'विश्लेषण';
      case 'strength':
        return 'शक्ति';
      case 'weakness':
        return 'कमजोरी';
      case 'favorable':
        return 'अनुकूल';
      case 'unfavorable':
        return 'प्रतिकूल';
      case 'neutral':
        return 'सम';
      case 'excellent':
        return 'उत्कृष्ट';
      case 'good':
        return 'राम्रो';
      case 'average':
        return 'मध्यम';
      case 'poor':
        return 'कम';
      case 'planet':
        return 'ग्रह';
      case 'house':
        return 'भाव';
      case 'degree':
        return 'डिग्री';
      case 'sign':
        return 'राशि';
      case 'lord':
        return 'स्वामी';
      case 'element':
        return 'तत्व';
      case 'quality':
        return 'गुण';
      case 'nature':
        return 'प्रकृति';
      case 'benefic':
        return 'शुभ';
      case 'malefic':
        return 'अशुभ';
      case 'exalted':
        return 'उच्च';
      case 'debilitated':
        return 'नीच';
      case 'own_sign':
        return 'स्वराशि';
      case 'friend':
        return 'मित्र';
      case 'enemy':
        return 'शत्रु';
      case 'remedies':
        return 'उपायहरू';
      case 'gemstone':
        return 'रत्न';
      case 'mantra':
        return 'मन्त्र';
      case 'color':
        return 'रंग';
      case 'direction':
        return 'दिशा';
      case 'number':
        return 'अंक';
      case 'metal':
        return 'धातु';
      case 'day':
        return 'दिन';
      case 'time':
        return 'समय';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
