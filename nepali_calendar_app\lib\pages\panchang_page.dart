import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/panchang_model.dart';
import '../services/panchang_service.dart';

class PanchangPage extends StatefulWidget {
  const PanchangPage({Key? key}) : super(key: key);

  @override
  State<PanchangPage> createState() => _PanchangPageState();
}

class _PanchangPageState extends State<PanchangPage> {
  final PanchangService _panchangService = PanchangService();
  PanchangData? _panchangData;
  bool _isLoading = true;
  bool _showWebView = false;
  WebViewController? _webViewController;

  @override
  void initState() {
    super.initState();
    _loadPanchangData();
  }

  Future<void> _loadPanchangData({bool forceRefresh = false}) async {
    setState(() {
      _isLoading = true;
      _showWebView = false; // Hide WebView during loading
    });

    try {
      // Always get fresh data (service handles online/offline logic)
      final panchangData = await _panchangService.getPanchangData(forceRefresh: true);

      if (panchangData != null) {
        setState(() {
          _panchangData = panchangData;
          _isLoading = false;
        });

        // Show success message on refresh
        if (forceRefresh && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'पञ्चाङ्ग अपडेट भयो!',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
        return;
      }

      // If no data available, show error
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'पञ्चाङ्ग लोड गर्न सकिएन',
              style: TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'त्रुटि: $e',
              style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _extractDataFromWebView() async {
    if (_webViewController != null) {
      final data = await _panchangService.extractDataFromWebView();
      if (data != null) {
        setState(() {
          _panchangData = data;
          _showWebView = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF6A1B9A),
      body: SafeArea(
        child: _showWebView ? _buildWebView() : _buildPanchangUI(),
      ),
    );
  }

  Widget _buildWebView() {
    // Initialize WebView controller
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) async {
            // Wait a bit for the content to load completely
            await Future.delayed(const Duration(seconds: 3));
            await _extractDataFromWebView();
          },
        ),
      )
      ..loadRequest(Uri.parse(_panchangService.widgetUrl));

    _panchangService.setWebViewController(_webViewController!);

    return Stack(
      children: [
        WebViewWidget(controller: _webViewController!),
        if (_isLoading)
          Container(
            color: const Color(0xFF6A1B9A),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'पंचांग लोड गर्दै...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPanchangUI() {
    if (_isLoading) {
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4A148C),
              Color(0xFF6A1B9A),
              Color(0xFF8E24AA),
              Color(0xFFF3E5F5),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'पंचांग लोड गर्दै...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'कृपया प्रतीक्षा गर्नुहोस्',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_panchangData == null) {
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4A148C),
              Color(0xFF6A1B9A),
              Color(0xFF8E24AA),
              Color(0xFFF3E5F5),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: const Icon(
                        Icons.error_outline,
                        color: Colors.white,
                        size: 48,
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'पंचांग डाटा लोड गर्न सकिएन',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'इन्टरनेट जडान जाँच गर्नुहोस्',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => _loadPanchangData(forceRefresh: true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: const Color(0xFF6A1B9A),
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        elevation: 4,
                      ),
                      icon: const Icon(Icons.refresh),
                      label: const Text(
                        'पुनः प्रयास गर्नुहोस्',
                        style: TextStyle(
                          fontFamily: 'NotoSansDevanagari',
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF4A148C),
            Color(0xFF6A1B9A),
            Color(0xFF8E24AA),
            Color(0xFFF3E5F5),
          ],
          stops: [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Column(
        children: [
          // Top gap and header
          const SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
                const Expanded(
                  child: Text(
                    'नेपाली पंचांग',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: () => _loadPanchangData(forceRefresh: true),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          
          // Panchang content - Simple single frame
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: _buildSimplePanchangCard(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimplePanchangCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [Colors.white, Color(0xFFF8F9FA)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with date
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    color: Color(0xFF6A1B9A),
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _panchangData!.dateInfo.nepali,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF6A1B9A),
                      fontFamily: 'NotoSansDevanagari',
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _panchangData!.dateInfo.english,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _panchangData!.dateInfo.nepalSamvat,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 16),

            // All information in simple rows
            _buildSimpleRow('सूर्य', '${_panchangData!.sunMoon.sunrise} - ${_panchangData!.sunMoon.sunset}', '☀️'),
            _buildSimpleRow('चन्द्र', '${_panchangData!.sunMoon.moonrise} - ${_panchangData!.sunMoon.moonset}', '🌙'),

            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 12),

            _buildSimpleRow('तिथि', '${_panchangData!.tithi.current} (${_panchangData!.tithi.endTime} सम्म)', '🌗'),
            _buildSimpleRow('पक्ष', _panchangData!.paksha, '🌙'),

            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 12),

            _buildSimpleRow('नक्षत्र', '${_panchangData!.nakshatra.current} (${_panchangData!.nakshatra.endTime} सम्म)', '⭐'),
            _buildSimpleRow('योग', '${_panchangData!.yoga.current} (${_panchangData!.yoga.endTime} सम्म)', '🔮'),

            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 12),

            // Karana information
            if (_panchangData!.karana.isNotEmpty) ...[
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('🔸', style: TextStyle(fontSize: 16)),
                  const SizedBox(width: 8),
                  const Text(
                    'करण:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _panchangData!.karana.map((karana) =>
                        Text(
                          '${karana.current} (${karana.endTime} सम्म)',
                          style: const TextStyle(
                            fontSize: 13,
                            fontFamily: 'NotoSansDevanagari',
                          ),
                        ),
                      ).toList(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 12),
            ],

            _buildSimpleRow('चन्द्र राशि', _panchangData!.moonSign, '♈'),
            _buildSimpleRow('दिनमान', _panchangData!.dayDuration, '⏰'),
            _buildSimpleRow('ऋतु', _panchangData!.season, '🌦️'),
            _buildSimpleRow('आयान', _panchangData!.ayana, '🧭'),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleRow(String label, String value, String emoji) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
                fontFamily: 'NotoSansDevanagari',
              ),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }





}
