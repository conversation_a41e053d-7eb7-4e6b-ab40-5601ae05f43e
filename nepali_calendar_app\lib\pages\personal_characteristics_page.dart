import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class PersonalCharacteristicsPage extends StatefulWidget {
  final UserData user;

  const PersonalCharacteristicsPage({Key? key, required this.user}) : super(key: key);

  @override
  State<PersonalCharacteristicsPage> createState() => _PersonalCharacteristicsPageState();
}

class _PersonalCharacteristicsPageState extends State<PersonalCharacteristicsPage> {
  bool _isLoading = false;
  dynamic _personalCharacteristicsResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchPersonalCharacteristics();
  }

  UserData get user => widget.user;

  Future<void> _fetchPersonalCharacteristics() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getPersonalCharacteristics(user);
      
      setState(() {
        _personalCharacteristicsResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'व्यक्तिगत विशेषताहरू',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'व्यक्तिगत विशेषताहरू विश्लेषण गर्दै...',
                  featureName: 'व्यक्तिगत विशेषताहरू',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchPersonalCharacteristics,
                  featureName: 'व्यक्तिगत विशेषताहरू',
                ),
                if (_personalCharacteristicsResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'व्यक्तिगत विशेषताहरू लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchPersonalCharacteristics,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.psychology,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'व्यक्तित्व विश्लेषण',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_personalCharacteristicsResult != null)
            _buildPersonalCharacteristicsContent(),
        ],
      ),
    );
  }

  Widget _buildPersonalCharacteristicsContent() {
    // Handle List format from API (house-wise predictions)
    if (_personalCharacteristicsResult is List) {
      final houseList = _personalCharacteristicsResult as List;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display each house prediction
          ...houseList.asMap().entries.map((entry) {
            final index = entry.key;
            final houseData = entry.value as Map<String, dynamic>;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // House Header
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2E7D32),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${houseData['current_house'] ?? (index + 1)} औं घर',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          houseData['current_zodiac'] ?? '',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // House Details
                  ...houseData.entries.map((houseEntry) {
                    if (houseEntry.key == 'current_house') return const SizedBox.shrink();

                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF4CAF50).withOpacity(0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getHouseFieldIcon(houseEntry.key),
                                color: const Color(0xFF2E7D32),
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _formatHouseKey(houseEntry.key),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF2E7D32),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          _buildHouseValueWidget(houseEntry.value),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ),
            );
          }).toList(),
        ],
      );
    }

    // Fallback for Map format (if API changes)
    else if (_personalCharacteristicsResult is Map<String, dynamic>) {
      final data = _personalCharacteristicsResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all key-value pairs from the result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getCharacteristicIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _formatKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else if (_personalCharacteristicsResult is String) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFF1F8E9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.description,
                  color: Color(0xFF2E7D32),
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'व्यक्तिगत विशेषताहरू',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _personalCharacteristicsResult.toString(),
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                height: 1.4,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_personalCharacteristicsResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  // Helper methods for house-wise data formatting
  IconData _getHouseFieldIcon(String key) {
    switch (key.toLowerCase()) {
      case 'verbal_location':
        return Icons.location_on;
      case 'current_zodiac':
        return Icons.star;
      case 'lord_of_zodiac':
        return Icons.account_balance;
      case 'lord_zodiac_location':
        return Icons.place;
      case 'lord_house_location':
        return Icons.home;
      case 'personalised_prediction':
        return Icons.auto_awesome;
      case 'lord_strength':
        return Icons.fitness_center;
      default:
        return Icons.info;
    }
  }

  String _formatHouseKey(String key) {
    switch (key.toLowerCase()) {
      case 'verbal_location':
        return 'स्थान विवरण';
      case 'current_zodiac':
        return 'वर्तमान राशि';
      case 'lord_of_zodiac':
        return 'राशि स्वामी';
      case 'lord_zodiac_location':
        return 'स्वामी राशि स्थान';
      case 'lord_house_location':
        return 'स्वामी घर स्थान';
      case 'personalised_prediction':
        return 'व्यक्तिगत भविष्यवाणी';
      case 'lord_strength':
        return 'स्वामी शक्ति';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  Widget _buildHouseValueWidget(dynamic value) {
    if (value == null) return const SizedBox.shrink();

    String displayValue = value.toString();

    // Special formatting for strength
    if (displayValue.toLowerCase() == 'exalted') {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: const Text(
          'उच्च (Exalted)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.green,
          ),
        ),
      );
    } else if (displayValue.toLowerCase() == 'debilitated') {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: const Text(
          'नीच (Debilitated)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
      );
    } else if (displayValue.toLowerCase() == 'neutral') {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: const Text(
          'तटस्थ (Neutral)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.orange,
          ),
        ),
      );
    }

    return Text(
      displayValue,
      style: const TextStyle(
        fontSize: 15,
        color: Colors.black87,
        height: 1.4,
      ),
    );
  }

  IconData _getCharacteristicIcon(String key) {
    switch (key.toLowerCase()) {
      case 'personality':
      case 'character':
        return Icons.person;
      case 'behavior':
      case 'nature':
        return Icons.psychology;
      case 'strengths':
        return Icons.thumb_up;
      case 'weaknesses':
        return Icons.thumb_down;
      case 'career':
      case 'profession':
        return Icons.work;
      case 'health':
        return Icons.health_and_safety;
      case 'relationships':
      case 'marriage':
        return Icons.favorite;
      case 'education':
        return Icons.school;
      case 'spiritual':
        return Icons.self_improvement;
      case 'communication':
        return Icons.chat;
      case 'leadership':
        return Icons.group;
      case 'creativity':
        return Icons.brush;
      case 'intelligence':
        return Icons.lightbulb;
      case 'emotions':
        return Icons.sentiment_satisfied;
      case 'social':
        return Icons.people;
      case 'financial':
        return Icons.monetization_on;
      case 'family':
        return Icons.family_restroom;
      case 'travel':
        return Icons.flight;
      case 'hobbies':
        return Icons.sports_esports;
      default:
        return Icons.info;
    }
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'personality':
        return 'व्यक्तित्व';
      case 'character':
        return 'चरित्र';
      case 'behavior':
        return 'व्यवहार';
      case 'nature':
        return 'प्रकृति';
      case 'strengths':
        return 'शक्तिहरू';
      case 'weaknesses':
        return 'कमजोरीहरू';
      case 'career':
        return 'करियर';
      case 'profession':
        return 'व्यवसाय';
      case 'health':
        return 'स्वास्थ्य';
      case 'relationships':
        return 'सम्बन्धहरू';
      case 'marriage':
        return 'विवाह';
      case 'education':
        return 'शिक्षा';
      case 'spiritual':
        return 'आध्यात्मिक';
      case 'communication':
        return 'सञ्चार';
      case 'leadership':
        return 'नेतृत्व';
      case 'creativity':
        return 'रचनात्मकता';
      case 'intelligence':
        return 'बुद्धिमत्ता';
      case 'emotions':
        return 'भावनाहरू';
      case 'social':
        return 'सामाजिक';
      case 'financial':
        return 'आर्थिक';
      case 'family':
        return 'पारिवारिक';
      case 'travel':
        return 'यात्रा';
      case 'hobbies':
        return 'शौकहरू';
      case 'temperament':
        return 'स्वभाव';
      case 'attitude':
        return 'मनोवृत्ति';
      case 'approach':
        return 'दृष्टिकोण';
      case 'lifestyle':
        return 'जीवनशैली';
      case 'preferences':
        return 'प्राथमिकताहरू';
      case 'tendencies':
        return 'प्रवृत्तिहरू';
      case 'inclinations':
        return 'झुकावहरू';
      case 'compatibility':
        return 'मेल';
      case 'challenges':
        return 'चुनौतीहरू';
      case 'opportunities':
        return 'अवसरहरू';
      case 'potential':
        return 'सम्भावना';
      case 'talents':
        return 'प्रतिभाहरू';
      case 'skills':
        return 'सीपहरू';
      case 'abilities':
        return 'क्षमताहरू';
      case 'characteristics':
        return 'विशेषताहरू';
      case 'traits':
        return 'गुणहरू';
      case 'qualities':
        return 'गुणस्तरहरू';
      case 'description':
        return 'विवरण';
      case 'analysis':
        return 'विश्लेषण';
      case 'overview':
        return 'सिंहावलोकन';
      case 'summary':
        return 'सारांश';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
