import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class PlanetDetailsPage extends StatefulWidget {
  final UserData user;

  const PlanetDetailsPage({Key? key, required this.user}) : super(key: key);

  @override
  State<PlanetDetailsPage> createState() => _PlanetDetailsPageState();
}

class _PlanetDetailsPageState extends State<PlanetDetailsPage> {
  bool _isLoading = false;
  dynamic _planetDetailsResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchPlanetDetails();
  }

  UserData get user => widget.user;

  Future<void> _fetchPlanetDetails() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getPlanetDetails(user);
      
      setState(() {
        _planetDetailsResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'ग्रह विवरण',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'ग्रह विवरण विश्लेषण गर्दै...',
                  featureName: 'ग्रह विवरण',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchPlanetDetails,
                  featureName: 'ग्रह विवरण',
                ),
                if (_planetDetailsResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.public,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'ग्रह विवरण लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchPlanetDetails,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.public,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'ग्रह विश्लेषण',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_planetDetailsResult != null)
            _buildPlanetDetailsContent(),
        ],
      ),
    );
  }

  Widget _buildPlanetDetailsContent() {
    if (_planetDetailsResult is Map<String, dynamic>) {
      final data = _planetDetailsResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Planets Section
          _buildPlanetsSection(data),

          const SizedBox(height: 20),

          // Birth Information Section
          _buildBirthInfoSection(data),

          const SizedBox(height: 20),

          // Lucky Information Section
          _buildLuckyInfoSection(data),

          const SizedBox(height: 20),

          // Panchang Section
          if (data['panchang'] != null)
            _buildPanchangSection(data['panchang']),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_planetDetailsResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildPlanetsSection(Map<String, dynamic> data) {
    final planets = <Map<String, dynamic>>[];

    // Extract planet data (numbered keys 0-9)
    for (int i = 0; i <= 9; i++) {
      if (data[i.toString()] != null) {
        planets.add(data[i.toString()]);
      }
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFF1976D2).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ग्रह स्थितिहरू',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1976D2),
            ),
          ),
          const SizedBox(height: 16),

          // Grid layout for planets
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: planets.length,
            itemBuilder: (context, index) {
              final planet = planets[index];
              return _buildPlanetCard(planet, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetCard(Map<String, dynamic> planet, int index) {
    final planetName = _translatePlanet(planet['full_name']?.toString() ?? '');
    final zodiac = _translateZodiac(planet['zodiac']?.toString() ?? '');
    final house = planet['house']?.toString() ?? '';
    final nakshatra = _translateNakshatra(planet['nakshatra']?.toString() ?? '');
    final degree = planet['local_degree']?.toStringAsFixed(2) ?? '';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getPlanetColor(index).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPlanetColor(index).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Planet name with icon
          Row(
            children: [
              Icon(
                _getPlanetIcon(planet['full_name']?.toString() ?? ''),
                color: _getPlanetColor(index),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  planetName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _getPlanetColor(index),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Planet details
          Text(
            'राशि: $zodiac',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1565C0),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'भाव: $house',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1565C0),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'नक्षत्र: $nakshatra',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1565C0),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'डिग्री: $degree°',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1565C0),
              fontWeight: FontWeight.w500,
            ),
          ),

          // Retrograde indicator
          if (planet['retro'] == true)
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'वक्री',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBirthInfoSection(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF1F8E9),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'जन्म जानकारी',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 16),

          if (data['rasi'] != null)
            _buildDetailRow('राशि', _translateZodiac(data['rasi'].toString())),
          if (data['nakshatra'] != null)
            _buildDetailRow('नक्षत्र', _translateNakshatra(data['nakshatra'].toString())),
          if (data['nakshatra_pada'] != null)
            _buildDetailRow('नक्षत्र पद', data['nakshatra_pada'].toString()),
          if (data['birth_dasa'] != null)
            _buildDetailRow('जन्म दशा', data['birth_dasa'].toString()),
          if (data['current_dasa'] != null)
            _buildDetailRow('वर्तमान दशा', data['current_dasa'].toString()),
        ],
      ),
    );
  }

  Widget _buildLuckyInfoSection(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF3E0),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFFFF9800).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'भाग्यशाली तत्वहरू',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFFE65100),
            ),
          ),
          const SizedBox(height: 16),

          if (data['lucky_gem'] != null)
            _buildLuckyItem('रत्न', (data['lucky_gem'] as List).join(', '), Icons.diamond),
          if (data['lucky_num'] != null)
            _buildLuckyItem('अंक', (data['lucky_num'] as List).join(', '), Icons.looks_one),
          if (data['lucky_colors'] != null)
            _buildLuckyItem('रंग', (data['lucky_colors'] as List).join(', '), Icons.palette),
          if (data['lucky_letters'] != null)
            _buildLuckyItem('अक्षर', (data['lucky_letters'] as List).join(', '), Icons.text_fields),
          if (data['lucky_name_start'] != null)
            _buildLuckyItem('नाम सुरुवात', (data['lucky_name_start'] as List).join(', '), Icons.abc),
        ],
      ),
    );
  }

  Widget _buildPanchangSection(Map<String, dynamic> panchang) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF3E5F5),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFF9C27B0).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'पञ्चाङ्ग विवरण',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF7B1FA2),
            ),
          ),
          const SizedBox(height: 16),

          if (panchang['day_of_birth'] != null)
            _buildDetailRow('जन्म दिन', _translateDay(panchang['day_of_birth'].toString())),
          if (panchang['day_lord'] != null)
            _buildDetailRow('दिन स्वामी', _translatePlanet(panchang['day_lord'].toString())),
          if (panchang['hora_lord'] != null)
            _buildDetailRow('होरा स्वामी', _translatePlanet(panchang['hora_lord'].toString())),
          if (panchang['tithi'] != null)
            _buildDetailRow('तिथि', panchang['tithi'].toString()),
          if (panchang['karana'] != null)
            _buildDetailRow('करण', panchang['karana'].toString()),
          if (panchang['yoga'] != null)
            _buildDetailRow('योग', panchang['yoga'].toString()),
          if (panchang['sunrise_at_birth'] != null)
            _buildDetailRow('सूर्योदय', panchang['sunrise_at_birth'].toString()),
          if (panchang['sunset_at_birth'] != null)
            _buildDetailRow('सूर्यास्त', panchang['sunset_at_birth'].toString()),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLuckyItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: const Color(0xFFE65100), size: 20),
          const SizedBox(width: 12),
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFFE65100),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFFBF360C),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPlanetColor(int index) {
    final colors = [
      const Color(0xFFE53935), // Red
      const Color(0xFFFF9800), // Orange
      const Color(0xFF2196F3), // Blue
      const Color(0xFFE91E63), // Pink
      const Color(0xFF4CAF50), // Green
      const Color(0xFF9C27B0), // Purple
      const Color(0xFF795548), // Brown
      const Color(0xFF607D8B), // Blue Grey
      const Color(0xFF3F51B5), // Indigo
      const Color(0xFF00BCD4), // Cyan
    ];
    return colors[index % colors.length];
  }

  IconData _getPlanetIcon(String planetName) {
    switch (planetName.toLowerCase()) {
      case 'sun':
        return Icons.wb_sunny;
      case 'moon':
        return Icons.nightlight_round;
      case 'mars':
        return Icons.fitness_center;
      case 'mercury':
        return Icons.psychology;
      case 'jupiter':
        return Icons.school;
      case 'venus':
        return Icons.favorite;
      case 'saturn':
        return Icons.schedule;
      case 'rahu':
        return Icons.arrow_upward;
      case 'ketu':
        return Icons.arrow_downward;
      case 'ascendant':
        return Icons.home;
      default:
        return Icons.circle;
    }
  }

  String _translatePlanet(String planet) {
    switch (planet.toLowerCase()) {
      case 'sun':
        return 'सूर्य';
      case 'moon':
        return 'चन्द्र';
      case 'mars':
        return 'मंगल';
      case 'mercury':
        return 'बुध';
      case 'jupiter':
        return 'बृहस्पति';
      case 'venus':
        return 'शुक्र';
      case 'saturn':
        return 'शनि';
      case 'rahu':
        return 'राहु';
      case 'ketu':
        return 'केतु';
      case 'ascendant':
        return 'लग्न';
      default:
        return planet;
    }
  }

  String _translateZodiac(String zodiac) {
    switch (zodiac.toLowerCase()) {
      case 'aries':
        return 'मेष';
      case 'taurus':
        return 'वृषभ';
      case 'gemini':
        return 'मिथुन';
      case 'cancer':
        return 'कर्कट';
      case 'leo':
        return 'सिंह';
      case 'virgo':
        return 'कन्या';
      case 'libra':
        return 'तुला';
      case 'scorpio':
        return 'वृश्चिक';
      case 'sagittarius':
        return 'धनु';
      case 'capricorn':
        return 'मकर';
      case 'aquarius':
        return 'कुम्भ';
      case 'pisces':
        return 'मीन';
      default:
        return zodiac;
    }
  }

  String _translateNakshatra(String nakshatra) {
    switch (nakshatra.toLowerCase()) {
      case 'ashvini':
        return 'अश्विनी';
      case 'bharani':
        return 'भरणी';
      case 'krittika':
        return 'कृत्तिका';
      case 'rohini':
        return 'रोहिणी';
      case 'mrigashira':
        return 'मृगशिरा';
      case 'ardra':
        return 'आर्द्रा';
      case 'punarvasu':
        return 'पुनर्वसु';
      case 'pushya':
        return 'पुष्य';
      case 'ashlesha':
        return 'आश्लेषा';
      case 'magha':
        return 'मघा';
      case 'purva phalguni':
        return 'पूर्व फाल्गुनी';
      case 'uttara phalguni':
        return 'उत्तर फाल्गुनी';
      case 'hasta':
        return 'हस्त';
      case 'chitra':
        return 'चित्रा';
      case 'swati':
        return 'स्वाति';
      case 'vishakha':
        return 'विशाखा';
      case 'anuradha':
        return 'अनुराधा';
      case 'jyeshtha':
        return 'ज्येष्ठा';
      case 'mula':
        return 'मूल';
      case 'purva ashadha':
        return 'पूर्व आषाढा';
      case 'uttara ashadha':
        return 'उत्तर आषाढा';
      case 'sravana':
        return 'श्रवण';
      case 'dhanista':
        return 'धनिष्ठा';
      case 'shatabhisha':
        return 'शतभिषा';
      case 'purva bhadrapada':
        return 'पूर्व भाद्रपद';
      case 'uttara bhadrapada':
        return 'उत्तर भाद्रपद';
      case 'revati':
        return 'रेवती';
      default:
        return nakshatra;
    }
  }

  String _translateDay(String day) {
    switch (day.toLowerCase()) {
      case 'sunday':
        return 'आइतबार';
      case 'monday':
        return 'सोमबार';
      case 'tuesday':
        return 'मंगलबार';
      case 'wednesday':
        return 'बुधबार';
      case 'thursday':
        return 'बिहिबार';
      case 'friday':
        return 'शुक्रबार';
      case 'saturday':
        return 'शनिबार';
      default:
        return day;
    }
  }
}
