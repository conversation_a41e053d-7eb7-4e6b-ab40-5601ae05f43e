import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class PlanetsInHousesPage extends StatefulWidget {
  final UserData user;

  const PlanetsInHousesPage({Key? key, required this.user}) : super(key: key);

  @override
  State<PlanetsInHousesPage> createState() => _PlanetsInHousesPageState();
}

class _PlanetsInHousesPageState extends State<PlanetsInHousesPage> {
  bool _isLoading = false;
  dynamic _planetsInHousesResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchPlanetsInHouses();
  }

  UserData get user => widget.user;

  Future<void> _fetchPlanetsInHouses() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getPlanetsInHouses(user);
      
      setState(() {
        _planetsInHousesResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'भावमा ग्रह स्थिति',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_planetsInHousesResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.home_work,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'जन्म मिति: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    Text(
                      'जन्म समय: ${user.birthTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'भावमा ग्रह स्थिति लोड हुँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchPlanetsInHouses,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.home_work,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'भावमा ग्रह स्थिति',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_planetsInHousesResult != null)
            _buildPlanetsInHousesContent(),
        ],
      ),
    );
  }

  Widget _buildPlanetsInHousesContent() {
    if (_planetsInHousesResult is Map<String, dynamic>) {
      final data = _planetsInHousesResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all key-value pairs from the result
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getHouseIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _formatHouseKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else if (_planetsInHousesResult is String) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFF1F8E9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: const Color(0xFF4CAF50).withOpacity(0.3),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.home_work,
                  color: Color(0xFF2E7D32),
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  'भावमा ग्रह स्थिति',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _planetsInHousesResult.toString(),
              style: const TextStyle(
                fontSize: 18,
                color: Color(0xFF1B5E20),
                height: 1.4,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_planetsInHousesResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_formatKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: _buildValueWidget(entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  IconData _getHouseIcon(String key) {
    switch (key.toLowerCase()) {
      case 'house1':
      case '1':
        return Icons.person;
      case 'house2':
      case '2':
        return Icons.monetization_on;
      case 'house3':
      case '3':
        return Icons.group;
      case 'house4':
      case '4':
        return Icons.home;
      case 'house5':
      case '5':
        return Icons.child_care;
      case 'house6':
      case '6':
        return Icons.health_and_safety;
      case 'house7':
      case '7':
        return Icons.favorite;
      case 'house8':
      case '8':
        return Icons.transform;
      case 'house9':
      case '9':
        return Icons.school;
      case 'house10':
      case '10':
        return Icons.work;
      case 'house11':
      case '11':
        return Icons.trending_up;
      case 'house12':
      case '12':
        return Icons.self_improvement;
      default:
        return Icons.home_work;
    }
  }

  String _formatHouseKey(String key) {
    // Convert house keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'house1':
      case '1':
        return 'पहिलो भाव (तनु भाव)';
      case 'house2':
      case '2':
        return 'दोस्रो भाव (धन भाव)';
      case 'house3':
      case '3':
        return 'तेस्रो भाव (भ्रातृ भाव)';
      case 'house4':
      case '4':
        return 'चौथो भाव (मातृ भाव)';
      case 'house5':
      case '5':
        return 'पाँचौं भाव (पुत्र भाव)';
      case 'house6':
      case '6':
        return 'छैटौं भाव (शत्रु भाव)';
      case 'house7':
      case '7':
        return 'सातौं भाव (कलत्र भाव)';
      case 'house8':
      case '8':
        return 'आठौं भाव (आयु भाव)';
      case 'house9':
      case '9':
        return 'नवौं भाव (भाग्य भाव)';
      case 'house10':
      case '10':
        return 'दशौं भाव (कर्म भाव)';
      case 'house11':
      case '11':
        return 'एघारौं भाव (लाभ भाव)';
      case 'house12':
      case '12':
        return 'बाह्रौं भाव (व्यय भाव)';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'planet':
        return 'ग्रह';
      case 'planets':
        return 'ग्रहहरू';
      case 'house':
        return 'भाव';
      case 'houses':
        return 'भावहरू';
      case 'position':
        return 'स्थिति';
      case 'zodiac':
        return 'राशि';
      case 'sign':
        return 'राशि';
      case 'degree':
        return 'डिग्री';
      case 'lord':
        return 'स्वामी';
      case 'strength':
        return 'शक्ति';
      case 'weakness':
        return 'कमजोरी';
      case 'effects':
        return 'प्रभावहरू';
      case 'characteristics':
        return 'विशेषताहरू';
      case 'description':
        return 'विवरण';
      case 'meaning':
        return 'अर्थ';
      case 'significance':
        return 'महत्व';
      case 'influence':
        return 'प्रभाव';
      case 'nature':
        return 'प्रकृति';
      case 'quality':
        return 'गुण';
      case 'element':
        return 'तत्व';
      case 'modality':
        return 'गुणवत्ता';
      case 'ruler':
        return 'शासक';
      case 'exaltation':
        return 'उच्च';
      case 'debilitation':
        return 'नीच';
      case 'moolatrikona':
        return 'मूलत्रिकोण';
      case 'own_sign':
        return 'स्वराशि';
      case 'friendly':
        return 'मित्र';
      case 'enemy':
        return 'शत्रु';
      case 'neutral':
        return 'सम';
      case 'benefic':
        return 'शुभ';
      case 'malefic':
        return 'अशुभ';
      case 'retrograde':
        return 'वक्री';
      case 'combust':
        return 'अस्त';
      case 'aspects':
        return 'दृष्टि';
      case 'conjunctions':
        return 'युति';
      case 'oppositions':
        return 'विरोध';
      case 'trines':
        return 'त्रिकोण';
      case 'squares':
        return 'चतुष्कोण';
      case 'sextiles':
        return 'षष्ठकोण';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
