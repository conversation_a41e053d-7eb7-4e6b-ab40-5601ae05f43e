import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/user_data_service.dart';

class RajjuVedhaMatchPage extends StatefulWidget {
  const RajjuVedhaMatchPage({Key? key}) : super(key: key);

  @override
  State<RajjuVedhaMatchPage> createState() => _RajjuVedhaMatchPageState();
}

class _RajjuVedhaMatchPageState extends State<RajjuVedhaMatchPage> {
  bool _isLoading = false;
  dynamic _rajjuVedhaResult;
  String? _error;
  UserData? _selectedBoy;
  UserData? _selectedGirl;
  List<UserData> _allUsers = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final users = await UserDataService.loadUserDataList();
      setState(() {
        _allUsers = users;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading users: $e';
      });
    }
  }

  Future<void> _fetchRajjuVedhaMatch() async {
    if (_selectedBoy == null || _selectedGirl == null) {
      setState(() {
        _error = 'कृपया केटा र केटी दुवै छान्नुहोस्';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getRajjuVedhaMatch(_selectedBoy!, _selectedGirl!);

      setState(() {
        _rajjuVedhaResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'रज्जु वेध मिलान',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildInfoCard(),
                const SizedBox(height: 12),
                _buildUserSelectionCard(),
                const SizedBox(height: 12),
                if (_selectedBoy != null && _selectedGirl != null)
                  _buildAnalyzeButton(),
                const SizedBox(height: 12),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_rajjuVedhaResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.link,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'रज्जु वेध मिलान',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'विशेष दोष विश्लेषण र मिलान',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelectionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'व्यक्ति छान्नुहोस्',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 12),

          // Boy and Girl Selection in Same Row
          Row(
            children: [
              // Boy Selection
              Expanded(
                child: _buildUserSelector(
                  title: 'केटा छान्नुहोस्',
                  icon: Icons.male,
                  selectedUser: _selectedBoy,
                  onUserSelected: (user) {
                    setState(() {
                      _selectedBoy = user;
                      _rajjuVedhaResult = null; // Clear previous results
                    });
                  },
                ),
              ),

              const SizedBox(width: 12),

              // Girl Selection
              Expanded(
                child: _buildUserSelector(
                  title: 'केटी छान्नुहोस्',
                  icon: Icons.female,
                  selectedUser: _selectedGirl,
                  onUserSelected: (user) {
                    setState(() {
                      _selectedGirl = user;
                      _rajjuVedhaResult = null; // Clear previous results
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelector({
    required String title,
    required IconData icon,
    required UserData? selectedUser,
    required Function(UserData?) onUserSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: const Color(0xFF2E7D32), size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFF4CAF50)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<UserData>(
              value: selectedUser,
              hint: Text(
                'छान्नुहोस्...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              isExpanded: true,
              items: _allUsers.map((user) {
                return DropdownMenuItem<UserData>(
                  value: user,
                  child: Text(
                    '${user.name} (${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day})',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1B5E20),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onUserSelected,
              dropdownColor: Colors.white,
              icon: const Icon(
                Icons.arrow_drop_down,
                color: Color(0xFF4CAF50),
              ),
            ),
          ),
        ),

        if (selectedUser != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF2E7D32), size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedUser.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B5E20),
                        ),
                      ),
                      Text(
                        'जन्म: ${selectedUser.birthDateBS.year}/${selectedUser.birthDateBS.month}/${selectedUser.birthDateBS.day} ${selectedUser.birthTime}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      Text(
                        'स्थान: ${selectedUser.district}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAnalyzeButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _fetchRajjuVedhaMatch,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 3,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.link, size: 24),
            const SizedBox(width: 12),
            Text(
              'रज्जु वेध विश्लेषण गर्नुहोस्',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'रज्जु वेध विश्लेषण गर्दै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'कृपया पर्खनुहोस्',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF388E3C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchRajjuVedhaMatch,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with couple info
          _buildCoupleHeader(),
          const SizedBox(height: 20),

          if (_rajjuVedhaResult != null)
            _buildRajjuVedhaContent(),
        ],
      ),
    );
  }

  Widget _buildCoupleHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE8F5E8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Boy info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.male, color: Color(0xFF2196F3), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedBoy?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedBoy?.birthDateBS.year}/${_selectedBoy?.birthDateBS.month}/${_selectedBoy?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // Link icon for Rajju Vedha
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.link,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Girl info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.female, color: Color(0xFFE91E63), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedGirl?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedGirl?.birthDateBS.year}/${_selectedGirl?.birthDateBS.month}/${_selectedGirl?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRajjuVedhaContent() {
    if (_rajjuVedhaResult is Map<String, dynamic>) {
      final data = _rajjuVedhaResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rajju Dosha Status
          if (data['is_rajju_dosha_present'] != null)
            _buildDoshaCard(
              'रज्जु दोष',
              data['is_rajju_dosha_present'] as bool,
              Icons.link,
              data['rajju_part']?.toString(),
            ),

          const SizedBox(height: 16),

          // Vedha Dosha Status
          if (data['is_vedha_dosha_present'] != null)
            _buildDoshaCard(
              'वेध दोष',
              data['is_vedha_dosha_present'] as bool,
              Icons.block,
              null,
            ),

          const SizedBox(height: 16),

          // Rajju Description
          if (data['rajju_description'] != null)
            _buildDescriptionCard(
              'रज्जु विवरण',
              data['rajju_description'].toString(),
              Icons.description,
            ),

          // Additional details
          ...data.entries.where((entry) =>
            entry.key != 'is_rajju_dosha_present' &&
            entry.key != 'is_vedha_dosha_present' &&
            entry.key != 'rajju_part' &&
            entry.key != 'rajju_description'
          ).map((entry) {
            return Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.info,
                        color: Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _translateKey(entry.key),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    entry.value.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_rajjuVedhaResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }
  Widget _buildDoshaCard(String title, bool isPresent, IconData icon, String? additionalInfo) {
    final color = isPresent ? Colors.red : const Color(0xFF4CAF50);
    final status = isPresent ? 'उपस्थित' : 'अनुपस्थित';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 32),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'स्थिति: $status',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          if (additionalInfo != null) ...[
            const SizedBox(height: 8),
            Text(
              'प्रकार: $additionalInfo',
              style: TextStyle(
                fontSize: 18,
                color: color,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDescriptionCard(String title, String description, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: const Color(0xFF1976D2).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF1976D2), size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1976D2),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF1565C0),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  String _translateKey(String key) {
    switch (key.toLowerCase()) {
      case 'is_rajju_dosha_present':
        return 'रज्जु दोष उपस्थिति';
      case 'is_vedha_dosha_present':
        return 'वेध दोष उपस्थिति';
      case 'rajju_part':
        return 'रज्जु भाग';
      case 'rajju_description':
        return 'रज्जु विवरण';
      case 'vedha_description':
        return 'वेध विवरण';
      case 'compatibility':
        return 'मिलान';
      case 'recommendation':
        return 'सिफारिस';
      case 'analysis':
        return 'विश्लेषण';
      case 'result':
        return 'परिणाम';
      case 'status':
        return 'स्थिति';
      case 'description':
        return 'विवरण';
      case 'present':
        return 'उपस्थित';
      case 'absent':
        return 'अनुपस्थित';
      case 'kanta':
        return 'कण्ठ';
      case 'kati':
        return 'कटि';
      case 'pada':
        return 'पाद';
      case 'shira':
        return 'शिर';
      case 'udara':
        return 'उदर';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
