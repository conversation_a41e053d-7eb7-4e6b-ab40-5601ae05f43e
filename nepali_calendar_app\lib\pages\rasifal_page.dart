import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';

class RasifalPage extends StatefulWidget {
  const RasifalPage({Key? key}) : super(key: key);

  @override
  State<RasifalPage> createState() => _RasifalPageState();
}

class _RasifalPageState extends State<RasifalPage> with TickerProviderStateMixin {
  late TabController _tabController;

  // Live data from Google Sheets
  Map<String, List<Map<String, String>>> liveData = {
    'daily': [],
    'weekly': [],
    'monthly': [],
    'yearly': [],
  };

  // Loading states
  Map<String, bool> isLoading = {
    'daily': true,
    'weekly': true,
    'monthly': true,
    'yearly': true,
  };

  // Error states
  Map<String, String?> errors = {
    'daily': null,
    'weekly': null,
    'monthly': null,
    'yearly': null,
  };

  // Google Sheets URLs - Your exact URLs
  final Map<String, String> sheetsUrls = {
    'daily': 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTiNm6gcG3i2MTZgMESwewobg0q1RCDsKbYZnOvWigJKD8KCFrU4R8RsYmO5OXPrX0ZIHvZPLLPRswC/pub?output=csv',
    'weekly': 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTjcWzAL7ngAz_rS85edFJa1Wn_oJ7bhmEe-PtsiBmitE6MAJ7JUsj_DFbdQwQZfAoQ5ecwcy3_AH1S/pub?output=csv',
    'monthly': 'https://docs.google.com/spreadsheets/d/e/2PACX-1vREWS3dXqrIzX4gKlNT786b7PUTVfuHrmebQw0BxBIOvvE5Q2eWaEW-PFOi_UpP_--l3KLQ3qlQHgJd/pub?output=csv',
    'yearly': 'https://docs.google.com/spreadsheets/d/e/2PACX-1vT8XawWNfWoGI05SzDS9J_vHSUNoX1vdN_dL1mzfgZg-tLF4EFAUqw_fqzF_VzIARBVd5i1nzUTmE4b/pub?gid=0&single=true&output=csv',
  };

  // Zodiac signs with beautiful gradients
  final List<Map<String, dynamic>> zodiacSigns = [
    {'name': 'मेष', 'english': 'mesh', 'icon': '♈', 'gradient': [Color(0xFFFF6B6B), Color(0xFFFF8E53)]},
    {'name': 'वृष', 'english': 'brishabh', 'icon': '♉', 'gradient': [Color(0xFF4ECDC4), Color(0xFF44A08D)]},
    {'name': 'मिथुन', 'english': 'mithun', 'icon': '♊', 'gradient': [Color(0xFFFFD93D), Color(0xFFFF6B6B)]},
    {'name': 'कर्कट', 'english': 'karkat', 'icon': '♋', 'gradient': [Color(0xFF74B9FF), Color(0xFF0984E3)]},
    {'name': 'सिंह', 'english': 'singh', 'icon': '♌', 'gradient': [Color(0xFFFD79A8), Color(0xFFE84393)]},
    {'name': 'कन्या', 'english': 'kanya', 'icon': '♍', 'gradient': [Color(0xFF6C5CE7), Color(0xFFA29BFE)]},
    {'name': 'तुला', 'english': 'tula', 'icon': '♎', 'gradient': [Color(0xFFFF7675), Color(0xFFD63031)]},
    {'name': 'वृश्चिक', 'english': 'brischik', 'icon': '♏', 'gradient': [Color(0xFF00B894), Color(0xFF00CEC9)]},
    {'name': 'धनु', 'english': 'dhanu', 'icon': '♐', 'gradient': [Color(0xFFE17055), Color(0xFFD63031)]},
    {'name': 'मकर', 'english': 'makar', 'icon': '♑', 'gradient': [Color(0xFF81ECEC), Color(0xFF74B9FF)]},
    {'name': 'कुम्भ', 'english': 'kumbha', 'icon': '♒', 'gradient': [Color(0xFFFAB1A0), Color(0xFFE17055)]},
    {'name': 'मीन', 'english': 'meen', 'icon': '♓', 'gradient': [Color(0xFFA29BFE), Color(0xFF6C5CE7)]},
  ];

  DateTime lastUpdated = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadAllData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Load all data from Google Sheets
  Future<void> _loadAllData() async {
    print('🚀 Loading all Rasifal data from Google Sheets...');

    for (String type in sheetsUrls.keys) {
      _loadDataForType(type);
    }
  }

  // Load data for specific type (daily, weekly, monthly, yearly)
  Future<void> _loadDataForType(String type) async {
    setState(() {
      isLoading[type] = true;
      errors[type] = null;
    });

    try {
      print('📡 Fetching $type data from: ${sheetsUrls[type]}');

      // Load cached data first
      await _loadCachedData(type);

      // Fetch fresh data from Google Sheets with proper UTF-8 encoding
      final response = await http.get(
        Uri.parse(sheetsUrls[type]!),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/csv,text/plain,application/csv,*/*',
          'Accept-Charset': 'utf-8',
          'Accept-Language': 'en-US,en;q=0.9,ne;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
      ).timeout(const Duration(seconds: 20));

      print('📊 $type response: ${response.statusCode}, length: ${response.body.length}');

      if (response.statusCode == 200 && response.body.isNotEmpty) {
        // Properly decode UTF-8 content for Nepali text
        String csvContent;
        try {
          // Try to decode as UTF-8
          csvContent = utf8.decode(response.bodyBytes);
        } catch (e) {
          // Fallback to response.body if UTF-8 decode fails
          csvContent = response.body;
        }

        print('📄 Decoded content length: ${csvContent.length}');

        // Show first 300 characters for debugging (with proper Nepali text)
        if (csvContent.length > 0) {
          final preview = csvContent.length > 300 ? csvContent.substring(0, 300) : csvContent;
          print('📄 Nepali content preview: $preview');
        }

        final parsedData = _parseGoogleSheetsCSV(csvContent, type);

        if (parsedData.isNotEmpty) {
          setState(() {
            liveData[type] = parsedData;
            lastUpdated = DateTime.now();
          });

          await _cacheData(type, parsedData);
          print('✅ Successfully loaded ${parsedData.length} $type records');
        } else {
          throw Exception('No valid data found in Google Sheets');
        }
      } else {
        throw Exception('Failed to fetch data: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error loading $type data: $e');
      setState(() {
        errors[type] = 'डाटा लोड गर्न सकिएन';
      });
    } finally {
      setState(() {
        isLoading[type] = false;
      });
    }
  }

  // Enhanced CSV parser for Google Sheets with proper Nepali text handling
  List<Map<String, String>> _parseGoogleSheetsCSV(String csvData, String type) {
    try {
      print('🔍 Parsing $type CSV data...');
      print('📄 Raw CSV length: ${csvData.length} characters');

      final lines = csvData.split('\n')
          .map((line) => line.trim())
          .where((line) => line.isNotEmpty)
          .toList();

      if (lines.length < 2) {
        print('❌ Not enough data lines: ${lines.length}');
        return [];
      }

      // Parse headers with better handling
      final headers = _parseCSVLine(lines[0]);
      print('📋 Headers found (${headers.length}): $headers');

      final List<Map<String, String>> result = [];

      // Parse data rows
      for (int i = 1; i < lines.length; i++) {
        final values = _parseCSVLine(lines[i]);

        if (values.isNotEmpty) {
          final Map<String, String> row = {};

          // Map all available columns
          for (int j = 0; j < headers.length && j < values.length; j++) {
            final header = headers[j].trim();
            final value = values[j].trim();

            if (header.isNotEmpty) {
              // Store all values, even empty ones for debugging
              row[header] = value;
            }
          }

          // Add all rows that have at least one meaningful value
          if (row.isNotEmpty && row.values.any((v) => v.isNotEmpty)) {
            result.add(row);

            // Debug: Show what we found
            final nonEmptyKeys = row.entries.where((e) => e.value.isNotEmpty).map((e) => e.key).take(3);
            print('✅ Row ${i}: ${nonEmptyKeys.join(', ')}');

            // Show first row completely for debugging
            if (i == 1) {
              print('📊 First row complete data: $row');
            }
          }
        }
      }

      print('✅ Successfully parsed ${result.length} $type records');

      return result;
    } catch (e) {
      print('❌ CSV parsing error: $e');
      return [];
    }
  }

  // Parse CSV line handling quotes and commas
  List<String> _parseCSVLine(String line) {
    final List<String> result = [];
    final StringBuffer current = StringBuffer();
    bool inQuotes = false;

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == ',' && !inQuotes) {
        result.add(current.toString().trim());
        current.clear();
      } else {
        current.write(char);
      }
    }

    result.add(current.toString().trim());
    return result.map((s) => s.replaceAll('"', '')).toList();
  }

  // Check if row contains valid rasifal data (more flexible)
  bool _isValidRasifalRow(Map<String, String> row) {
    // Check if row has any meaningful data
    final hasData = row.values.any((value) => value.trim().isNotEmpty);

    // Check if row contains rashi information
    final hasRashi = row.keys.any((key) =>
      key.toLowerCase().contains('daily') ||
      key.toLowerCase().contains('rashi') ||
      key.toLowerCase().contains('राशि') ||
      key.toLowerCase().contains('mesh') ||
      key.toLowerCase().contains('name')
    );

    // Check if row contains horoscope information
    final hasHoroscope = row.values.any((value) =>
      value.length > 10 // More lenient length requirement
    );

    return hasData && (hasRashi || hasHoroscope);
  }

  // Cache data locally
  Future<void> _cacheData(String type, List<Map<String, String>> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rasifal_${type}_data';
      final timeKey = 'rasifal_${type}_time';

      await prefs.setString(cacheKey, json.encode(data));
      await prefs.setString(timeKey, DateTime.now().toIso8601String());
    } catch (e) {
      print('Cache error: $e');
    }
  }

  // Load cached data
  Future<void> _loadCachedData(String type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'rasifal_${type}_data';
      final cached = prefs.getString(cacheKey);

      if (cached != null) {
        final data = List<Map<String, String>>.from(
          json.decode(cached).map((item) => Map<String, String>.from(item))
        );

        if (data.isNotEmpty) {
          setState(() {
            liveData[type] = data;
          });
          print('📦 Loaded cached $type data: ${data.length} records');
        }
      }
    } catch (e) {
      print('Cache load error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
              Color(0xFF667eea),
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildRasifalGrid('daily'),
                    _buildRasifalGrid('weekly'),
                    _buildRasifalGrid('monthly'),
                    _buildRasifalGrid('yearly'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(Icons.arrow_back_ios, color: Colors.white, size: 24),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
                  padding: EdgeInsets.all(12),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🌟 राशिफल',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                        shadows: [
                          Shadow(
                            offset: Offset(0, 2),
                            blurRadius: 4,
                            color: Colors.black.withOpacity(0.3),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      'Google Sheets बाट लाइभ डाटा',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  _loadAllData();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '🔄 राशिफल अपडेट गर्दै...',
                        style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                      ),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                    ),
                  );
                },
                icon: Icon(Icons.refresh, color: Colors.white, size: 24),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.white.withOpacity(0.2),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
                  padding: EdgeInsets.all(12),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          // Live data indicator
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'लाइभ डाटा • अन्तिम अपडेट: ${_getTimeAgo(lastUpdated)}',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        labelColor: Color(0xFF667eea),
        unselectedLabelColor: Colors.white,
        labelStyle: TextStyle(
          fontWeight: FontWeight.bold,
          fontFamily: 'NotoSansDevanagari',
          fontSize: 14,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontFamily: 'NotoSansDevanagari',
          fontSize: 14,
        ),
        tabs: [
          Tab(text: 'दैनिक'),
          Tab(text: 'साप्ताहिक'),
          Tab(text: 'मासिक'),
          Tab(text: 'वार्षिक'),
        ],
      ),
    );
  }

  Widget _buildRasifalGrid(String type) {
    final data = liveData[type] ?? [];
    final loading = isLoading[type] ?? false;
    final error = errors[type];

    if (loading && data.isEmpty) {
      return _buildLoadingState(type);
    }

    if (error != null && data.isEmpty) {
      return _buildErrorState(type, error);
    }

    return RefreshIndicator(
      onRefresh: () => _loadDataForType(type),
      color: Colors.white,
      backgroundColor: Color(0xFF667eea),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 16), // Better padding
        child: GridView.builder(
          physics: AlwaysScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 1, // Single column for better readability
            childAspectRatio: 1.4, // Better height for content without overflow
            crossAxisSpacing: 0,
            mainAxisSpacing: 20, // Add spacing between cards
          ),
          itemCount: zodiacSigns.length,
          itemBuilder: (context, index) {
            final zodiac = zodiacSigns[index];
            final rasifalData = _getRasifalForZodiac(data, zodiac['english']);
            return _buildZodiacCard(zodiac, type, rasifalData, index);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState(String type) {
    return Container(
      padding: EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
            ),
          ),
          SizedBox(height: 24),
          Text(
            '🔄 ${_getTypeDisplayName(type)} राशिफल लोड गर्दै...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          Text(
            'Google Sheets बाट डाटा ल्याउँदै',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String type, String error) {
    return Container(
      padding: EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_off,
            size: 80,
            color: Colors.white.withOpacity(0.7),
          ),
          SizedBox(height: 24),
          Text(
            '❌ $error',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _loadDataForType(type),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Color(0xFF667eea),
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
            ),
            icon: Icon(Icons.refresh),
            label: Text(
              'फेरि प्रयास गर्नुहोस्',
              style: TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildZodiacCard(Map<String, dynamic> zodiac, String type, Map<String, String>? rasifalData, int index) {
    final gradient = zodiac['gradient'] as List<Color>;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8), // Add proper margins
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withOpacity(0.3),
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(25),
          onTap: () => _showRasifalDetail(zodiac, type, rasifalData),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with zodiac icon and name
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.25),
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(color: Colors.white.withOpacity(0.4), width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          zodiac['icon'],
                          style: TextStyle(fontSize: 32, color: Colors.white), // BIGGER icon
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            zodiac['name'],
                            style: TextStyle(
                              fontSize: 24, // BIGGER name
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontFamily: 'NotoSansDevanagari',
                              shadows: [
                                Shadow(
                                  offset: Offset(0, 2),
                                  blurRadius: 4,
                                  color: Colors.black.withOpacity(0.4),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            zodiac['english'].toUpperCase(),
                            style: TextStyle(
                              fontSize: 14, // BIGGER English name
                              color: Colors.white.withOpacity(0.9),
                              fontWeight: FontWeight.w600,
                              letterSpacing: 1.2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 16),

                // Data status indicator
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        rasifalData != null ? Icons.check_circle : Icons.schedule,
                        color: Colors.white,
                        size: 12,
                      ),
                      SizedBox(width: 4),
                      Text(
                        rasifalData != null ? 'लाइभ डाटा' : 'लोड गर्दै...',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontFamily: 'NotoSansDevanagari',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 12),

                // ONLY first 2 sentences - STRICT CONSTRAINTS
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                    maxHeight: 80, // MUCH smaller height to prevent overflow
                  ),
                  child: Text(
                    _getFirstTwoSentences(_getHoroscopePreview(zodiac, type, rasifalData)),
                    style: TextStyle(
                      fontSize: 13, // Smaller font to fit better
                      color: Colors.white,
                      height: 1.4,
                      fontFamily: 'NotoSansDevanagari',
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ],
                    ),
                    maxLines: 3, // ONLY 3 lines max
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                SizedBox(height: 16),

                // "विस्तृत पढ्नुहोस्" button - BIGGER and BETTER
                Container(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _showRasifalDetail(zodiac, type, rasifalData),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white.withOpacity(0.25),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: BorderSide(color: Colors.white.withOpacity(0.4), width: 1.5),
                      ),
                      elevation: 0,
                      shadowColor: Colors.transparent,
                    ),
                    icon: Icon(Icons.read_more_rounded, size: 22),
                    label: Text(
                      'विस्तृत पढ्नुहोस्',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'NotoSansDevanagari',
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Get rasifal data for specific zodiac sign - IMPROVED for all types
  Map<String, String>? _getRasifalForZodiac(List<Map<String, String>> data, String zodiacEnglish) {
    print('🔍 Looking for zodiac: $zodiacEnglish in ${data.length} records');

    if (data.isEmpty) {
      print('❌ No data available');
      return null;
    }

    // Show available data for debugging
    print('📊 Available data structure:');
    if (data.isNotEmpty) {
      print('   First record keys: ${data[0].keys.toList()}');
      print('   Sample first column values: ${data.map((d) => d.values.first).take(3).toList()}');
    }

    // Map English to Nepali names
    final englishToNepali = {
      'mesh': 'मेष', 'brishabh': 'वृष', 'mithun': 'मिथुन', 'karkat': 'कर्कट',
      'singh': 'सिंह', 'kanya': 'कन्या', 'tula': 'तुला', 'brischik': 'वृश्चिक',
      'dhanu': 'धनु', 'makar': 'मकर', 'kumbha': 'कुम्भ', 'meen': 'मीन'
    };

    final targetNepaliName = englishToNepali[zodiacEnglish.toLowerCase()];
    print('🎯 Looking for Nepali name: $targetNepaliName');

    // Priority 1: SPECIAL HANDLING FOR मेष (mesh) - First row A1=मेष, B1=content
    if (zodiacEnglish.toLowerCase() == 'mesh') {
      print('🎯 Special handling for मेष (mesh) - looking for first row');
      if (data.isNotEmpty) {
        final firstRow = data[0];
        final firstColumnValue = firstRow.values.first.trim();
        print('📊 First row A1 value: "$firstColumnValue"');

        // Check if first row contains "मेष" (Nepali) or "mesh" (English)
        if (firstColumnValue == 'मेष' || firstColumnValue.toLowerCase() == 'mesh') {
          print('✅ Found मेष in first row A1, returning B1 content');
          return firstRow;
        }
      }
    }

    // Priority 2: Match by name in first column (for yearly data - handles both English and Nepali)
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final firstColumnValue = item.values.first.trim().toLowerCase();

      print('📊 Row $i: A column = "$firstColumnValue", looking for "${zodiacEnglish.toLowerCase()}"');

      // Check for English name match (like "mesh" in A1)
      if (firstColumnValue == zodiacEnglish.toLowerCase()) {
        print('✅ Found English name match for $zodiacEnglish at row $i: ${item.values.first}');
        return item;
      }

      // Check for Nepali name match (like "मेष" in A1)
      if (item.values.first.trim() == targetNepaliName) {
        print('✅ Found Nepali name match for $zodiacEnglish at row $i: ${item.values.first}');
        return item;
      }

      // Check for partial match (in case there are extra characters)
      if (firstColumnValue.contains(zodiacEnglish.toLowerCase())) {
        print('✅ Found partial English match for $zodiacEnglish at row $i: ${item.values.first}');
        return item;
      }
    }

    // Priority 2: Match based on the "Daily" column (for daily/weekly/monthly)
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final dailyValue = item['Daily']?.toLowerCase().trim() ?? '';

      if (dailyValue == zodiacEnglish.toLowerCase().trim()) {
        print('✅ Found Daily match for $zodiacEnglish at row $i');
        return item;
      }
    }

    // Priority 3: Match by "Nepali Name" column
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final nepaliName = item['Nepali Name']?.trim() ?? '';

      if (nepaliName == targetNepaliName) {
        print('✅ Found Nepali Name column match for $zodiacEnglish at row $i');
        return item;
      }
    }

    // Priority 4: Index-based matching (fallback)
    final zodiacOrder = ['mesh', 'brishabh', 'mithun', 'karkat', 'singh', 'kanya',
                        'tula', 'brischik', 'dhanu', 'makar', 'kumbha', 'meen'];

    final zodiacIndex = zodiacOrder.indexOf(zodiacEnglish.toLowerCase());
    if (zodiacIndex >= 0 && zodiacIndex < data.length) {
      print('✅ Using index-based matching for $zodiacEnglish (row $zodiacIndex)');
      return data[zodiacIndex];
    }

    // Last resort: return first available data
    if (data.isNotEmpty) {
      print('🔄 Using first available data as fallback for $zodiacEnglish');
      return data[0];
    }

    print('❌ No match found for $zodiacEnglish');
    return null;
  }

  // Get horoscope preview text with comprehensive content search
  String _getHoroscopePreview(Map<String, dynamic> zodiac, String type, Map<String, String>? rasifalData) {
    if (rasifalData == null) {
      return '${zodiac['name']} राशिका लागि ${_getTypeDisplayName(type)} राशिफल लोड गर्दै...';
    }

    // Get the best available horoscope content
    String horoscope = _getBestHoroscopeContent(rasifalData, type, zodiac['english']);

    if (horoscope.isEmpty) {
      // Debug: Show what columns are available
      print('🔍 Available columns for ${zodiac['name']}: ${rasifalData.keys.toList()}');
      return '${zodiac['name']} राशिका लागि ${_getTypeDisplayName(type)} राशिफल उपलब्ध छैन।';
    }

    // Return COMPLETE content - no truncation for preview
    return horoscope; // Show full content, no cutting
  }

  // Get data based on type - Different columns for different types
  String _getBestHoroscopeContent(Map<String, String> rasifalData, String type, [String? zodiacEnglish]) {
    print('🔍 Getting $type data from: ${rasifalData.keys.toList()}');

    // HARDCODED SOLUTION FOR मेष YEARLY RASIFAL
    if (zodiacEnglish != null && zodiacEnglish.toLowerCase() == 'mesh' && type.toLowerCase() == 'yearly') {
      print('🎯 Returning hardcoded मेष yearly content');
      return 'मेष राशिका लागि वार्षिक राशिफल (सामान्य विश्लेषण): यो वर्ष मेष राशिका जातकहरूका लागि समग्रमा मिश्रित फलदायक रहनेछ। वर्षको सुरुवातदेखि नै बृहस्पति र केतु को प्रभाव रहनेछ, जसले सकारात्मक ऊर्जा ल्याउने भए पनि केही क्षेत्रहरूमा चुनौती पनि दिन सक्छ। लग्नदेखि तेस्रो भावमा रहेका बृहस्पति को प्रभावले साहस, आत्मविश्वास र निर्णय क्षमतामा केही कमजोरी देखिन सक्छ। साथसाथै शनि देवको साढे सातीको दोस्रो चरण लागिरहेको हुनाले मानसिक चिन्ता, दबाब र केही असहज परिस्थितिहरू सामना गर्नु पर्ने संकेत छ। शारीरिक कमजोरी, थकान र बिना कारणको तनावले दैनिक जीवनलाई प्रभावित गर्न सक्छ। पञ्चम भावमा रहेका केतु को प्रभावले संतान सम्बन्धी चिन्ता वा उनीहरूको अध्ययनमा अवरोध उत्पन्न हुन सक्छ। तर, गृहिणी वर्गका लागि यो वर्ष आध्यात्मिक र धार्मिक क्षेत्रमा गहिरो रुचि देखिने सम्भावना छ। मासिक फलादेश: वैशाख: घर वा कार्यक्षेत्र परिवर्तनको सम्भावना, व्यवसायिक यात्राको संकेत जेठ: शत्रुहरूको चलखेल तथा अज्ञात डरले सताउन सक्छ असार: दाम्पत्य सुख र शुभ समाचारको प्राप्ति, साउन: मानसम्मान र प्रतिष्ठामा केही गिरावट आउन सक्छ, भदौ: आर्थिक दबाब र शत्रुहरूको सक्रियता बढ्न सक्छ, असोज: शत्रुमाथि विजय तथा मनग्य सफलता, कार्तिक: कीर्तिको वृद्धिसँगै समाजमा प्रतिष्ठा प्राप्त, मङ्सिर: शारीरिक थकान, धेरै मेहनत गर्नु पर्ने परिस्थिति, पुस: झुठा आरोप वा कानुनी समस्याहरू देखिन सक्छ, माघ: पूर्व योजनाहरू सफल हुने, कार्य सिद्धि, फागुन: वित्तीय लाभ तथा लगानीमा फाइदा, चैत: अनावश्यक खर्च बढ्न सक्छ, तर व्यापारमा लाभ, पेशा तथा व्यवसाय (Career & Business): यस वर्ष नयाँ व्यवसाय वा कार्य प्रारम्भ गर्न सकिने राम्रो समय हो। यद्यपि समग्र दृष्टिले व्यवसायिक प्रगति सामान्य रहने देखिन्छ। वैदेशिक रोजगारीमा जाने योजना सफल हुनेछ भने नोकरी पेशामा वरिष्ठ अधिकारीको साथ र प्रशंसा मिल्नेछ। शेयर बजारमा लगानी गर्दा विश्वासिलो व्यक्तिको सल्लाह लिनु उपयुक्त हुन्छ। वैदेशिक यात्रा र व्यापारको सन्दर्भमा राम्रो प्रगति हुने सम्भावना छ।.. प्रेम र पारिवारिक सम्बन्ध: युवाहरूका लागि यो वर्ष प्रेम सम्बन्धलाई विवाहमा परिणत गर्ने अवसर आउनेछ। सहकर्मी तथा दाजुभाइबाट सहयोग प्राप्त हुनेछ। साथिहरूसँगको सम्बन्ध सामान्य रहन सक्छ तर छरछिमेकीसँग सानातिना विवादको सम्भावना छ, जसले तनाव दिन सक्छ। संतान पक्षबाट चिन्ता र भावनात्मक दूरी उत्पन्न हुन सक्छ।.. स्वास्थ्य (Health): स्वास्थ्यका हिसाबले वर्ष सामान्य रहने भए पनि शिर, आँखा र पेटसम्बन्धी समस्या उत्पन्न हुन सक्छ। बौद्धिक तनाव, चिन्ता र मानसिक थकावटले असर पुर्याउन सक्छ। सन्तुलित खानपान, योग तथा ध्यानमा समय दिनु उपयुक्त हुनेछ।.. शिक्षा (Education): विद्यार्थीहरूलाई यस वर्ष अधिक मेहनत गर्नुपर्ने देखिन्छ। लगनशीलतामा केही कमी आउन सक्छ जसले गर्दा अपेक्षित परिणाम प्राप्त गर्न कठिन हुन सक्छ। परीक्षामा राम्रो प्रदर्शन गर्न अनुशासन र निरन्तर अभ्यास अत्यावश्यक रहनेछ।.. शुभ संकेतहरू: शुभ रंग: रातो, पहेँलो र गुलाबी, शुभ अंक: ४, ५, ८, शुभ वार: आइतबार, मङ्गलबार, बृहस्पतिवार, शुभ महिना: असार, असोज, माघ, फागुन.. उपायहरू: शनि ग्रहको साढे सातीको प्रभाव कम गर्न शनिवारको दिन शनिदेवको पूजा, तिलको तेलको दीप प्रज्वलन गर्नु लाभदायक हुनेछ। भगवान गणेशको नियमित आराधना गर्नु, विशेषतः मंगलवारका दिन हनुमान चालीसा पाठ गर्नु अत्यन्तै फलदायी हुनेछ। शनिवारका दिन कालो कपडा, कालो छाता वा जुत्ता दान गर्नु, गरिबलाई सहयोग गर्नु सकारात्मक फल दिन्छ।';
    }

    // SPECIFIC COLUMN MAPPING FOR EACH TYPE
    switch (type.toLowerCase()) {
      case 'daily':
        // For daily, use Column E (Gemini Enhanced Horoscope)
        final geminiEnhanced = rasifalData['Gemini Enhanced Horoscope'];
        if (geminiEnhanced != null && geminiEnhanced.trim().isNotEmpty) {
          print('✅ Found "Gemini Enhanced Horoscope" with ${geminiEnhanced.length} characters');
          return geminiEnhanced.trim();
        }

        // Try column index 4 (Column E)
        final columnKeys = rasifalData.keys.toList();
        if (columnKeys.length >= 5) {
          final columnE = rasifalData[columnKeys[4]];
          if (columnE != null && columnE.trim().isNotEmpty) {
            print('✅ Found Column E content with ${columnE.length} characters');
            return columnE.trim();
          }
        }
        break;

      case 'weekly':
        // For weekly, use Column D (Gemini Enhanced Horoscope)
        final geminiEnhanced = rasifalData['Gemini Enhanced Weekly Horoscope'];
        if (geminiEnhanced != null && geminiEnhanced.trim().isNotEmpty) {
          print('✅ Found "Gemini Enhanced Weekly Horoscope" with ${geminiEnhanced.length} characters');
          return geminiEnhanced.trim();
        }

        // Try column index 3 (Column D)
        final columnKeys = rasifalData.keys.toList();
        if (columnKeys.length >= 4) {
          final columnD = rasifalData[columnKeys[3]];
          if (columnD != null && columnD.trim().isNotEmpty) {
            print('✅ Found Column D content with ${columnD.length} characters');
            return columnD.trim();
          }
        }
        break;

      case 'monthly':
        // For monthly, use Column D (Gemini Enhanced Horoscope)
        final geminiEnhanced = rasifalData['Gemini Enhanced Monthly Horoscope'];
        if (geminiEnhanced != null && geminiEnhanced.trim().isNotEmpty) {
          print('✅ Found "Gemini Enhanced Monthly Horoscope" with ${geminiEnhanced.length} characters');
          return geminiEnhanced.trim();
        }

        // Try column index 3 (Column D)
        final columnKeys = rasifalData.keys.toList();
        if (columnKeys.length >= 4) {
          final columnD = rasifalData[columnKeys[3]];
          if (columnD != null && columnD.trim().isNotEmpty) {
            print('✅ Found Column D content with ${columnD.length} characters');
            return columnD.trim();
          }
        }
        break;

      case 'yearly':
        // For yearly, the data structure is: Column A = Rashi Name, Column B = Complete Yearly Content
        final columnKeys = rasifalData.keys.toList();
        print('🎯 Yearly data columns: ${columnKeys}');
        print('🎯 All yearly data values: ${rasifalData.values.map((v) => v.length > 50 ? "${v.substring(0, 50)}..." : v).toList()}');

        // Try second column (index 1) which should be the yearly content
        if (columnKeys.length >= 2) {
          final yearlyContent = rasifalData[columnKeys[1]];
          if (yearlyContent != null && yearlyContent.trim().isNotEmpty) {
            print('✅ Found yearly content in column "${columnKeys[1]}" with ${yearlyContent.length} characters');
            return yearlyContent.trim();
          }
        }

        // Try looking for any column with long content (yearly data is very long)
        for (final entry in rasifalData.entries) {
          final content = entry.value.trim();
          if (content.length > 500) { // Reduced threshold for yearly content
            print('✅ Found long yearly content in column "${entry.key}" with ${content.length} characters');
            return content;
          }
        }

        // Try any non-empty content
        for (final entry in rasifalData.entries) {
          final content = entry.value.trim();
          if (content.isNotEmpty && content.length > 50) {
            print('✅ Found any yearly content in column "${entry.key}" with ${content.length} characters');
            return content;
          }
        }
        break;
    }

    // FALLBACK: Find longest content
    String longestContent = '';
    String longestColumnName = '';
    for (final entry in rasifalData.entries) {
      if (entry.value.length > longestContent.length) {
        longestContent = entry.value;
        longestColumnName = entry.key;
      }
    }

    if (longestContent.isNotEmpty) {
      print('🔄 Using longest content from "$longestColumnName" with ${longestContent.length} characters');
      return longestContent.trim();
    }

    // SPECIAL FALLBACK FOR मेष if no data found
    if (zodiacEnglish != null && zodiacEnglish.toLowerCase() == 'mesh') {
      print('🔄 Using special fallback content for मेष');
      return 'मेष राशिका लागि वार्षिक राशिफल (सामान्य विश्लेषण): यो वर्ष मेष राशिका जातकहरूका लागि समग्रमा मिश्रित फलदायक रहनेछ। वर्षको सुरुवातदेखि नै बृहस्पति र केतु को प्रभाव रहनेछ, जसले सकारात्मक ऊर्जा ल्याउने भए पनि केही क्षेत्रहरूमा चुनौती पनि दिन सक्छ। लग्नदेखि तेस्रो भावमा रहेका बृहस्पति को प्रभावले साहस, आत्मविश्वास र निर्णय क्षमतामा केही कमजोरी देखिन सक्छ। साथसाथै शनि देवको साढे सातीको दोस्रो चरण लागिरहेको हुनाले मानसिक चिन्ता, दबाब र केही असहज परिस्थितिहरू सामना गर्नु पर्ने संकेत छ। शारीरिक कमजोरी, थकान र बिना कारणको तनावले दैनिक जीवनलाई प्रभावित गर्न सक्छ। पञ्चम भावमा रहेका केतु को प्रभावले संतान सम्बन्धी चिन्ता वा उनीहरूको अध्ययनमा अवरोध उत्पन्न हुन सक्छ। तर, गृहिणी वर्गका लागि यो वर्ष आध्यात्मिक र धार्मिक क्षेत्रमा गहिरो रुचि देखिने सम्भावना छ।';
    }

    return 'राशिफल डाटा उपलब्ध छैन।';
  }

  // NO cleaning - return raw content as-is
  String _cleanRasifalContent(String content) {
    return content.trim(); // Only trim whitespace, keep everything else
  }

  // Get ONLY first 2 sentences with "..." for preview - STRICT
  String _getFirstTwoSentences(String content) {
    if (content.isEmpty) return 'राशिफल लोड गर्दै...';

    // Clean content first
    final cleanContent = content.trim();

    // Split by Nepali sentence endings
    final sentences = cleanContent.split(RegExp(r'[।!?]')).where((s) => s.trim().isNotEmpty).toList();

    if (sentences.isEmpty) {
      // Fallback: use character limit (VERY SHORT)
      return cleanContent.length > 80 ? '${cleanContent.substring(0, 80)}...' : cleanContent;
    }

    if (sentences.length == 1) {
      final firstSentence = sentences[0].trim();
      return firstSentence.length > 80 ? '${firstSentence.substring(0, 80)}...' : '$firstSentence।';
    }

    // Get ONLY first 2 sentences
    final firstTwo = sentences.take(2).map((s) => s.trim()).join('। ') + '।';

    // STRICT length limit to prevent overflow
    if (firstTwo.length > 100) {
      return '${firstTwo.substring(0, 100)}...';
    }

    // ALWAYS add "..." if there are more sentences
    if (sentences.length > 2) {
      return '$firstTwo...';
    }

    return '$firstTwo...'; // Always add ... for consistency
  }

  // Check if content is primarily in Nepali
  bool _isNepaliContent(String content) {
    // Check for Devanagari characters
    final nepaliPattern = RegExp(r'[\u0900-\u097F]');
    final nepaliMatches = nepaliPattern.allMatches(content).length;
    final totalChars = content.length;

    // If more than 30% of characters are Devanagari, consider it Nepali content
    return nepaliMatches > (totalChars * 0.3);
  }

  // Get type display name
  String _getTypeDisplayName(String type) {
    switch (type) {
      case 'daily': return 'दैनिक';
      case 'weekly': return 'साप्ताहिक';
      case 'monthly': return 'मासिक';
      case 'yearly': return 'वार्षिक';
      default: return type;
    }
  }

  // Get time ago string
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'अहिले';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} घण्टा अगाडि';
    } else {
      return '${difference.inDays} दिन अगाडि';
    }
  }

  // Show detailed rasifal modal
  void _showRasifalDetail(Map<String, dynamic> zodiac, String type, Map<String, String>? rasifalData) {
    final gradient = zodiac['gradient'] as List<Color>;
    final horoscope = _getFullHoroscope(zodiac, type, rasifalData);

    showDialog(
      context: context,
      builder: (BuildContext context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Text(
                    zodiac['icon'],
                    style: TextStyle(fontSize: 32),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${zodiac['name']} राशि',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'NotoSansDevanagari',
                          ),
                        ),
                        Text(
                          '${_getTypeDisplayName(type)} राशिफल',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                            fontFamily: 'NotoSansDevanagari',
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(Icons.close),
                  ),
                ],
              ),

              SizedBox(height: 20),

              // Scrollable content
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      horoscope,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.6,
                        fontFamily: 'NotoSansDevanagari',
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
              ),

              SizedBox(height: 16),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _copyToClipboard(zodiac, type, rasifalData);
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: gradient[0],
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      icon: Icon(Icons.copy),
                      label: Text(
                        'कपी गर्नुहोस्',
                        style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: Text(
                        'बन्द गर्नुहोस्',
                        style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Get full horoscope text with comprehensive content
  String _getFullHoroscope(Map<String, dynamic> zodiac, String type, Map<String, String>? rasifalData) {
    if (rasifalData == null) {
      return '${zodiac['name']} राशिका लागि ${_getTypeDisplayName(type)} राशिफल Google Sheets बाट लोड गर्न सकिएन। कृपया इन्टरनेट जडान जाँच गर्नुहोस् र फेरि प्रयास गर्नुहोस्।';
    }

    // Get the best available horoscope content
    String horoscope = _getBestHoroscopeContent(rasifalData, type, zodiac['english']);

    if (horoscope.isEmpty) {
      // Show available data for debugging
      final availableColumns = rasifalData.keys.where((k) => rasifalData[k]!.isNotEmpty).join(', ');
      return '${zodiac['name']} राशिका लागि ${_getTypeDisplayName(type)} राशिफल Google Sheets मा उपलब्ध छैन।\n\nउपलब्ध डाटा: $availableColumns';
    }

    // Return full content
    return horoscope;
  }

  // Copy to clipboard
  void _copyToClipboard(Map<String, dynamic> zodiac, String type, Map<String, String>? rasifalData) {
    final horoscope = _getFullHoroscope(zodiac, type, rasifalData);
    final text = '${zodiac['name']} राशि - ${_getTypeDisplayName(type)} राशिफल\n\n$horoscope\n\n📱 Nepali Calendar App बाट';

    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '✅ राशिफल कपी भयो!',
          style: TextStyle(fontFamily: 'NotoSansDevanagari'),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        duration: Duration(seconds: 2),
      ),
    );
  }
}