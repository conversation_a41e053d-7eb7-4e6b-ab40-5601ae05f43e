import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class SadeSatiTablePage extends StatefulWidget {
  final UserData user;

  const SadeSatiTablePage({Key? key, required this.user}) : super(key: key);

  @override
  State<SadeSatiTablePage> createState() => _SadeSatiTablePageState();
}

class _SadeSatiTablePageState extends State<SadeSatiTablePage> {
  bool _isLoading = false;
  dynamic _sadeSatiTableResult; // Can be List or Map depending on API response
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchSadeSatiTable();
  }

  UserData get user => widget.user;

  Future<void> _fetchSadeSatiTable() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getSadeSatiTable(user);
      
      setState(() {
        _sadeSatiTableResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'साढेसाती तालिका',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchSadeSatiTable,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_sadeSatiTableResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CircleAvatar(
            radius: 35,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 28,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day} • ${user.birthTime}',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 20),
          Text(
            'साढेसाती तालिका तयार गरिँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF1B5E20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchSadeSatiTable,
            icon: const Icon(Icons.refresh),
            label: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.table_chart,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'साढेसाती तालिका',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Display all the result data
          if (_sadeSatiTableResult != null)
            _buildSadeSatiTableContent(),
        ],
      ),
    );
  }

  Widget _buildSadeSatiTableContent() {
    // The API returns a List of sade sati periods
    if (_sadeSatiTableResult is List) {
      final periods = _sadeSatiTableResult as List;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary header
          Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'कुल ${periods.length} साढेसाती अवधिहरू फेला परे',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Display each sade sati period
          ...periods.asMap().entries.map((entry) {
            final index = entry.key;
            final period = entry.value as Map<String, dynamic>;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Period header
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getTypeColor(period['type'] ?? ''),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'अवधि ${index + 1}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Period details
                  _buildPeriodDetail('प्रकार', _translateType(period['type'] ?? '')),
                  _buildPeriodDetail('राशि', _translateZodiac(period['zodiac'] ?? '')),
                  _buildPeriodDetail('धैया', _translateDhaiya(period['dhaiya'] ?? '')),
                  _buildPeriodDetail('दिशा', _translateDirection(period['direction'] ?? '')),
                  _buildPeriodDetail('शुरुवात मिति', period['start_date'] ?? ''),
                  _buildPeriodDetail('समाप्ति मिति', period['end_date'] ?? ''),

                  if (period['retro'] == true)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.withOpacity(0.5)),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.refresh, color: Colors.orange, size: 16),
                          SizedBox(width: 6),
                          Text(
                            'वक्री (Retrograde)',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      // Fallback for unexpected response format
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_sadeSatiTableResult.toString()}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildPeriodDetail(String label, String value) {
    if (value.isEmpty || value == 'N/A') return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF1B5E20),
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'sade sati':
        return const Color(0xFFD32F2F); // Red for main Sade Sati
      case 'kantaka shani':
        return const Color(0xFFFF9800); // Orange for Kantaka Shani
      case 'ashtama shani':
        return const Color(0xFF9C27B0); // Purple for Ashtama Shani
      case 'ardhastama shani':
        return const Color(0xFF3F51B5); // Indigo for Ardhastama Shani
      default:
        return const Color(0xFF2E7D32); // Default green
    }
  }

  String _translateType(String type) {
    switch (type.toLowerCase()) {
      case 'sade sati':
        return 'साढेसाती';
      case 'kantaka shani':
        return 'कण्टक शनि';
      case 'ashtama shani':
        return 'अष्टम शनि';
      case 'ardhastama shani':
        return 'अर्धाष्टम शनि';
      default:
        return type;
    }
  }

  String _translateZodiac(String zodiac) {
    switch (zodiac.toLowerCase()) {
      case 'aries':
        return 'मेष';
      case 'taurus':
        return 'वृषभ';
      case 'gemini':
        return 'मिथुन';
      case 'cancer':
        return 'कर्कट';
      case 'leo':
        return 'सिंह';
      case 'virgo':
        return 'कन्या';
      case 'libra':
        return 'तुला';
      case 'scorpio':
        return 'वृश्चिक';
      case 'sagittarius':
        return 'धनु';
      case 'capricorn':
        return 'मकर';
      case 'aquarius':
        return 'कुम्भ';
      case 'pisces':
        return 'मीन';
      default:
        return zodiac;
    }
  }

  String _translateDhaiya(String dhaiya) {
    switch (dhaiya.toLowerCase()) {
      case '1st dhaiya':
        return 'पहिलो धैया';
      case '2nd dhaiya':
        return 'दोस्रो धैया';
      case '3rd dhaiya':
        return 'तेस्रो धैया';
      case 'small panoti':
        return 'सानो पनौती';
      default:
        return dhaiya;
    }
  }

  String _translateDirection(String direction) {
    switch (direction.toLowerCase()) {
      case 'rising':
        return 'बढ्दो';
      case 'peak':
        return 'चरम';
      case 'setting':
        return 'घट्दो';
      case 'n/a':
        return 'लागू नहुने';
      default:
        return direction;
    }
  }
}
