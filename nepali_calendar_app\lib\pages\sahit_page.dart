import 'package:flutter/material.dart';

class SahitPage extends StatefulWidget {
  const SahitPage({Key? key}) : super(key: key);

  @override
  State<SahitPage> createState() => _SahitPageState();
}

class _SahitPageState extends State<SahitPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final List<SahitCategory> _categories = [
    SahitCategory(
      title: 'बिवाह गर्ने साइत',
      icon: Icons.favorite,
      color: const Color(0xFFE91E63),
      months: [
        Sa<PERSON><PERSON><PERSON><PERSON>(
          name: 'वैशाख',
          dates: [1, 3, 5, 7, 8, 16, 17, 22, 23, 28, 30, 31],
        ),
        Sa<PERSON><PERSON><PERSON><PERSON>(
          name: 'जेठ',
          dates: [1, 2, 14, 18, 19, 24, 25],
        ),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(
          name: 'मंसिर',
          dates: [6, 8, 13, 14, 18, 19, 20],
        ),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(
          name: 'माघ',
          dates: [21, 22, 27],
        ),
        Sahit<PERSON><PERSON><PERSON>(
          name: 'फागुन',
          dates: [8, 12, 13, 14, 25, 26, 27, 28, 30],
        ),
      ],
    ),
    Sa<PERSON><PERSON><PERSON><PERSON><PERSON>(
      title: 'पास्नी गर्ने साइतहरु',
      icon: Icons.child_care,
      color: const Color(0xFF2196F3),
      months: [
        SahitMonth(
          name: 'वैशाख',
          dates: [1, 17],
        ),
        SahitMonth(
          name: 'जेठ',
          dates: [14],
        ),
        SahitMonth(
          name: 'असार',
          dates: [2, 13],
        ),
        SahitMonth(
          name: 'साउन',
          dates: [26, 29],
        ),
        SahitMonth(
          name: 'भदौ',
          dates: [29],
        ),
        SahitMonth(
          name: 'असोज',
          dates: [16],
        ),
        SahitMonth(
          name: 'कार्तिक',
          dates: [7, 17, 21, 24],
        ),
      ],
    ),
    SahitCategory(
      title: 'रुद्री गर्ने साइत',
      icon: Icons.self_improvement,
      color: const Color(0xFFFF9800),
      months: [
        SahitMonth(
          name: 'वैशाख',
          dates: [1, 4, 5, 6, 8, 11, 12, 13, 16, 19, 20, 21, 23, 26, 27, 28, 30],
        ),
        SahitMonth(
          name: 'जेठ',
          dates: [2, 3, 4, 6, 9, 10, 11, 17, 18, 19, 21, 25],
        ),
        SahitMonth(
          name: 'असार',
          dates: [2, 7, 30, 31],
        ),
        SahitMonth(
          name: 'साउन',
          dates: [2, 5, 6, 10, 13, 14, 15, 18, 21, 22, 23, 25, 28, 29, 31],
        ),
        SahitMonth(
          name: 'भदौ',
          dates: [3, 4, 5, 9, 12, 13, 14, 16],
        ),
        SahitMonth(
          name: 'असोज',
          dates: [7, 11, 12, 13, 15, 18, 20, 22, 24, 25, 26, 28, 31],
        ),
        SahitMonth(
          name: 'कार्तिक',
          dates: [1, 9, 10, 11, 13, 17, 18, 20, 23, 24, 25, 26, 29, 30],
        ),
      ],
    ),
    SahitCategory(
      title: 'होम गर्ने साइत',
      icon: Icons.local_fire_department,
      color: const Color(0xFFF44336),
      months: [
        SahitMonth(
          name: 'वैशाख',
          dates: [1, 3, 5, 7, 9, 11, 13, 14, 15, 17, 19, 22, 24, 26, 28, 30],
        ),
        SahitMonth(
          name: 'जेठ',
          dates: [1, 3, 4, 6, 8, 10, 12, 16, 18, 20, 22, 24],
        ),
        SahitMonth(
          name: 'असार',
          dates: [27, 29, 31],
        ),
        SahitMonth(
          name: 'साउन',
          dates: [1, 3, 4, 6, 9, 12, 14, 16, 17, 18, 20, 22, 24, 26, 28, 31],
        ),
        SahitMonth(
          name: 'भदौ',
          dates: [2, 4, 6, 8, 10, 12, 14, 16, 18],
        ),
        SahitMonth(
          name: 'असोज',
          dates: [6, 8, 10, 12, 14, 16, 18, 21, 23, 25, 26, 28, 30],
        ),
        SahitMonth(
          name: 'कार्तिक',
          dates: [1, 8, 9, 11, 13, 17, 19, 21, 24, 26, 28],
        ),
      ],
    ),
    SahitCategory(
      title: 'ब्रतबन्ध गर्ने साइत',
      icon: Icons.celebration,
      color: const Color(0xFF9C27B0),
      months: [
        SahitMonth(
          name: 'वैशाख',
          dates: [5, 19, 24],
        ),
        SahitMonth(
          name: 'फागुन',
          dates: [7, 14, 15],
        ),
        SahitMonth(
          name: 'चैत',
          dates: [6, 15],
        ),
      ],
    ),
    SahitCategory(
      title: 'पसल खोल्ने साइत',
      icon: Icons.store,
      color: const Color(0xFF4CAF50),
      months: [
        SahitMonth(
          name: 'वैशाख',
          dates: [7, 8, 17],
        ),
        SahitMonth(
          name: 'जेठ',
          dates: [4, 14, 17],
        ),
        SahitMonth(
          name: 'असार',
          dates: [27, 28],
        ),
        SahitMonth(
          name: 'साउन',
          dates: [5, 29],
        ),
        SahitMonth(
          name: 'भदौ',
          dates: [9, 15],
        ),
        SahitMonth(
          name: 'असोज',
          dates: [11, 25, 26],
        ),
        SahitMonth(
          name: 'कार्तिक',
          dates: [7, 17, 21, 29, 30],
        ),
      ],
    ),
    SahitCategory(
      title: 'जग राख्न्ने साइत',
      icon: Icons.foundation,
      color: const Color(0xFF795548),
      months: [
        SahitMonth(
          name: 'साउन',
          dates: [26],
        ),
        SahitMonth(
          name: 'कार्तिक',
          dates: [21],
        ),
        SahitMonth(
          name: 'मंसिर',
          dates: [11, 19, 20],
        ),
        SahitMonth(
          name: 'फागुन',
          dates: [14],
        ),
      ],
    ),
    SahitCategory(
      title: 'घर सर्ने साइत',
      icon: Icons.home,
      color: const Color(0xFF009688),
      months: [
        SahitMonth(
          name: 'माघ',
          dates: [28],
        ),
        SahitMonth(
          name: 'फागुन',
          dates: [14, 30],
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'शुभ साइतहरू - वि.सं. २०८२',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: _categories.map((category) {
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(category.icon, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    category.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: _categories.map((category) {
            return _buildCategoryContent(category);
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildCategoryContent(SahitCategory category) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Enhanced Category Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: category.color.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: category.color.withOpacity(0.15),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            category.color.withOpacity(0.2),
                            category.color.withOpacity(0.1),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: category.color.withOpacity(0.1),
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        category.icon,
                        color: category.color,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            category.title,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: category.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'वि.सं. २०८२ का शुभ मुहूर्तहरू',
                            style: TextStyle(
                              fontSize: 14,
                              color: category.color.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Months
          ...category.months.map((month) {
            return _buildMonthCard(month, category.color);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildMonthCard(SahitMonth month, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Month Header with enhanced design
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  color.withOpacity(0.2),
                  color.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: color.withOpacity(0.2),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.calendar_month,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  month.name,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),

          // Dates with enhanced design
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: month.dates.map((date) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            color.withOpacity(0.1),
                            color.withOpacity(0.05),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: color.withOpacity(0.3),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: color.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        date.toString(),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


}

class SahitCategory {
  final String title;
  final IconData icon;
  final Color color;
  final List<SahitMonth> months;

  SahitCategory({
    required this.title,
    required this.icon,
    required this.color,
    required this.months,
  });
}

class SahitMonth {
  final String name;
  final List<int> dates;

  SahitMonth({
    required this.name,
    required this.dates,
  });
}
