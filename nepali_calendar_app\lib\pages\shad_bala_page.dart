import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';

class ShadBalaPage extends StatefulWidget {
  final UserData user;

  const ShadBalaPage({Key? key, required this.user}) : super(key: key);

  @override
  State<ShadBalaPage> createState() => _ShadBalaPageState();
}

class _ShadBalaPageState extends State<ShadBalaPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _shadBalaResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchShadBala();
  }

  UserData get user => widget.user;

  Future<void> _fetchShadBala() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getShadBala(user);
      
      setState(() {
        _shadBalaResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'षड्बल विश्लेषण',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchShadBala,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_shadBalaResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CircleAvatar(
            radius: 35,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 28,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day} • ${user.birthTime}',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 20),
          Text(
            'षड्बल विश्लेषण गरिँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF1B5E20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchShadBala,
            icon: const Icon(Icons.refresh),
            label: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.balance,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'षड्बल विश्लेषण',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Display all the result data
          if (_shadBalaResult != null)
            _buildShadBalaContent(),
        ],
      ),
    );
  }

  Widget _buildShadBalaContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display each key-value pair from the result
        ..._shadBalaResult!.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatKey(entry.key),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
                const SizedBox(height: 12),
                if (entry.value is Map)
                  _buildPlanetaryTable(entry.value as Map<String, dynamic>)
                else
                  Text(
                    _formatValue(entry.value),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1B5E20),
                      height: 1.5,
                    ),
                  ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildPlanetaryTable(Map<String, dynamic> planetData) {
    return Column(
      children: planetData.entries.map((entry) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFF4CAF50).withOpacity(0.2),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _translatePlanet(entry.key),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1B5E20),
                ),
              ),
              Text(
                entry.value?.toString() ?? 'N/A',
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF2E7D32),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'uccha_bala':
        return 'उच्च बल';
      case 'saptavargaja_bala':
        return 'सप्तवर्गज बल';
      case 'ojayugma_bala':
        return 'ओजयुग्म बल';
      case 'kendra_bala':
        return 'केन्द्र बल';
      case 'drekkna_bala':
        return 'द्रेक्काण बल';
      case 'total_sthana_bala':
        return 'कुल स्थान बल';
      case 'nathonnatha_bala':
        return 'नाथोन्नाथ बल';
      case 'dig_bala':
        return 'दिक् बल';
      case 'paksha_bala':
        return 'पक्ष बल';
      case 'thribhaga_bala':
        return 'त्रिभाग बल';
      case 'abda_bala':
        return 'अब्द बल';
      case 'masa_bala':
        return 'मास बल';
      case 'vara_bala':
        return 'वार बल';
      case 'hora_bala':
        return 'होरा बल';
      case 'total_balas':
        return 'कुल बलहरू';
      case 'ayana_bala':
        return 'अयन बल';
      case 'chesta_bala':
        return 'चेष्टा बल';
      case 'naisargeka_balas':
        return 'नैसर्गिक बल';
      case 'drik_bala':
        return 'दृक् बल';
      case 'ratio':
        return 'अनुपात';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      return value.map((item) => '• $item').join('\n');
    } else if (value is Map) {
      return value.entries
          .map((e) => '${_translatePlanet(e.key.toString())}: ${e.value}')
          .join('\n');
    } else {
      return value.toString();
    }
  }

  String _translatePlanet(String planet) {
    switch (planet.toLowerCase()) {
      case 'sun':
        return 'सूर्य';
      case 'moon':
        return 'चन्द्र';
      case 'mars':
        return 'मंगल';
      case 'mercury':
        return 'बुध';
      case 'jupiter':
        return 'बृहस्पति';
      case 'venus':
        return 'शुक्र';
      case 'saturn':
        return 'शनि';
      case 'rahu':
        return 'राहु';
      case 'ketu':
        return 'केतु';
      default:
        return planet;
    }
  }
}
