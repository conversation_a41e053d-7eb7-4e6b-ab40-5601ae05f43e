import 'package:flutter/material.dart';
import '../models/share_market.dart';
import '../services/share_market_service.dart';

class ShareMarketPage extends StatefulWidget {
  const ShareMarketPage({super.key});

  @override
  State<ShareMarketPage> createState() => _ShareMarketPageState();
}

class _ShareMarketPageState extends State<ShareMarketPage> {
  List<ShareMarket> shares = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadShareMarketData();
  }

  Future<void> loadShareMarketData({bool forceRefresh = false}) async {
    setState(() {
      isLoading = true;
    });

    try {
      final data = await ShareMarketService.getShareMarketData(forceRefresh: forceRefresh);
      setState(() {
        shares = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: const Text(
          'शेयर बजार',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        backgroundColor: const Color(0xFF1565C0),
        foregroundColor: Colors.white,
        elevation: 8,
        shadowColor: Colors.black.withOpacity(0.3),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: () => loadShareMarketData(forceRefresh: true),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0D47A1),
              Color(0xFF1565C0),
              Color(0xFF1976D2),
              Color(0xFFE3F2FD),
            ],
            stops: [0.0, 0.2, 0.4, 1.0],
          ),
        ),
        child: isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 20),
                    Text(
                      'शेयर डाटा लोड गर्दै...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  // Beautiful Summary Card
                  Container(
                    margin: const EdgeInsets.all(8),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white,
                          Color(0xFFF3F8FF),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.15),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                          spreadRadius: 2,
                        ),
                      ],
                      border: Border.all(
                        color: const Color(0xFF1976D2).withOpacity(0.2),
                        width: 1.5,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1976D2).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: const Icon(
                            Icons.trending_up_rounded,
                            color: Color(0xFF1565C0),
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'कुल ${_convertToNepaliNumbers(shares.length.toString())} कम्पनीहरू',
                                style: const TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF1565C0),
                                  fontFamily: 'NotoSansDevanagari',
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Text(
                                'नेप्से शेयर बजारको लाइभ डाटा',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'NotoSansDevanagari',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Modern Table Header
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFF0D47A1),
                          Color(0xFF1565C0),
                          Color(0xFF1976D2),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                    child: const Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            'सिम्बल',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'LTP',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'परिवर्तन',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'उच्च',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            'न्यून',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Beautiful Share List
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      child: ListView.builder(
                        padding: const EdgeInsets.only(
                          top: 8,
                          left: 4,
                          right: 4,
                          bottom: 16,
                        ),
                        itemCount: shares.length,
                        itemBuilder: (context, index) {
                          final share = shares[index];
                          final isEven = index % 2 == 0;

                          return Container(
                            margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: isEven 
                                  ? [const Color(0xFFF8FBFF), Colors.white]
                                  : [Colors.white, const Color(0xFFF0F6FF)],
                              ),
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(
                                color: const Color(0xFF1976D2).withOpacity(0.1),
                                width: 1.5,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                // Symbol
                                Expanded(
                                  flex: 3,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 4),
                                    child: Text(
                                      share.symbol,
                                      style: const TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF2E2E2E),
                                        fontFamily: 'NotoSansDevanagari',
                                      ),
                                      overflow: TextOverflow.visible,
                                      maxLines: null,
                                    ),
                                  ),
                                ),

                                // LTP (Last Traded Price)
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    share.formattedLtp,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF1565C0),
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'NotoSansDevanagari',
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),

                                // Change Percent
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    share.formattedChange,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: share.isPositive ? const Color(0xFF2E7D32) : const Color(0xFFD32F2F),
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'NotoSansDevanagari',
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),

                                // High Price
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    share.formattedHigh,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF2E7D32),
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'NotoSansDevanagari',
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),

                                // Low Price
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    share.formattedLow,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFFE65100),
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'NotoSansDevanagari',
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // Beautiful Footer
                  Container(
                    margin: const EdgeInsets.fromLTRB(12, 12, 12, 24),
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFFF8FBFF),
                          Colors.white,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                          spreadRadius: 1,
                        ),
                      ],
                      border: Border.all(
                        color: const Color(0xFF1976D2).withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1976D2).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.show_chart_rounded,
                            color: Color(0xFF1565C0),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Text(
                            'नेप्से शेयर बजारको लाइभ डाटा • नियमित अपडेट',
                            style: TextStyle(
                              fontSize: 13,
                              color: Color(0xFF424242),
                              fontWeight: FontWeight.w600,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // Convert English numbers to Nepali numbers
  String _convertToNepaliNumbers(String englishNumber) {
    const englishToNepali = {
      '0': '०',
      '1': '१',
      '2': '२',
      '3': '३',
      '4': '४',
      '5': '५',
      '6': '६',
      '7': '७',
      '8': '८',
      '9': '९',
    };

    String nepaliNumber = englishNumber;
    englishToNepali.forEach((english, nepali) {
      nepaliNumber = nepaliNumber.replaceAll(english, nepali);
    });

    return nepaliNumber;
  }
}
