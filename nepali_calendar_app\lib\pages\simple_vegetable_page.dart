import 'package:flutter/material.dart';
import '../models/simple_vegetable.dart';
import '../services/simple_vegetable_service.dart';

class SimpleVegetablePage extends StatefulWidget {
  const SimpleVegetablePage({Key? key}) : super(key: key);

  @override
  State<SimpleVegetablePage> createState() => _SimpleVegetablePageState();
}

class _SimpleVegetablePageState extends State<SimpleVegetablePage> {
  List<SimpleVegetable> vegetables = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadVegetables();
  }

  Future<void> loadVegetables({bool forceRefresh = false}) async {
    setState(() {
      isLoading = true;
    });

    try {
      final data = await SimpleVegetableService.getVegetables(forceRefresh: forceRefresh);
      setState(() {
        vegetables = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  // Return all vegetables without filtering
  List<SimpleVegetable> getAllVegetables() {
    return vegetables;
  }

  // Convert English numbers to Nepali numbers
  String _convertToNepaliNumbers(String text) {
    Map<String, String> englishToNepali = {
      '0': '०', '1': '१', '2': '२', '3': '३', '4': '४',
      '5': '५', '6': '६', '7': '७', '8': '८', '9': '९'
    };

    String result = text;
    englishToNepali.forEach((english, nepali) {
      result = result.replaceAll(english, nepali);
    });

    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: const Text(
          'तरकारी बजारभाउ',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        backgroundColor: const Color(0xFF1B5E20),
        foregroundColor: Colors.white,
        elevation: 8,
        shadowColor: Colors.black.withOpacity(0.3),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              onPressed: () => loadVegetables(forceRefresh: true),
              icon: const Icon(Icons.refresh_rounded, size: 24),
              style: IconButton.styleFrom(
                backgroundColor: Colors.white.withOpacity(0.2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
          decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1B5E20),
              Color(0xFF2E7D32),
              Color(0xFF4CAF50),
              Color(0xFFE8F5E8),
            ],
            stops: [0.0, 0.2, 0.4, 1.0],
          ),
        ),
        child: isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 20),
                    Text(
                      'ताजा भाउदर लोड गर्दै...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'NotoSansDevanagari',
                      ),
                    ),
                  ],
                ),
              )
            : vegetables.isEmpty
                ? const Center(
                    child: Text(
                      'कुनै तरकारी फेला परेन',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
              : Column(
                  children: [
                    // Beautiful Header Card
                    Container(
                      margin: const EdgeInsets.all(8),
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            Color(0xFFF8FFF8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.15),
                            blurRadius: 15,
                            offset: const Offset(0, 8),
                            spreadRadius: 2,
                          ),
                        ],
                        border: Border.all(
                          color: const Color(0xFF4CAF50).withOpacity(0.2),
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: const Color(0xFF4CAF50).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.shopping_basket_rounded,
                              color: Color(0xFF4CAF50),
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'कुल ${_convertToNepaliNumbers(vegetables.length.toString())} वस्तुहरू',
                                  style: const TextStyle(
                                    fontSize: 26,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF2E7D32),
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                                const SizedBox(height: 4),
                                const Text(
                                  'कालिमाटी बजारको ताजा भाउदर',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'NotoSansDevanagari',
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: const Color(0xFF4CAF50),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Text(
                              'LIVE',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Modern Table Header
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [
                            Color(0xFF1B5E20),
                            Color(0xFF2E7D32),
                            Color(0xFF4CAF50),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: const Text(
                              'कृषि उपज',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: const Text(
                              'न्यूनतम',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: const Text(
                              'अधिकतम',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: const Text(
                              'औसत',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Beautiful Card List
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(15),
                            bottomRight: Radius.circular(15),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ListView.builder(
                          padding: const EdgeInsets.only(
                            top: 8,
                            left: 4,
                            right: 4,
                            bottom: 16,
                          ),
                          itemCount: getAllVegetables().length,
                          itemBuilder: (context, index) {
                            final vegetable = getAllVegetables()[index];
                            final isEven = index % 2 == 0;

                            return Container(
                              margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: isEven
                                    ? [const Color(0xFFF8FFF8), Colors.white]
                                    : [Colors.white, const Color(0xFFF0F8F0)],
                                ),
                                borderRadius: BorderRadius.circular(15),
                                border: Border.all(
                                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  // Product Name
                                  Expanded(
                                    flex: 5,
                                    child: Padding(
                                      padding: const EdgeInsets.only(right: 4),
                                      child: Text(
                                        vegetable.name,
                                        style: const TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFF2E2E2E),
                                          fontFamily: 'NotoSansDevanagari',
                                        ),
                                        overflow: TextOverflow.visible,
                                        maxLines: null,
                                      ),
                                    ),
                                  ),

                                  // Minimum Price
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      vegetable.minPrice,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF1976D2),
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'NotoSansDevanagari',
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),

                                  // Maximum Price
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      vegetable.maxPrice,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFFE65100),
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'NotoSansDevanagari',
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),

                                  // Average Price
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      vegetable.avgPrice,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF2E7D32),
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'NotoSansDevanagari',
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                    // Beautiful Footer
                    Container(
                      margin: const EdgeInsets.fromLTRB(12, 12, 12, 24),
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Color(0xFFF8FFF8),
                            Colors.white,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                            spreadRadius: 1,
                          ),
                        ],
                        border: Border.all(
                          color: const Color(0xFF4CAF50).withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFF4CAF50).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.store_rounded,
                              color: Color(0xFF2E7D32),
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Expanded(
                            child: Text(
                              'कालिमाटी बजारको दैनिक भाउदर • नियमित अपडेट',
                              style: TextStyle(
                                fontSize: 13,
                                color: Color(0xFF424242),
                                fontWeight: FontWeight.w600,
                                fontFamily: 'NotoSansDevanagari',
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}
