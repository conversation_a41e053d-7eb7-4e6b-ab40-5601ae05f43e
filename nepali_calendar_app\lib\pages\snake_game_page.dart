import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../models/snake_game_model.dart';

class SnakeGamePage extends StatefulWidget {
  const SnakeGamePage({super.key});

  @override
  State<SnakeGamePage> createState() => _SnakeGamePageState();
}

class _SnakeGamePageState extends State<SnakeGamePage>
    with TickerProviderStateMixin {
  late SnakeGame game;
  Timer? gameTimer;
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    game = SnakeGame();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _startGame() {
    game.startGame();
    _startGameLoop();
    setState(() {});
  }

  void _startGameLoop() {
    gameTimer?.cancel();
    gameTimer = Timer.periodic(Duration(milliseconds: game.speed), (timer) {
      if (game.isGameActive) {
        game.update();
        setState(() {});
        
        if (game.isGameOver) {
          timer.cancel();
          _showGameOverDialog();
        }
        
        // Update timer speed if it changed
        if (timer.tick % 10 == 0) {
          timer.cancel();
          _startGameLoop();
        }
      }
    });
  }

  void _pauseGame() {
    game.togglePause();
    if (game.isGamePaused) {
      gameTimer?.cancel();
    } else {
      _startGameLoop();
    }
    setState(() {});
  }

  void _resetGame() {
    gameTimer?.cancel();
    game.resetGame();
    setState(() {});
  }

  void _showGameOverDialog() {
    HapticFeedback.heavyImpact();
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            'खेल समाप्त!',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.emoji_events,
                color: Colors.amber,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'तपाईंको स्कोर: ${game.score}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontFamily: 'NotoSansDevanagari',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'उच्च स्कोर: ${game.highScore}',
                style: TextStyle(
                  color: Colors.amber,
                  fontSize: 16,
                  fontFamily: 'NotoSansDevanagari',
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'सर्पको लम्बाई: ${game.snakeLength}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: 'NotoSansDevanagari',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resetGame();
              },
              child: const Text(
                'फेरि खेल्नुहोस्',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 16,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: const Text(
                'बाहिर निस्कनुहोस्',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom App Bar
              _buildCustomAppBar(),
              
              // Score Board
              _buildScoreBoard(),
              
              // Game Board - Bigger and more prominent
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: _buildGameBoard(),
                ),
              ),
              
              // Controls - Compact layout
              _buildControls(),

              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'स्नेक गेम',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontFamily: 'NotoSansDevanagari',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'क्लासिक आर्केड',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ],
            ),
          ),
          if (game.isGameActive)
            GestureDetector(
              onTap: _pauseGame,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.orange, Colors.deepOrange],
                  ),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  game.isGamePaused ? Icons.play_arrow : Icons.pause,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildScoreBoard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.15),
            Colors.white.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreItem('स्कोर', game.score.toString(), Colors.green),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withOpacity(0.3),
          ),
          _buildScoreItem('उच्च स्कोर', game.highScore.toString(), Colors.amber),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withOpacity(0.3),
          ),
          _buildScoreItem('स्पीड', game.getSpeedLevel().toString(), Colors.blue),
        ],
      ),
    );
  }

  Widget _buildScoreItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildGameBoard() {
    return GestureDetector(
      onTap: () {
        if (game.isGameReady) {
          _startGame();
        } else if (game.isGamePaused) {
          _pauseGame();
        }
      },
      onPanUpdate: (details) {
        if (!game.isGameActive) return;

        // Swipe gestures for direction control
        if (details.delta.dx > 10) {
          game.changeDirection(Direction.right);
        } else if (details.delta.dx < -10) {
          game.changeDirection(Direction.left);
        } else if (details.delta.dy > 10) {
          game.changeDirection(Direction.down);
        } else if (details.delta.dy < -10) {
          game.changeDirection(Direction.up);
        }
      },
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.15),
              Colors.white.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              spreadRadius: 2,
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: Stack(
            children: [
              // Game Grid
              _buildGameGrid(),

              // Game State Overlay
              if (!game.isGameActive) _buildGameStateOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGameGrid() {
    return Padding(
      padding: const EdgeInsets.all(6),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: SnakeGame.gridWidth,
          crossAxisSpacing: 0.5,
          mainAxisSpacing: 0.5,
        ),
        itemCount: SnakeGame.gridWidth * SnakeGame.gridHeight,
        itemBuilder: (context, index) {
          int x = index % SnakeGame.gridWidth;
          int y = index ~/ SnakeGame.gridWidth;
          Position pos = Position(x, y);

          return _buildGridCell(pos);
        },
      ),
    );
  }

  Widget _buildGridCell(Position pos) {
    Color cellColor = Colors.transparent;
    Widget? cellChild;

    if (game.isSnakeHead(pos)) {
      cellColor = Colors.green;
      cellChild = const Icon(
        Icons.circle,
        color: Colors.white,
        size: 12,
      );
    } else if (game.isSnakeBody(pos)) {
      cellColor = Colors.green[300]!;
    } else if (game.isFood(pos)) {
      cellColor = Colors.red;
      cellChild = ScaleTransition(
        scale: _pulseAnimation,
        child: const Icon(
          Icons.circle,
          color: Colors.white,
          size: 10,
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: cellColor,
        borderRadius: BorderRadius.circular(2),
        boxShadow: cellColor != Colors.transparent
            ? [
                BoxShadow(
                  color: cellColor.withOpacity(0.5),
                  spreadRadius: 1,
                  blurRadius: 2,
                ),
              ]
            : null,
      ),
      child: cellChild,
    );
  }

  Widget _buildGameStateOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(18),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              game.isGameReady ? Icons.play_circle_filled :
              game.isGamePaused ? Icons.pause_circle_filled : Icons.refresh,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              game.getGameStateMessage(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
            if (game.isGameReady) ...[
              const SizedBox(height: 8),
              Text(
                'स्वाइप गरेर दिशा बदल्नुहोस्',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                  fontFamily: 'NotoSansDevanagari',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        children: [
          // Directional Controls
          Column(
            children: [
              // Up
              _buildControlButton(Icons.keyboard_arrow_up, () {
                game.changeDirection(Direction.up);
              }),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Left
                  _buildControlButton(Icons.keyboard_arrow_left, () {
                    game.changeDirection(Direction.left);
                  }),

                  // Center space
                  const SizedBox(width: 60),

                  // Right
                  _buildControlButton(Icons.keyboard_arrow_right, () {
                    game.changeDirection(Direction.right);
                  }),
                ],
              ),

              // Down
              _buildControlButton(Icons.keyboard_arrow_down, () {
                game.changeDirection(Direction.down);
              }),
            ],
          ),

          const SizedBox(height: 10),

          // Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                'नयाँ खेल',
                Icons.refresh,
                Colors.green,
                _resetGame,
              ),
              if (game.isGameActive)
                _buildActionButton(
                  game.isGamePaused ? 'जारी राख्नुहोस्' : 'रोक्नुहोस्',
                  game.isGamePaused ? Icons.play_arrow : Icons.pause,
                  Colors.orange,
                  _pauseGame,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton(IconData icon, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.all(4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[600]!, Colors.blue[800]!],
          ),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildActionButton(String text, IconData icon, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withOpacity(0.8)],
          ),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
