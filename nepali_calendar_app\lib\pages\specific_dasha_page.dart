import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class SpecificDashaPage extends StatefulWidget {
  final UserData user;

  const SpecificDashaPage({Key? key, required this.user}) : super(key: key);

  @override
  State<SpecificDashaPage> createState() => _SpecificDashaPageState();
}

class _SpecificDashaPageState extends State<SpecificDashaPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _specificDashaResult;
  String? _error;
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _resultKey = GlobalKey();

  // Planet selection
  String _selectedMahadasha = 'Sun';
  String _selectedAntardasha = 'Sun';
  String _selectedParyantardasha = 'Sun';
  String _selectedSookshmadasha = 'Sun';

  final List<String> _planets = [
    'Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Saturn', 'Jupiter', '<PERSON><PERSON>', 'Ketu'
  ];

  @override
  void initState() {
    super.initState();
  }

  UserData get user => widget.user;

  Future<void> _fetchSpecificDasha() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getSpecificDasha(
        user,
        mahadasha: _selectedMahadasha,
        antardasha: _selectedAntardasha,
        paryantardasha: _selectedParyantardasha,
        sookshmadasha: _selectedSookshmadasha,
      );
      
      setState(() {
        _specificDashaResult = result;
        _isLoading = false;
      });

      // Scroll to result after data loads
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_resultKey.currentContext != null) {
          Scrollable.ensureVisible(
            _resultKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'विशिष्ट दशा विश्लेषण',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchSpecificDasha,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                _buildPlanetSelectionCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'विशिष्ट दशा विश्लेषण गर्दै...',
                  featureName: 'विशिष्ट दशा',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchSpecificDasha,
                  featureName: 'विशिष्ट दशा',
                ),
                if (_specificDashaResult != null)
                  Container(
                    key: _resultKey,
                    child: _buildResultWidget(),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day} • ${user.birthTime}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetSelectionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.tune,
                color: Color(0xFF2E7D32),
                size: 32,
              ),
              SizedBox(width: 12),
              Text(
                'ग्रह चयन गर्नुहोस्',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          _buildPlanetDropdown('महादशा', _selectedMahadasha, (value) {
            setState(() {
              _selectedMahadasha = value!;
            });
          }),
          
          _buildPlanetDropdown('अन्तर्दशा', _selectedAntardasha, (value) {
            setState(() {
              _selectedAntardasha = value!;
            });
          }),
          
          _buildPlanetDropdown('प्रत्यन्तर्दशा', _selectedParyantardasha, (value) {
            setState(() {
              _selectedParyantardasha = value!;
            });
          }),
          
          _buildPlanetDropdown('सूक्ष्म दशा', _selectedSookshmadasha, (value) {
            setState(() {
              _selectedSookshmadasha = value!;
            });
          }),
          
          const SizedBox(height: 20),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _fetchSpecificDasha,
              icon: const Icon(Icons.search),
              label: const Text(
                'दशा विश्लेषण गर्नुहोस्',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetDropdown(String label, String selectedValue, ValueChanged<String?> onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF2E7D32),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: selectedValue,
                onChanged: onChanged,
                items: _planets.map((planet) {
                  return DropdownMenuItem<String>(
                    value: planet,
                    child: Text(
                      _translatePlanet(planet),
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                  );
                }).toList(),
                icon: const Icon(
                  Icons.arrow_drop_down,
                  color: Color(0xFF4CAF50),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 20),
          Text(
            'विशिष्ट दशा विश्लेषण गरिँदै...',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF1B5E20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchSpecificDasha,
            icon: const Icon(Icons.refresh),
            label: const Text('पुनः प्रयास गर्नुहोस्'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.analytics,
                color: Color(0xFF2E7D32),
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'विशिष्ट दशा विश्लेषण',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    Text(
                      '${_translatePlanet(_selectedMahadasha)} - ${_translatePlanet(_selectedAntardasha)} - ${_translatePlanet(_selectedParyantardasha)} - ${_translatePlanet(_selectedSookshmadasha)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF66BB6A),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Display all the result data
          if (_specificDashaResult != null)
            _buildSpecificDashaContent(),
        ],
      ),
    );
  }

  Widget _buildSpecificDashaContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display each key-value pair from the result
        ..._specificDashaResult!.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getIconForKey(entry.key),
                      color: const Color(0xFF2E7D32),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _formatKey(entry.key),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF4CAF50).withOpacity(0.2),
                    ),
                  ),
                  child: _buildFormattedValue(entry.key, entry.value),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'specific_dasha':
        return 'विशिष्ट दशा';
      case 'dasha_combination':
        return 'दशा संयोजन';
      case 'start_date':
        return 'शुरुवात मिति';
      case 'end_date':
        return 'समाप्ति मिति';
      case 'duration':
        return 'अवधि';
      case 'remaining_period':
        return 'बाँकी अवधि';
      case 'effects':
        return 'प्रभावहरू';
      case 'predictions':
        return 'भविष्यवाणी';
      case 'remedies':
        return 'उपायहरू';
      case 'favorable_activities':
        return 'अनुकूल गतिविधिहरू';
      case 'unfavorable_activities':
        return 'प्रतिकूल गतिविधिहरू';
      case 'general_effects':
        return 'सामान्य प्रभावहरू';
      case 'career_effects':
        return 'करियर प्रभावहरू';
      case 'health_effects':
        return 'स्वास्थ्य प्रभावहरू';
      case 'relationship_effects':
        return 'सम्बन्ध प्रभावहरू';
      case 'financial_effects':
        return 'आर्थिक प्रभावहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      return value.map((item) => '• $item').join('\n');
    } else if (value is Map) {
      return value.entries
          .map((e) => '${_formatKey(e.key.toString())}: ${_formatValue(e.value)}')
          .join('\n');
    } else {
      return value.toString();
    }
  }

  String _translatePlanet(String planet) {
    switch (planet.toLowerCase()) {
      case 'sun':
        return 'सूर्य';
      case 'moon':
        return 'चन्द्र';
      case 'mars':
        return 'मंगल';
      case 'mercury':
        return 'बुध';
      case 'jupiter':
        return 'बृहस्पति';
      case 'venus':
        return 'शुक्र';
      case 'saturn':
        return 'शनि';
      case 'rahu':
        return 'राहु';
      case 'ketu':
        return 'केतु';
      default:
        return planet;
    }
  }

  Widget _buildFormattedValue(String key, dynamic value) {
    if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.6,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is Map) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((e) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_formatKey(e.key.toString())}:',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2E7D32),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  e.value.toString(),
                  style: const TextStyle(
                    fontSize: 18,
                    color: Color(0xFF1B5E20),
                    height: 1.6,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.6,
        ),
      );
    }
  }

  IconData _getIconForKey(String key) {
    switch (key.toLowerCase()) {
      case 'specific_dasha':
      case 'dasha_combination':
        return Icons.timeline;
      case 'start_date':
        return Icons.play_arrow;
      case 'end_date':
        return Icons.stop;
      case 'duration':
        return Icons.schedule;
      case 'predictions':
        return Icons.psychology;
      case 'effects':
        return Icons.star;
      case 'remedies':
        return Icons.healing;
      case 'favorable':
        return Icons.thumb_up;
      case 'unfavorable':
        return Icons.thumb_down;
      case 'mahadasha':
        return Icons.public;
      case 'antardasha':
        return Icons.access_time;
      case 'pratyantardasha':
        return Icons.timer;
      default:
        return Icons.info;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
