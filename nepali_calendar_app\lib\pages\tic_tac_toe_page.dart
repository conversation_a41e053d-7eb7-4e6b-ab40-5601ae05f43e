import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/tic_tac_toe_model.dart';

class TicTacToePage extends StatefulWidget {
  final GameMode gameMode;
  final Player humanPlayer;
  final BotDifficulty botDifficulty;

  const TicTacToePage({
    super.key,
    this.gameMode = GameMode.friend,
    this.humanPlayer = Player.X,
    this.botDifficulty = BotDifficulty.medium,
  });

  @override
  State<TicTacToePage> createState() => _TicTacToePageState();
}

class _TicTacToePageState extends State<TicTacToePage>
    with TickerProviderStateMixin {
  late TicTacToeGame game;
  late AnimationController _boardAnimationController;
  late AnimationController _cellAnimationController;
  late Animation<double> _boardScaleAnimation;
  late Animation<double> _cellScaleAnimation;

  @override
  void initState() {
    super.initState();
    game = TicTacToeGame(
      gameMode: widget.gameMode,
      humanPlayer: widget.humanPlayer,
      botDifficulty: widget.botDifficulty,
    );
    
    _boardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _cellAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _boardScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _boardAnimationController,
      curve: Curves.elasticOut,
    ));
    
    _cellScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cellAnimationController,
      curve: Curves.bounceOut,
    ));
    
    _boardAnimationController.forward();
  }

  @override
  void dispose() {
    _boardAnimationController.dispose();
    _cellAnimationController.dispose();
    super.dispose();
  }

  void _onCellTap(int row, int col) {
    // Don't allow moves if it's bot's turn
    if (game.gameMode == GameMode.bot && game.currentPlayer == game.botPlayer) {
      return;
    }

    if (game.makeMove(row, col)) {
      setState(() {});

      // Vibrate on move
      HapticFeedback.lightImpact();

      // Check if game ended
      if (game.gameState != GameState.playing) {
        _showGameEndDialog();
      } else if (game.isBotTurn()) {
        // Bot makes move after a short delay
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted && game.isBotTurn()) {
            game.makeBotMove();
            setState(() {});

            // Check if game ended after bot move
            if (game.gameState != GameState.playing) {
              _showGameEndDialog();
            }
          }
        });
      }
    }
  }

  void _showGameEndDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'खेल समाप्त!',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansDevanagari',
            ),
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                game.getGameResultMessage(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontFamily: 'NotoSansDevanagari',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              _buildScoreCard(),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resetGame();
              },
              child: const Text(
                'नयाँ खेल',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 16,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              },
              child: const Text(
                'बाहिर निस्कनुहोस्',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _resetGame() {
    setState(() {
      game.resetGame();
    });
    _boardAnimationController.reset();
    _boardAnimationController.forward();
  }

  void _resetScores() {
    setState(() {
      game.resetScores();
    });
    _boardAnimationController.reset();
    _boardAnimationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom App Bar
              _buildCustomAppBar(),
              
              // Score Board
              _buildScoreBoard(),
              
              // Current Player Info
              _buildCurrentPlayerInfo(),
              
              // Game Board
              Expanded(
                child: Center(
                  child: _buildGameBoard(),
                ),
              ),
              
              // Action Buttons
              _buildActionButtons(),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'टिक ट्याक टो',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontFamily: 'NotoSansDevanagari',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  game.getGameModeText(),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreBoard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.15),
            Colors.white.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreItem('X', game.xScore, Colors.blue),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withOpacity(0.3),
          ),
          _buildScoreItem('बराबरी', game.drawCount, Colors.orange),
          Container(
            width: 1,
            height: 40,
            color: Colors.white.withOpacity(0.3),
          ),
          _buildScoreItem('O', game.oScore, Colors.red),
        ],
      ),
    );
  }

  Widget _buildScoreItem(String label, int score, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          score.toString(),
          style: TextStyle(
            color: color,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentPlayerInfo() {
    if (game.gameState != GameState.playing) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: game.currentPlayer == Player.X ? Colors.blue.withOpacity(0.2) : Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: game.currentPlayer == Player.X ? Colors.blue : Colors.red,
          width: 2,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person,
            color: game.currentPlayer == Player.X ? Colors.blue : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            game.getGameResultMessage(),
            style: TextStyle(
              color: game.currentPlayer == Player.X ? Colors.blue : Colors.red,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameBoard() {
    return ScaleTransition(
      scale: _boardScaleAnimation,
      child: Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.15),
              Colors.white.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              spreadRadius: 2,
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 9,
            itemBuilder: (context, index) {
              int row = index ~/ 3;
              int col = index % 3;
              return _buildGameCell(row, col);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildGameCell(int row, int col) {
    final player = game.board[row][col];
    final isWinning = game.isWinningCell(row, col);

    return GestureDetector(
      onTap: () => _onCellTap(row, col),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isWinning
                ? [Colors.green.withOpacity(0.3), Colors.green.withOpacity(0.1)]
                : [Colors.white.withOpacity(0.2), Colors.white.withOpacity(0.05)],
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: isWinning ? Colors.green : Colors.white.withOpacity(0.3),
            width: isWinning ? 3 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              game.getPlayerSymbol(player),
              key: ValueKey('${row}_${col}_${player.toString()}'),
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: player == Player.X ? Colors.blue :
                       player == Player.O ? Colors.red : Colors.transparent,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.3),
                    offset: const Offset(2, 2),
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              'नयाँ खेल',
              Icons.refresh,
              Colors.orange,
              _resetGame,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildActionButton(
              'स्कोर रिसेट',
              Icons.clear_all,
              Colors.red,
              _resetScores,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String text, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withOpacity(0.8)],
          ),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.15),
            Colors.white.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildScoreItem('X', game.xScore, Colors.blue),
          _buildScoreItem('बराबरी', game.drawCount, Colors.orange),
          _buildScoreItem('O', game.oScore, Colors.red),
        ],
      ),
    );
  }
}
