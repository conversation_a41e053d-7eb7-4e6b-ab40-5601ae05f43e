import 'package:flutter/material.dart';
import '../models/tic_tac_toe_model.dart';
import 'tic_tac_toe_page.dart';

class TicTacToeSetupPage extends StatefulWidget {
  const TicTacToeSetupPage({super.key});

  @override
  State<TicTacToeSetupPage> createState() => _TicTacToeSetupPageState();
}

class _TicTacToeSetupPageState extends State<TicTacToeSetupPage>
    with TickerProviderStateMixin {
  GameMode selectedMode = GameMode.friend;
  Player selectedPlayer = Player.X;
  BotDifficulty selectedDifficulty = BotDifficulty.medium;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startGame() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => TicTacToePage(
          gameMode: selectedMode,
          humanPlayer: selectedPlayer,
          botDifficulty: selectedDifficulty,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F3460),
            ],
          ),
        ),
        child: SafeArea(
          bottom: true,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                children: [
                  // Custom App Bar
                  _buildCustomAppBar(),

                  // Content
                  Expanded(
                    child: Column(
                      children: [
                        // Scrollable content
                        Expanded(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Column(
                              children: [
                                // Title
                                _buildTitle(),

                                const SizedBox(height: 40),

                                // Game Mode Selection
                                _buildGameModeSelection(),

                                const SizedBox(height: 30),

                                // Bot options (only for bot mode)
                                if (selectedMode == GameMode.bot) ...[
                                  _buildDifficultySelection(),
                                  const SizedBox(height: 30),
                                  _buildPlayerSelection(),
                                  const SizedBox(height: 30),
                                ],
                              ],
                            ),
                          ),
                        ),

                        // Fixed bottom button area
                        Container(
                          padding: const EdgeInsets.all(20),
                          child: _buildStartButton(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Text(
              'टिक ट्याक टो सेटअप',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontFamily: 'NotoSansDevanagari',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.3),
                spreadRadius: 2,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.grid_3x3,
            color: Colors.white,
            size: 48,
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'खेल सेटअप गर्नुहोस्',
          style: TextStyle(
            color: Colors.white,
            fontSize: 28,
            fontFamily: 'NotoSansDevanagari',
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'आफ्नो मनपर्ने खेल मोड छान्नुहोस्',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 16,
            fontFamily: 'NotoSansDevanagari',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildGameModeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'खेल मोड छान्नुहोस्:',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontFamily: 'NotoSansDevanagari',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildModeCard(
                title: 'साथी विरुद्ध',
                subtitle: 'दुई खेलाडी',
                icon: Icons.people,
                mode: GameMode.friend,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModeCard(
                title: 'बोट विरुद्ध',
                subtitle: 'कम्प्युटर विरुद्ध',
                icon: Icons.smart_toy,
                mode: GameMode.bot,
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModeCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required GameMode mode,
    required Color color,
  }) {
    final isSelected = selectedMode == mode;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedMode = mode;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isSelected
                ? [color.withOpacity(0.3), color.withOpacity(0.1)]
                : [Colors.white.withOpacity(0.1), Colors.white.withOpacity(0.05)],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.white.withOpacity(0.2),
            width: isSelected ? 3 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    spreadRadius: 2,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isSelected ? [color, color.withOpacity(0.7)] : [Colors.grey, Colors.grey.withOpacity(0.7)],
                ),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 32,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? color : Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: isSelected ? color.withOpacity(0.8) : Colors.white.withOpacity(0.6),
                fontSize: 12,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDifficultySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'कठिनाई स्तर छान्नुहोस्:',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontFamily: 'NotoSansDevanagari',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildDifficultyCard(
                title: 'सजिलो',
                subtitle: 'जित्न सजिलो',
                difficulty: BotDifficulty.easy,
                color: Colors.green,
                icon: Icons.sentiment_very_satisfied,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDifficultyCard(
                title: 'मध्यम',
                subtitle: 'सन्तुलित',
                difficulty: BotDifficulty.medium,
                color: Colors.orange,
                icon: Icons.sentiment_neutral,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDifficultyCard(
                title: 'कठिन',
                subtitle: 'चुनौतीपूर्ण',
                difficulty: BotDifficulty.hard,
                color: Colors.red,
                icon: Icons.sentiment_very_dissatisfied,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDifficultyCard({
    required String title,
    required String subtitle,
    required BotDifficulty difficulty,
    required Color color,
    required IconData icon,
  }) {
    final isSelected = selectedDifficulty == difficulty;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedDifficulty = difficulty;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isSelected
                ? [color.withOpacity(0.3), color.withOpacity(0.1)]
                : [Colors.white.withOpacity(0.1), Colors.white.withOpacity(0.05)],
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: isSelected ? color : Colors.white.withOpacity(0.2),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.white.withOpacity(0.7),
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? color : Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: isSelected ? color.withOpacity(0.8) : Colors.white.withOpacity(0.6),
                fontSize: 10,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'तपाईं कुन खेल्नुहुन्छ?',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontFamily: 'NotoSansDevanagari',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildPlayerCard(
                symbol: 'X',
                player: Player.X,
                color: Colors.blue,
                description: 'पहिले खेल्नुहोस्',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildPlayerCard(
                symbol: 'O',
                player: Player.O,
                color: Colors.red,
                description: 'दोस्रो खेल्नुहोस्',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlayerCard({
    required String symbol,
    required Player player,
    required Color color,
    required String description,
  }) {
    final isSelected = selectedPlayer == player;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPlayer = player;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isSelected
                ? [color.withOpacity(0.3), color.withOpacity(0.1)]
                : [Colors.white.withOpacity(0.1), Colors.white.withOpacity(0.05)],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.white.withOpacity(0.2),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              symbol,
              style: TextStyle(
                color: isSelected ? color : Colors.white,
                fontSize: 48,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: isSelected ? color.withOpacity(0.8) : Colors.white.withOpacity(0.6),
                fontSize: 12,
                fontFamily: 'NotoSansDevanagari',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: ElevatedButton(
        onPressed: _startGame,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Ink(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.4),
                spreadRadius: 2,
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.play_arrow, color: Colors.white, size: 28),
                SizedBox(width: 12),
                Text(
                  'खेल सुरु गर्नुहोस्',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
