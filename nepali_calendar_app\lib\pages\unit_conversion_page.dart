import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/unit_conversion_model.dart';
import '../services/unit_conversion_service.dart';

class UnitConversionPage extends StatefulWidget {
  const UnitConversionPage({super.key});

  @override
  State<UnitConversionPage> createState() => _UnitConversionPageState();
}

class _UnitConversionPageState extends State<UnitConversionPage>
    with TickerProviderStateMixin {
  UnitType _selectedUnitType = UnitConversionData.unitTypes[0];
  Unit? _fromUnit;
  Unit? _toUnit;
  TextEditingController _inputController = TextEditingController();
  List<ConversionResult> _results = [];
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fromUnit = _selectedUnitType.units[0];
    _toUnit = _selectedUnitType.units[1];
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _inputController.dispose();
    super.dispose();
  }

  void _onUnitTypeChanged(UnitType unitType) {
    setState(() {
      _selectedUnitType = unitType;
      _fromUnit = unitType.units[0];
      _toUnit = unitType.units[1];
      _results.clear();
    });
    _performConversion();
  }

  void _performConversion() {
    if (_inputController.text.isEmpty || _fromUnit == null || _toUnit == null) {
      setState(() {
        _results.clear();
      });
      return;
    }

    final double? inputValue = double.tryParse(_inputController.text);
    if (inputValue == null || inputValue < 0) {
      setState(() {
        _results.clear();
      });
      return;
    }

    // Get all conversions for the current unit type
    final allResults = UnitConversionService.getAllConversions(
      inputValue, 
      _fromUnit!, 
      _selectedUnitType
    );

    // Add the primary conversion at the top
    final primaryResult = UnitConversionService.convert(inputValue, _fromUnit!, _toUnit!);
    
    setState(() {
      _results = [primaryResult, ...allResults.where((r) => r.unit.name != _toUnit!.name)];
    });

    HapticFeedback.lightImpact();
  }

  void _swapUnits() {
    if (_fromUnit != null && _toUnit != null) {
      setState(() {
        final temp = _fromUnit;
        _fromUnit = _toUnit;
        _toUnit = temp;
      });
      _performConversion();
      HapticFeedback.mediumImpact();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1E293B), // Slate 800
              Color(0xFF0F172A), // Slate 900
              Color(0xFF020617), // Slate 950
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom App Bar
              _buildCustomAppBar(),
              
              // Unit Type Selector
              _buildUnitTypeSelector(),
              
              // Conversion Input Section
              _buildConversionInput(),
              
              // Results Section
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildResultsSection(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
      child: Row(
        children: [
          // Back Button
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF334155),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () => Navigator.pop(context),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Title Section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'एकाइ रूपान्तरण',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 26,
                    fontFamily: 'NotoSansDevanagari',
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Unit Converter',
                  style: TextStyle(
                    color: const Color(0xFF94A3B8),
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Clear Button
          Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFEF4444).withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () {
                  _inputController.clear();
                  setState(() {
                    _results.clear();
                  });
                },
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(
                    Icons.clear_all_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitTypeSelector() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: UnitConversionData.unitTypes.length,
        itemBuilder: (context, index) {
          final unitType = UnitConversionData.unitTypes[index];
          final isSelected = _selectedUnitType.name == unitType.name;

          return GestureDetector(
            onTap: () => _onUnitTypeChanged(unitType),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                gradient: isSelected
                    ? const LinearGradient(colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)])
                    : null,
                color: isSelected ? null : const Color(0xFF334155),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? const Color(0xFF3B82F6) : const Color(0xFF475569),
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: const Color(0xFF3B82F6).withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ] : null,
              ),
              child: Center(
                child: Text(
                  unitType.nepaliName,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildConversionInput() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF475569), Color(0xFF334155)],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: const Color(0xFF64748B), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Input Field
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF475569)),
            ),
            child: TextField(
              controller: _inputController,
              onChanged: (value) => _performConversion(),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                hintText: 'Enter value to convert...',
                hintStyle: TextStyle(
                  color: const Color(0xFF94A3B8),
                  fontSize: 16,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Unit Selectors
          Row(
            children: [
              // From Unit
              Expanded(
                child: _buildUnitDropdown(
                  label: 'From',
                  nepaliLabel: 'बाट',
                  value: _fromUnit,
                  onChanged: (unit) {
                    setState(() {
                      _fromUnit = unit;
                    });
                    _performConversion();
                  },
                ),
              ),

              const SizedBox(width: 16),

              // Swap Button
              Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF10B981), Color(0xFF059669)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: _swapUnits,
                    child: const Padding(
                      padding: EdgeInsets.all(12),
                      child: Icon(
                        Icons.swap_horiz_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // To Unit
              Expanded(
                child: _buildUnitDropdown(
                  label: 'To',
                  nepaliLabel: 'मा',
                  value: _toUnit,
                  onChanged: (unit) {
                    setState(() {
                      _toUnit = unit;
                    });
                    _performConversion();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUnitDropdown({
    required String label,
    required String nepaliLabel,
    required Unit? value,
    required Function(Unit?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$nepaliLabel ($label)',
          style: const TextStyle(
            color: Color(0xFF94A3B8),
            fontSize: 14,
            fontWeight: FontWeight.w600,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFF1E293B),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF475569)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<Unit>(
              value: value,
              isExpanded: true,
              dropdownColor: const Color(0xFF1E293B),
              style: const TextStyle(color: Colors.white),
              items: _selectedUnitType.units.map((Unit unit) {
                return DropdownMenuItem<Unit>(
                  value: unit,
                  child: Text(
                    unit.nepaliName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: 'NotoSansDevanagari',
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsSection() {
    if (_results.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Results Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.calculate_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'रूपान्तरण परिणाम',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Results List
          Expanded(
            child: ListView.builder(
              itemCount: _results.length,
              itemBuilder: (context, index) {
                final result = _results[index];
                final isPrimary = index == 0;

                return _buildResultCard(result, isPrimary);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultCard(ConversionResult result, bool isPrimary) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: isPrimary
            ? const LinearGradient(
                colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
              )
            : const LinearGradient(
                colors: [Color(0xFF475569), Color(0xFF334155)],
              ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isPrimary ? const Color(0xFF3B82F6) : const Color(0xFF64748B),
          width: isPrimary ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (isPrimary ? const Color(0xFF3B82F6) : Colors.black).withOpacity(0.2),
            spreadRadius: isPrimary ? 2 : 1,
            blurRadius: isPrimary ? 12 : 8,
            offset: Offset(0, isPrimary ? 4 : 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Unit Name
          Row(
            children: [
              if (isPrimary)
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.star_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              if (isPrimary) const SizedBox(width: 8),
              Expanded(
                child: Text(
                  result.unit.nepaliName,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isPrimary ? 18 : 16,
                    fontWeight: isPrimary ? FontWeight.bold : FontWeight.w600,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ),
              // Copy Button
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: result.displayValue));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('${result.displayValue} copied!'),
                        duration: const Duration(seconds: 1),
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      Icons.copy_rounded,
                      color: Colors.white.withOpacity(0.8),
                      size: 18,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Value
          Text(
            result.fullDisplay,
            style: TextStyle(
              color: Colors.white,
              fontSize: isPrimary ? 24 : 20,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
            ),
          ),

          // Municipal Format for Nepali area units
          if (result.municipalFormat != null && result.municipalFormat!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  result.municipalFormat!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'NotoSansDevanagari',
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFF334155),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.calculate_outlined,
              color: Color(0xFF94A3B8),
              size: 48,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'मान प्रविष्ट गर्नुहोस्',
            style: TextStyle(
              fontSize: 20,
              fontFamily: 'NotoSansDevanagari',
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter a value to see conversions',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF94A3B8),
            ),
          ),
        ],
      ),
    );
  }
}
