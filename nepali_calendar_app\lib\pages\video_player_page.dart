import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/bhagavad_gita_model.dart';
import '../services/bhagavad_gita_service.dart';

class VideoPlayerPage extends StatefulWidget {
  final BhagavadGitaChapter chapter;

  const VideoPlayerPage({Key? key, required this.chapter}) : super(key: key);

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late WebViewController _webViewController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar
          },
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(_getYouTubeEmbedUrl()));
  }

  String _getYouTubeEmbedUrl() {
    final videoId = BhagavadGitaService.getVideoIdForChapter(widget.chapter.chapterNumber);
    return 'https://www.youtube.com/embed/$videoId?autoplay=1&rel=0&showinfo=0&modestbranding=1&controls=1&fs=1&playsinline=1';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'अध्याय ${widget.chapter.chapterNumber}',
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'NotoSansDevanagari',
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline, color: Colors.white),
            onPressed: () => _showChapterInfo(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => _webViewController.reload(),
          ),
          IconButton(
            icon: const Icon(Icons.open_in_new, color: Colors.white),
            onPressed: () => _openInYouTube(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Video Player WebView - Smaller size for mobile
          Container(
            height: 220, // Fixed smaller height for mobile
            child: Stack(
              children: [
                WebViewWidget(controller: _webViewController),
                if (_isLoading)
                  Container(
                    color: Colors.black,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            color: Colors.orange,
                          ),
                          SizedBox(height: 8),
                          Text(
                            'भिडियो लोड गर्दै...',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'NotoSansDevanagari',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Chapter Information - More space for content
          Expanded(
            child:
            Expanded(
              child: Container(
                color: Colors.white,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Chapter Title - More compact
                      Text(
                        widget.chapter.titleNepali,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'NotoSansDevanagari',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.chapter.titleSanskrit,
                        style: TextStyle(
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[700],
                          fontFamily: 'NotoSansDevanagari',
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        widget.chapter.titleEnglish,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Stats Row - More compact
                      Row(
                        children: [
                          _buildStatChip(
                            Icons.format_list_numbered,
                            '${widget.chapter.verseCount} श्लोकहरू',
                            Colors.blue,
                          ),
                          const SizedBox(width: 8),
                          _buildStatChip(
                            Icons.access_time,
                            widget.chapter.duration,
                            Colors.green,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Description
                      _buildSection(
                        'विवरण',
                        widget.chapter.description,
                        Icons.description,
                      ),
                      const SizedBox(height: 12),

                      // Summary
                      _buildSection(
                        'सारांश',
                        widget.chapter.summary,
                        Icons.summarize,
                      ),
                      const SizedBox(height: 16),

                      // Navigation Buttons
                      Row(
                        children: [
                          if (widget.chapter.chapterNumber > 1)
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _navigateToChapter(widget.chapter.chapterNumber - 1),
                                icon: const Icon(Icons.skip_previous),
                                label: const Text(
                                  'अघिल्लो अध्याय',
                                  style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                                ),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.orange,
                                  side: const BorderSide(color: Colors.orange),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                          if (widget.chapter.chapterNumber > 1 && widget.chapter.chapterNumber < 18)
                            const SizedBox(width: 12),
                          if (widget.chapter.chapterNumber < 18)
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _navigateToChapter(widget.chapter.chapterNumber + 1),
                                icon: const Icon(Icons.skip_next),
                                label: const Text(
                                  'अर्को अध्याय',
                                  style: TextStyle(fontFamily: 'NotoSansDevanagari'),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w600,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, String content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.orange, size: 16),
            const SizedBox(width: 6),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withOpacity(0.2)),
          ),
          child: Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              fontFamily: 'NotoSansDevanagari',
            ),
          ),
        ),
      ],
    );
  }

  void _showChapterInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'अध्याय ${widget.chapter.chapterNumber}',
          style: const TextStyle(fontFamily: 'NotoSansDevanagari'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.chapter.titleNepali,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${widget.chapter.verseCount} श्लोकहरू • ${widget.chapter.duration}',
              style: const TextStyle(
                color: Colors.grey,
                fontFamily: 'NotoSansDevanagari',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'बन्द गर्नुहोस्',
              style: TextStyle(fontFamily: 'NotoSansDevanagari'),
            ),
          ),
        ],
      ),
    );
  }

  void _showNextChapterDialog() {
    if (widget.chapter.chapterNumber < 18) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            'अर्को अध्याय',
            style: TextStyle(fontFamily: 'NotoSansDevanagari'),
          ),
          content: const Text(
            'के तपाईं अर्को अध्याय हेर्न चाहनुहुन्छ?',
            style: TextStyle(fontFamily: 'NotoSansDevanagari'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'होइन',
                style: TextStyle(fontFamily: 'NotoSansDevanagari'),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _navigateToChapter(widget.chapter.chapterNumber + 1);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
              child: const Text(
                'हो',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'NotoSansDevanagari',
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  void _openInYouTube() async {
    final videoId = BhagavadGitaService.getVideoIdForChapter(widget.chapter.chapterNumber);
    final urls = [
      'youtube://watch?v=$videoId',
      'https://www.youtube.com/watch?v=$videoId',
      'https://m.youtube.com/watch?v=$videoId',
    ];

    bool launched = false;
    for (String url in urls) {
      try {
        if (await canLaunchUrl(Uri.parse(url))) {
          await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
          launched = true;
          break;
        }
      } catch (e) {
        continue;
      }
    }

    if (!launched && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('YouTube खोल्न सकिएन'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _navigateToChapter(int chapterNumber) {
    final chapter = BhagavadGitaService.getChapter(chapterNumber);
    if (chapter != null) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerPage(chapter: chapter),
        ),
      );
    }
  }
}
