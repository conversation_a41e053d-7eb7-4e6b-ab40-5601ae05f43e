import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/user_data_service.dart';

class WesternMatchPage extends StatefulWidget {
  const WesternMatchPage({Key? key}) : super(key: key);

  @override
  State<WesternMatchPage> createState() => _WesternMatchPageState();
}

class _WesternMatchPageState extends State<WesternMatchPage> {
  bool _isLoading = false;
  dynamic _westernResult;
  String? _error;
  UserData? _selectedBoy;
  UserData? _selectedGirl;
  List<UserData> _allUsers = [];
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _resultKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final users = await UserDataService.loadUserDataList();
      setState(() {
        _allUsers = users;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading users: $e';
      });
    }
  }

  Future<void> _fetchWesternMatch() async {
    if (_selectedBoy == null || _selectedGirl == null) {
      setState(() {
        _error = 'कृपया केटा र केटी दुवै छान्नुहोस्';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getWesternMatch(_selectedBoy!, _selectedGirl!);

      setState(() {
        _westernResult = result;
        _isLoading = false;
      });

      // Auto-scroll to result after data loads
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_resultKey.currentContext != null) {
          Scrollable.ensureVisible(
            _resultKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    } catch (e) {
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'पश्चिमी राशि मिलान',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildInfoCard(),
                const SizedBox(height: 12),
                _buildUserSelectionCard(),
                const SizedBox(height: 12),
                if (_selectedBoy != null && _selectedGirl != null)
                  _buildAnalyzeButton(),
                const SizedBox(height: 12),
                if (_isLoading) _buildLoadingWidget(),
                if (_error != null) _buildErrorWidget(),
                if (_westernResult != null)
                  Container(
                    key: _resultKey,
                    child: _buildResultWidget(),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.public,
                  color: Color(0xFF2E7D32),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'पश्चिमी राशि मिलान',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B5E20),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'पश्चिमी ज्योतिष राशि आधारित मिलान',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelectionCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'व्यक्ति छान्नुहोस्',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1B5E20),
            ),
          ),
          const SizedBox(height: 12),

          // Boy and Girl Selection in Same Row
          Row(
            children: [
              // Boy Selection
              Expanded(
                child: _buildUserSelector(
                  title: 'केटा छान्नुहोस्',
                  icon: Icons.male,
                  selectedUser: _selectedBoy,
                  onUserSelected: (user) {
                    setState(() {
                      _selectedBoy = user;
                      _westernResult = null; // Clear previous results
                    });
                  },
                ),
              ),

              const SizedBox(width: 12),

              // Girl Selection
              Expanded(
                child: _buildUserSelector(
                  title: 'केटी छान्नुहोस्',
                  icon: Icons.female,
                  selectedUser: _selectedGirl,
                  onUserSelected: (user) {
                    setState(() {
                      _selectedGirl = user;
                      _westernResult = null; // Clear previous results
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUserSelector({
    required String title,
    required IconData icon,
    required UserData? selectedUser,
    required Function(UserData?) onUserSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: const Color(0xFF2E7D32), size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7D32),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFF4CAF50)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<UserData>(
              value: selectedUser,
              hint: Text(
                'छान्नुहोस्...',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              isExpanded: true,
              items: _allUsers.map((user) {
                return DropdownMenuItem<UserData>(
                  value: user,
                  child: Text(
                    '${user.name} (${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day})',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF1B5E20),
                    ),
                  ),
                );
              }).toList(),
              onChanged: onUserSelected,
              dropdownColor: Colors.white,
              icon: const Icon(
                Icons.arrow_drop_down,
                color: Color(0xFF4CAF50),
              ),
            ),
          ),
        ),

        if (selectedUser != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF2E7D32), size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedUser.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B5E20),
                        ),
                      ),
                      Text(
                        'जन्म: ${selectedUser.birthDateBS.year}/${selectedUser.birthDateBS.month}/${selectedUser.birthDateBS.day} ${selectedUser.birthTime}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      Text(
                        'स्थान: ${selectedUser.district}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAnalyzeButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _fetchWesternMatch,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4CAF50),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 3,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.public, size: 24),
            const SizedBox(width: 12),
            Text(
              'पश्चिमी राशि मिलान विश्लेषण गर्नुहोस्',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            strokeWidth: 3,
          ),
          SizedBox(height: 20),
          Text(
            'पश्चिमी राशि मिलान विश्लेषण गर्दै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF2E7D32),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'कृपया पर्खनुहोस्',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF388E3C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchWesternMatch,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with couple info
          _buildCoupleHeader(),
          const SizedBox(height: 20),

          if (_westernResult != null)
            _buildWesternContent(),
        ],
      ),
    );
  }

  Widget _buildCoupleHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE8F5E8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Row(
        children: [
          // Boy info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.male, color: Color(0xFF2196F3), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedBoy?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedBoy?.birthDateBS.year}/${_selectedBoy?.birthDateBS.month}/${_selectedBoy?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // Globe icon for Western
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.public,
              color: Colors.white,
              size: 24,
            ),
          ),

          // Girl info
          Expanded(
            child: Column(
              children: [
                const Icon(Icons.female, color: Color(0xFFE91E63), size: 32),
                const SizedBox(height: 8),
                Text(
                  _selectedGirl?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${_selectedGirl?.birthDateBS.year}/${_selectedGirl?.birthDateBS.month}/${_selectedGirl?.birthDateBS.day}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWesternContent() {
    if (_westernResult is Map<String, dynamic>) {
      final data = _westernResult as Map<String, dynamic>;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display all analysis results
          ...data.entries.map((entry) {
            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFF1F8E9),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF4CAF50).withOpacity(0.3),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getAnalysisIcon(entry.key),
                        color: const Color(0xFF2E7D32),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _translateAnalysisKey(entry.key),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildValueWidget(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 32),
            const SizedBox(height: 12),
            const Text(
              'अप्रत्याशित डेटा ढाँचा',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'प्राप्त डेटा: ${_westernResult.toString()}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      );
    }
  }
  Widget _buildValueWidget(dynamic value) {
    if (value is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${_translateKey(entry.key)}:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF2E7D32),
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    entry.value.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else if (value is List) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: value.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4CAF50),
                  ),
                ),
                Expanded(
                  child: Text(
                    item.toString(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    } else {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontSize: 18,
          color: Color(0xFF1B5E20),
          height: 1.4,
        ),
      );
    }
  }

  IconData _getAnalysisIcon(String key) {
    switch (key.toLowerCase()) {
      case 'compatibility':
        return Icons.favorite;
      case 'boy_sign':
        return Icons.male;
      case 'girl_sign':
        return Icons.female;
      case 'match_percentage':
        return Icons.percent;
      case 'relationship_type':
        return Icons.psychology;
      case 'love_compatibility':
        return Icons.favorite_border;
      case 'friendship_compatibility':
        return Icons.handshake;
      case 'communication':
        return Icons.chat;
      case 'trust':
        return Icons.security;
      case 'emotional_connection':
        return Icons.sentiment_satisfied;
      case 'physical_attraction':
        return Icons.flash_on;
      case 'overall_rating':
        return Icons.star;
      case 'recommendation':
        return Icons.lightbulb;
      case 'analysis':
        return Icons.analytics;
      default:
        return Icons.public;
    }
  }

  String _translateAnalysisKey(String key) {
    switch (key.toLowerCase()) {
      case 'compatibility':
        return 'मिलान';
      case 'boy_sign':
        return 'केटाको राशि';
      case 'girl_sign':
        return 'केटीको राशि';
      case 'match_percentage':
        return 'मिलान प्रतिशत';
      case 'relationship_type':
        return 'सम्बन्ध प्रकार';
      case 'love_compatibility':
        return 'प्रेम मिलान';
      case 'friendship_compatibility':
        return 'मित्रता मिलान';
      case 'communication':
        return 'संवाद';
      case 'trust':
        return 'विश्वास';
      case 'emotional_connection':
        return 'भावनात्मक जडान';
      case 'physical_attraction':
        return 'शारीरिक आकर्षण';
      case 'overall_rating':
        return 'समग्र मूल्याङ्कन';
      case 'recommendation':
        return 'सिफारिस';
      case 'analysis':
        return 'विश्लेषण';
      case 'western_astrology':
        return 'पश्चिमी ज्योतिष';
      case 'zodiac_compatibility':
        return 'राशि मिलान';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _translateKey(String key) {
    switch (key.toLowerCase()) {
      case 'sign':
        return 'राशि';
      case 'element':
        return 'तत्व';
      case 'quality':
        return 'गुण';
      case 'ruling_planet':
        return 'स्वामी ग्रह';
      case 'compatibility_score':
        return 'मिलान अंक';
      case 'percentage':
        return 'प्रतिशत';
      case 'description':
        return 'विवरण';
      case 'recommendation':
        return 'सिफारिस';
      case 'analysis':
        return 'विश्लेषण';
      case 'result':
        return 'परिणाम';
      case 'status':
        return 'स्थिति';
      case 'rating':
        return 'मूल्याङ्कन';
      case 'excellent':
        return 'उत्कृष्ट';
      case 'good':
        return 'राम्रो';
      case 'average':
        return 'मध्यम';
      case 'poor':
        return 'कम';
      case 'high':
        return 'उच्च';
      case 'medium':
        return 'मध्यम';
      case 'low':
        return 'कम';
      case 'fire':
        return 'अग्नि';
      case 'earth':
        return 'पृथ्वी';
      case 'air':
        return 'वायु';
      case 'water':
        return 'जल';
      case 'cardinal':
        return 'चल';
      case 'fixed':
        return 'स्थिर';
      case 'mutable':
        return 'द्विस्वभाव';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }
}
