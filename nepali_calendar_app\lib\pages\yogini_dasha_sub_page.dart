import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../services/vedic_astro_api_service.dart';
import '../services/error_handler_service.dart';
import '../widgets/offline_content_widget.dart';

class YoginiDashaSubPage extends StatefulWidget {
  final UserData user;

  const YoginiDashaSubPage({Key? key, required this.user}) : super(key: key);

  @override
  State<YoginiDashaSubPage> createState() => _YoginiDashaSubPageState();
}

class _YoginiDashaSubPageState extends State<YoginiDashaSubPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _yoginiDashaSubResult;
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchYoginiDashaSub();
  }

  UserData get user => widget.user;

  Future<void> _fetchYoginiDashaSub() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await VedicAstroApiService.getYoginiDashaSub(user);
      
      setState(() {
        _yoginiDashaSubResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = ErrorHandlerService.getErrorMessage(e);
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text(
          'योगिनी दशा उप',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchYoginiDashaSub,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8F9FA), Color(0xFFE8F5E8)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildUserInfoCard(),
                const SizedBox(height: 20),
                if (_isLoading) LoadingWidget(
                  message: 'योगिनी दशा उप विश्लेषण गर्दै...',
                  featureName: 'योगिनी दशा उप',
                ),
                if (_error != null) ErrorDisplayWidget(
                  errorMessage: _error!,
                  onRetry: _fetchYoginiDashaSub,
                  featureName: 'योगिनी दशा उप',
                ),
                if (_yoginiDashaSubResult != null) _buildResultWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          CircleAvatar(
            radius: 35,
            backgroundColor: const Color(0xFF4CAF50),
            child: Text(
              user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 28,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B5E20),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  'जन्म: ${user.birthDateBS.year}/${user.birthDateBS.month}/${user.birthDateBS.day} • ${user.birthTime}',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF388E3C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
          ),
          SizedBox(height: 20),
          Text(
            'योगिनी दशा उप विश्लेषण गरिँदै...',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF1B5E20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'त्रुटि भयो',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF1B5E20),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _fetchYoginiDashaSub,
            icon: const Icon(Icons.refresh),
            label: const Text(
              'पुनः प्रयास गर्नुहोस्',
              style: TextStyle(fontSize: 16),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultWidget() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.spa,
                color: Color(0xFF2E7D32),
                size: 36,
              ),
              SizedBox(width: 12),
              Text(
                'योगिनी दशा उप विवरण',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B5E20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Display all the result data
          if (_yoginiDashaSubResult != null)
            _buildYoginiDashaSubContent(),
        ],
      ),
    );
  }

  Widget _buildYoginiDashaSubContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display each key-value pair from the result
        ..._yoginiDashaSubResult!.entries.map((entry) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFFF1F8E9),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getIconForKey(entry.key),
                      color: const Color(0xFF2E7D32),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _formatKey(entry.key),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF4CAF50).withOpacity(0.2),
                    ),
                  ),
                  child: Text(
                    _formatValue(entry.value),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1B5E20),
                      height: 1.6,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  String _formatKey(String key) {
    // Convert API keys to Nepali labels
    switch (key.toLowerCase()) {
      case 'yogini_dasha_sub':
        return 'योगिनी दशा उप';
      case 'yogini_sub_dasha_list':
        return 'योगिनी उप दशा सूची';
      case 'sub_dasha_list':
        return 'उप दशा सूची';
      case 'sub_dasha_order':
        return 'उप दशा क्रम';
      case 'sub_dasha_dates':
        return 'उप दशा मितिहरू';
      case 'sub_dasha_start_dates':
        return 'उप दशा शुरुवात मितिहरू';
      case 'sub_dasha_end_dates':
        return 'उप दशा समाप्ति मितिहरू';
      case 'main_yogini_dasha':
        return 'मुख्य योगिनी दशा';
      case 'current_sub_dasha':
        return 'वर्तमान उप दशा';
      case 'next_sub_dasha':
        return 'अर्को उप दशा';
      case 'sub_period_duration':
        return 'उप अवधि समयावधि';
      case 'remaining_period':
        return 'बाँकी अवधि';
      case 'yogini_name':
        return 'योगिनी नाम';
      case 'start_date':
        return 'शुरुवात मिति';
      case 'end_date':
        return 'समाप्ति मिति';
      case 'duration':
        return 'अवधि';
      case 'effects':
        return 'प्रभावहरू';
      case 'predictions':
        return 'भविष्यवाणी';
      case 'remedies':
        return 'उपायहरू';
      case 'favorable_activities':
        return 'अनुकूल गतिविधिहरू';
      case 'unfavorable_activities':
        return 'प्रतिकूल गतिविधिहरू';
      case 'bot_response':
        return 'विस्तृत विश्लेषण';
      default:
        return key.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _formatValue(dynamic value) {
    if (value is List) {
      // Handle nested arrays for yogini sub-dasha sequences
      if (value.isNotEmpty && value[0] is List) {
        return value.asMap().entries.map((entry) {
          final index = entry.key;
          final subList = entry.value as List;
          return '${index + 1}. ${subList.join(', ')}';
        }).join('\n\n');
      } else {
        return value.map((item) => '• $item').join('\n');
      }
    } else if (value is Map) {
      return value.entries
          .map((e) => '${_formatKey(e.key.toString())}: ${_formatValue(e.value)}')
          .join('\n');
    } else {
      return value.toString();
    }
  }

  IconData _getIconForKey(String key) {
    switch (key.toLowerCase()) {
      case 'yogini_dasha':
      case 'current_yogini_dasha':
        return Icons.timeline;
      case 'lord':
      case 'planet':
        return Icons.public;
      case 'start_date':
      case 'from':
        return Icons.play_arrow;
      case 'end_date':
      case 'to':
        return Icons.stop;
      case 'duration':
        return Icons.schedule;
      case 'predictions':
        return Icons.psychology;
      case 'effects':
        return Icons.star;
      case 'remedies':
        return Icons.healing;
      case 'favorable':
        return Icons.thumb_up;
      case 'unfavorable':
        return Icons.thumb_down;
      default:
        return Icons.info;
    }
  }
}
