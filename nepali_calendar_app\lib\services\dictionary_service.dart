import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dictionary_model.dart';

class DictionaryService {
  static const String baseUrl = 'https://api.dictionaryapi.dev/api/v2/entries/en';
  static const String cacheKeyPrefix = 'dictionary_cache_';
  static const String historyKey = 'dictionary_search_history';
  static const Duration cacheExpiry = Duration(days: 7);

  // Search for word definition
  static Future<List<DictionaryEntry>> searchWord(String word) async {
    if (word.trim().isEmpty) {
      throw Exception('कृपया खोज्न चाहेको शब्द लेख्नुहोस्');
    }

    final cleanWord = word.trim().toLowerCase();
    
    try {
      // Try to get from cache first
      final cachedResult = await _getCachedResult(cleanWord);
      if (cachedResult != null) {
        await _addToSearchHistory(cleanWord);
        return cachedResult;
      }

      // Fetch from API
      final url = '$baseUrl/$cleanWord';
      print('🔍 Searching dictionary for: $cleanWord');
      print('📡 API URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'NepaliCalendarApp/1.0',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 15));

      print('📊 Response status: ${response.statusCode}');
      print('📄 Response length: ${response.body.length}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        final List<DictionaryEntry> entries = jsonData
            .map((entry) => DictionaryEntry.fromJson(entry))
            .toList();

        if (entries.isNotEmpty) {
          // Cache the result
          await _cacheResult(cleanWord, entries);
          // Add to search history
          await _addToSearchHistory(cleanWord);
          
          print('✅ Found ${entries.length} dictionary entries for "$cleanWord"');
          return entries;
        } else {
          throw Exception('शब्द फेला परेन');
        }
      } else if (response.statusCode == 404) {
        throw Exception('शब्द फेला परेन। कृपया सही स्पेलिङ जाँच गर्नुहोस्।');
      } else {
        throw Exception('डाटा लोड गर्न सकिएन। कृपया फेरि प्रयास गर्नुहोस्।');
      }
    } on SocketException {
      throw Exception('इन्टरनेट जडान छैन। कृपया जडान जाँच गर्नुहोस्।');
    } on http.ClientException {
      throw Exception('नेटवर्क त्रुटि। कृपया फेरि प्रयास गर्नुहोस्।');
    } catch (e) {
      if (e.toString().contains('शब्द फेला परेन') || 
          e.toString().contains('इन्टरनेट जडान छैन') ||
          e.toString().contains('कृपया खोज्न चाहेको शब्द लेख्नुहोस्')) {
        rethrow;
      }
      print('❌ Dictionary search error: $e');
      throw Exception('शब्दकोश सेवामा समस्या छ। कृपया पछि प्रयास गर्नुहोस्।');
    }
  }

  // Cache management
  static Future<void> _cacheResult(String word, List<DictionaryEntry> entries) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'entries': entries.map((entry) => {
          'word': entry.word,
          'phonetic': entry.phonetic,
          'meanings': entry.meanings.map((meaning) => {
            'partOfSpeech': meaning.partOfSpeech,
            'definitions': meaning.definitions.map((def) => {
              'definition': def.definition,
              'example': def.example,
              'synonyms': def.synonyms,
              'antonyms': def.antonyms,
            }).toList(),
            'synonyms': meaning.synonyms,
            'antonyms': meaning.antonyms,
          }).toList(),
          'phonetics': entry.phonetics,
          'sourceUrl': entry.sourceUrl,
        }).toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      await prefs.setString('$cacheKeyPrefix$word', json.encode(cacheData));
      print('💾 Cached dictionary result for: $word');
    } catch (e) {
      print('❌ Failed to cache dictionary result: $e');
    }
  }

  static Future<List<DictionaryEntry>?> _getCachedResult(String word) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString('$cacheKeyPrefix$word');
      
      if (cachedData != null) {
        final Map<String, dynamic> cacheMap = json.decode(cachedData);
        final DateTime cacheTime = DateTime.parse(cacheMap['timestamp']);
        
        // Check if cache is still valid
        if (DateTime.now().difference(cacheTime) < cacheExpiry) {
          final List<dynamic> entriesData = cacheMap['entries'];
          final List<DictionaryEntry> entries = entriesData
              .map((entry) => DictionaryEntry.fromJson(entry))
              .toList();
          
          print('💾 Using cached dictionary result for: $word');
          return entries;
        } else {
          // Remove expired cache
          await prefs.remove('$cacheKeyPrefix$word');
        }
      }
    } catch (e) {
      print('❌ Failed to get cached dictionary result: $e');
    }
    return null;
  }

  // Search history management
  static Future<void> _addToSearchHistory(String word) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(historyKey) ?? '[]';
      final List<dynamic> historyList = json.decode(historyJson);
      
      // Remove if already exists
      historyList.removeWhere((item) => item['word'] == word);
      
      // Add to beginning
      historyList.insert(0, {
        'word': word,
        'searchTime': DateTime.now().toIso8601String(),
      });
      
      // Keep only last 50 searches
      if (historyList.length > 50) {
        historyList.removeRange(50, historyList.length);
      }
      
      await prefs.setString(historyKey, json.encode(historyList));
    } catch (e) {
      print('❌ Failed to add to search history: $e');
    }
  }

  static Future<List<DictionarySearchHistory>> getSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(historyKey) ?? '[]';
      final List<dynamic> historyList = json.decode(historyJson);
      
      return historyList
          .map((item) => DictionarySearchHistory.fromJson(item))
          .toList();
    } catch (e) {
      print('❌ Failed to get search history: $e');
      return [];
    }
  }

  static Future<void> clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(historyKey);
    } catch (e) {
      print('❌ Failed to clear search history: $e');
    }
  }

  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final cacheKeys = keys.where((key) => key.startsWith(cacheKeyPrefix));
      
      for (final key in cacheKeys) {
        await prefs.remove(key);
      }
      
      print('🗑️ Cleared dictionary cache');
    } catch (e) {
      print('❌ Failed to clear dictionary cache: $e');
    }
  }

  // Get word suggestions (simple implementation)
  static List<String> getWordSuggestions(String query) {
    if (query.length < 2) return [];
    
    // Common English words for suggestions
    final commonWords = [
      'hello', 'world', 'computer', 'phone', 'house', 'water', 'food', 'book',
      'school', 'work', 'family', 'friend', 'love', 'happy', 'sad', 'good',
      'bad', 'big', 'small', 'new', 'old', 'beautiful', 'ugly', 'fast', 'slow',
      'hot', 'cold', 'light', 'dark', 'easy', 'hard', 'strong', 'weak',
      'rich', 'poor', 'young', 'old', 'tall', 'short', 'fat', 'thin',
      'smart', 'stupid', 'kind', 'mean', 'funny', 'serious', 'quiet', 'loud'
    ];
    
    return commonWords
        .where((word) => word.toLowerCase().startsWith(query.toLowerCase()))
        .take(5)
        .toList();
  }
}
