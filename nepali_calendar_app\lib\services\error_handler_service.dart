import 'dart:io';
import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class ErrorHandlerService {
  static const String _noInternetMessage = 'इन्टरनेट जडान आवश्यक छ। कृपया जडान जाँच गर्नुहोस्।';
  static const String _timeoutMessage = 'समय सकियो। कृपया पुनः प्रयास गर्नुहोस्।';
  static const String _serverErrorMessage = 'सर्भर समस्या। केही समयपछि प्रयास गर्नुहोस्।';
  static const String _dataErrorMessage = 'डेटा प्रोसेसिङ त्रुटि भयो।';
  static const String _genericErrorMessage = 'केही समस्या भयो। कृपया पुनः प्रयास गर्नुहोस्।';
  static const String _offlineMessage = 'तपाईं अफलाइन हुनुहुन्छ। इन्टरनेट जडान गरेर पुनः प्रयास गर्नुहोस्।';

  /// Check if device has internet connectivity
  static Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }
      
      // Additional check by trying to reach a reliable server
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get user-friendly error message from exception
  static String getErrorMessage(dynamic error) {
    // Log detailed error for debugging (only in debug mode)
    if (kDebugMode) {
      print('Detailed Error: $error');
    }

    // Return user-friendly messages based on error type
    if (error is SocketException) {
      return _noInternetMessage;
    } else if (error is TimeoutException) {
      return _timeoutMessage;
    } else if (error is FormatException) {
      return _dataErrorMessage;
    } else if (error is HttpException) {
      return _serverErrorMessage;
    } else if (error.toString().contains('Failed to fetch') || 
               error.toString().contains('HTTP Error')) {
      return _serverErrorMessage;
    } else if (error.toString().contains('No internet') || 
               error.toString().contains('network')) {
      return _noInternetMessage;
    } else {
      return _genericErrorMessage;
    }
  }

  /// Handle API call with proper error handling and connectivity check
  static Future<T> handleApiCall<T>(
    Future<T> Function() apiCall, {
    String? customOfflineMessage,
  }) async {
    // Check connectivity first
    if (!await hasInternetConnection()) {
      throw Exception(customOfflineMessage ?? _offlineMessage);
    }

    try {
      return await apiCall();
    } on SocketException {
      throw Exception(_noInternetMessage);
    } on TimeoutException {
      throw Exception(_timeoutMessage);
    } on FormatException {
      throw Exception(_dataErrorMessage);
    } on HttpException {
      throw Exception(_serverErrorMessage);
    } catch (e) {
      // Log for debugging but don't expose details to user
      if (kDebugMode) {
        print('API Call Error: $e');
      }
      throw Exception(_genericErrorMessage);
    }
  }

  /// Get offline message
  static String get offlineMessage => _offlineMessage;

  /// Get no internet message
  static String get noInternetMessage => _noInternetMessage;

  /// Sanitize error message to remove sensitive information
  static String sanitizeErrorMessage(String errorMessage) {
    // Remove API keys, URLs, and other sensitive information
    String sanitized = errorMessage;
    
    // Remove API key patterns
    sanitized = sanitized.replaceAll(RegExp(r'api_key=[^&\s]+'), 'api_key=***');
    sanitized = sanitized.replaceAll(RegExp(r'key=[^&\s]+'), 'key=***');
    
    // Remove full URLs
    sanitized = sanitized.replaceAll(RegExp(r'https?://[^\s]+'), '[URL]');
    
    // Remove specific API endpoints
    sanitized = sanitized.replaceAll(RegExp(r'api\.vedicastroapi\.com[^\s]*'), '[API_ENDPOINT]');
    
    // If the message still contains sensitive info, return generic message
    if (sanitized.contains('api') || sanitized.contains('http') || sanitized.contains('.com')) {
      return _genericErrorMessage;
    }
    
    return sanitized;
  }

  /// Check if error message contains sensitive information
  static bool containsSensitiveInfo(String message) {
    final sensitivePatterns = [
      'api_key',
      'http://',
      'https://',
      '.com',
      'api.vedicastroapi',
      'YOUR_API_KEY',
    ];
    
    return sensitivePatterns.any((pattern) => 
      message.toLowerCase().contains(pattern.toLowerCase()));
  }
}
