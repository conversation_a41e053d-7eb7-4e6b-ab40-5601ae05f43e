import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/forex_model.dart';

class ForexService {
  static const String baseUrl = 'https://www.nrb.org.np/api/forex/v1';
  static const String cacheKey = 'forex_data_cache';
  static const String cacheTimeKey = 'forex_cache_time';
  static const int cacheValidityHours = 6; // Cache valid for 6 hours

  static Future<ForexResponse?> getLatestRates({bool forceRefresh = true}) async {
    try {
      // Always fetch fresh data first

      // Fetch fresh data from API
      final today = DateTime.now();
      final yesterday = today.subtract(const Duration(days: 1));

      final fromDate = _formatDate(yesterday);
      final toDate = _formatDate(today);

      final url = Uri.parse('$baseUrl/rates').replace(queryParameters: {
        'page': '1',
        'per_page': '100', // Get more data for better caching
        'from': fromDate,
        'to': toDate,
      });

      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final forexResponse = ForexResponse.fromJson(jsonData);

        // Cache the successful response
        await _cacheData(forexResponse);
        print('Fresh FOREX data fetched and cached');

        return forexResponse;
      } else {
        print('FOREX API Error: ${response.statusCode}');
        // Return cached data if API fails
        return await _getCachedData();
      }
    } catch (e) {
      print('FOREX Service Error: $e');
      // Return cached data if network fails
      return await _getCachedData();
    }
  }

  static Future<ForexResponse?> getRatesForDateRange({
    required DateTime from,
    required DateTime to,
    int page = 1,
    int perPage = 10,
  }) async {
    try {
      final url = Uri.parse('$baseUrl/rates').replace(queryParameters: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        'from': _formatDate(from),
        'to': _formatDate(to),
      });

      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return ForexResponse.fromJson(jsonData);
      } else {
        print('FOREX API Error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('FOREX Service Error: $e');
      return null;
    }
  }

  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Cache management methods
  static Future<void> _cacheData(ForexResponse data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(data.toJson());
      await prefs.setString(cacheKey, jsonString);
      await prefs.setInt(cacheTimeKey, DateTime.now().millisecondsSinceEpoch);
      print('FOREX data cached successfully');
    } catch (e) {
      print('Error caching FOREX data: $e');
    }
  }

  static Future<ForexResponse?> _getCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(cacheKey);
      final cacheTime = prefs.getInt(cacheTimeKey);

      if (cachedJson != null && cacheTime != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - cacheTime;
        final cacheValidityMs = cacheValidityHours * 60 * 60 * 1000;

        if (cacheAge < cacheValidityMs) {
          final jsonData = json.decode(cachedJson);
          return ForexResponse.fromJson(jsonData);
        } else {
          print('Cached FOREX data expired');
        }
      }
    } catch (e) {
      print('Error reading cached FOREX data: $e');
    }
    return null;
  }

  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(cacheKey);
      await prefs.remove(cacheTimeKey);
      print('FOREX cache cleared');
    } catch (e) {
      print('Error clearing FOREX cache: $e');
    }
  }

  static Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheTime = prefs.getInt(cacheTimeKey);

      if (cacheTime != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - cacheTime;
        final cacheValidityMs = cacheValidityHours * 60 * 60 * 1000;
        return cacheAge < cacheValidityMs;
      }
    } catch (e) {
      print('Error checking cache validity: $e');
    }
    return false;
  }

  // Get popular currencies from the rates
  static List<CurrencyRate> getPopularCurrencyRates(List<CurrencyRate> allRates) {
    final popularISO3 = ['USD', 'EUR', 'GBP', 'JPY', 'INR', 'CNY', 'AUD', 'CAD'];
    
    return allRates.where((rate) {
      return popularISO3.contains(rate.currency.iso3);
    }).toList();
  }

  // Format currency rate for display
  static String formatRate(String rate) {
    try {
      final double rateValue = double.parse(rate);
      return rateValue.toStringAsFixed(2);
    } catch (e) {
      return rate;
    }
  }

  // Get currency flag emoji
  static String getCurrencyFlag(String iso3) {
    switch (iso3) {
      case 'USD':
        return '🇺🇸';
      case 'EUR':
        return '🇪🇺';
      case 'GBP':
        return '🇬🇧';
      case 'JPY':
        return '🇯🇵';
      case 'INR':
        return '🇮🇳';
      case 'CNY':
        return '🇨🇳';
      case 'AUD':
        return '🇦🇺';
      case 'CAD':
        return '🇨🇦';
      case 'CHF':
        return '🇨🇭';
      case 'SGD':
        return '🇸🇬';
      case 'SEK':
        return '🇸🇪';
      case 'DKK':
        return '🇩🇰';
      case 'NOK':
        return '🇳🇴';
      case 'THB':
        return '🇹🇭';
      case 'MYR':
        return '🇲🇾';
      case 'KRW':
        return '🇰🇷';
      case 'HKD':
        return '🇭🇰';
      case 'AED':
        return '🇦🇪';
      case 'SAR':
        return '🇸🇦';
      case 'QAR':
        return '🇶🇦';
      case 'KWD':
        return '🇰🇼';
      case 'BHD':
        return '🇧🇭';
      default:
        return '💱';
    }
  }

  // Get currency symbol
  static String getCurrencySymbol(String iso3) {
    switch (iso3) {
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      case 'INR':
        return '₹';
      case 'CNY':
        return '¥';
      case 'AUD':
        return 'A\$';
      case 'CAD':
        return 'C\$';
      case 'CHF':
        return 'Fr';
      case 'SGD':
        return 'S\$';
      case 'SEK':
        return 'kr';
      case 'DKK':
        return 'kr';
      case 'NOK':
        return 'kr';
      case 'THB':
        return '฿';
      case 'MYR':
        return 'RM';
      case 'KRW':
        return '₩';
      case 'HKD':
        return 'HK\$';
      case 'AED':
        return 'د.إ';
      case 'SAR':
        return '﷼';
      case 'QAR':
        return '﷼';
      case 'KWD':
        return 'د.ك';
      case 'BHD':
        return '.د.ب';
      default:
        return '';
    }
  }
}
