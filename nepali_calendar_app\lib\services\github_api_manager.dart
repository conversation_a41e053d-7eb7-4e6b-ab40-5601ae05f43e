import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class GitHubApiManager {
  static String _currentApiKey = 'a9d9bb9b-95be-528a-8da4-5a3b1d87faf5';
  static String _baseUrl = 'https://api.vedicastroapi.com/v3-json';
  
  // Your GitHub raw URL
  static const String _configUrl = 'https://raw.githubusercontent.com/ujjwal98466/nepali-calendar-config/main/testonly.json';
  
  static bool _initialized = false;
  static DateTime? _lastUpdate;
  
  /// Initialize the API key manager
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Load saved configuration from local storage
      await _loadLocalConfig();

      // Always update from GitHub on app start
      print('🚀 App started - checking GitHub for latest API key...');
      try {
        await _updateFromGitHub();
        print('✅ GitHub update completed on startup');
      } catch (e) {
        print('⚠️ GitHub update failed on startup: $e');
      }

      _initialized = true;
      print('✅ GitHubApiManager initialized with key: ${_currentApiKey.substring(0, 8)}...');
    } catch (e) {
      print('❌ GitHubApiManager initialization error: $e');
      _initialized = true; // Continue with default key
    }
  }
  
  /// Get current API key
  static String get apiKey => _currentApiKey;
  
  /// Get base URL
  static String get baseUrl => _baseUrl;
  
  /// Force update from GitHub
  static Future<bool> updateFromGitHub() async {
    return await _updateFromGitHub();
  }
  
  /// Load configuration from local storage
  static Future<void> _loadLocalConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _currentApiKey = prefs.getString('vedic_api_key') ?? _currentApiKey;
      _baseUrl = prefs.getString('api_base_url') ?? _baseUrl;
      
      final lastUpdateStr = prefs.getString('last_update');
      if (lastUpdateStr != null) {
        _lastUpdate = DateTime.tryParse(lastUpdateStr);
      }
      
      print('📱 Loaded local config - API key: ${_currentApiKey.substring(0, 8)}...');
    } catch (e) {
      print('❌ Error loading local config: $e');
    }
  }
  
  /// Update configuration from GitHub
  static Future<bool> _updateFromGitHub() async {
    try {
      // Always check GitHub for updates (no time limit)
      // This ensures fresh API key on every check
      
      print('🔄 Fetching config from GitHub...');
      
      final response = await http.get(
        Uri.parse(_configUrl),
        headers: {'Cache-Control': 'no-cache'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final config = json.decode(response.body);
        print('📥 Received config from GitHub');
        
        bool updated = false;
        
        // Update API key
        final newApiKey = config['vedic_api_key'] as String?;
        if (newApiKey != null && newApiKey.isNotEmpty && newApiKey != _currentApiKey) {
          _currentApiKey = newApiKey;
          updated = true;
          print('🔑 Updated API key: ${_currentApiKey.substring(0, 8)}...');
        }
        
        // Update base URL
        final newBaseUrl = config['api_base_url'] as String?;
        if (newBaseUrl != null && newBaseUrl.isNotEmpty && newBaseUrl != _baseUrl) {
          _baseUrl = newBaseUrl;
          updated = true;
          print('🌐 Updated base URL: $_baseUrl');
        }
        
        if (updated) {
          // Save to local storage
          await _saveLocalConfig();
          _lastUpdate = DateTime.now();
          print('✅ Configuration updated successfully from GitHub');
        } else {
          print('ℹ️ No configuration changes detected');
        }
        
        return updated;
      } else {
        print('❌ Failed to fetch config: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ Error updating from GitHub: $e');
      return false;
    }
  }
  
  /// Save configuration to local storage
  static Future<void> _saveLocalConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString('vedic_api_key', _currentApiKey);
      await prefs.setString('api_base_url', _baseUrl);
      await prefs.setString('last_update', DateTime.now().toIso8601String());
      
      print('💾 Saved configuration to local storage');
    } catch (e) {
      print('❌ Error saving local config: $e');
    }
  }
  
  /// Check if API key is working
  static Future<bool> testApiKey([String? testKey]) async {
    try {
      final keyToTest = testKey ?? _currentApiKey;
      
      print('🧪 Testing API key: ${keyToTest.substring(0, 8)}...');
      
      // Simple test call to check if key works
      final testUrl = Uri.parse(
        '$_baseUrl/dosha/mangal-dosh'
        '?api_key=$keyToTest'
        '&dob=01/01/1990'
        '&tob=12:00'
        '&lat=27.7172'
        '&lon=85.3240'
        '&tz=5.75'
        '&lang=en'
      );
      
      final response = await http.get(testUrl).timeout(const Duration(seconds: 5));
      
      final isWorking = response.statusCode == 200;
      print(isWorking ? '✅ API key is working' : '❌ API key failed');
      
      return isWorking;
    } catch (e) {
      print('❌ API key test failed: $e');
      return false;
    }
  }
  
  /// Get API key with automatic fallback
  static Future<String> getWorkingApiKey() async {
    // Always try to update from GitHub first
    print('🔄 Checking GitHub for latest API key...');
    try {
      await updateFromGitHub();
      print('✅ GitHub check completed');
    } catch (e) {
      print('⚠️ GitHub check failed: $e');
    }

    // Test current key
    if (await testApiKey()) {
      print('✅ Current API key is working: ${_currentApiKey.substring(0, 8)}...');
      return _currentApiKey;
    }

    print('⚠️ All attempts failed, using current key as last resort');
    return _currentApiKey;
  }
  
  /// Manual configuration for testing
  static Future<void> setApiKey(String newKey) async {
    _currentApiKey = newKey;
    await _saveLocalConfig();
    print('🔧 Manually set API key: ${newKey.substring(0, 8)}...');
  }

  /// Clear cache and force refresh from GitHub
  static Future<void> forceRefresh() async {
    _lastUpdate = null; // Clear last update time
    print('🔄 Force refreshing from GitHub...');
    await updateFromGitHub();
    print('✅ Force refresh completed');
  }
  
  /// Get configuration status
  static Map<String, dynamic> getStatus() {
    return {
      'current_key': _currentApiKey.substring(0, 8) + '...',
      'base_url': _baseUrl,
      'last_update': _lastUpdate?.toIso8601String(),
      'initialized': _initialized,
      'config_url': _configUrl,
    };
  }
  
  /// Show setup instructions
  static void showSetupInstructions() {
    print('''
🚀 GitHub API Manager Setup Instructions:

1. Create GitHub repository: nepali-calendar-config
2. Add file: api-config.json
3. Content:
   {
     "vedic_api_key": "your-new-api-key-here",
     "api_base_url": "https://api.vedicastroapi.com/v3-json"
   }
4. Get raw URL and update _configUrl in this file
5. To change API key: Edit the JSON file on GitHub

Current config URL: $_configUrl
    ''');
  }
}
