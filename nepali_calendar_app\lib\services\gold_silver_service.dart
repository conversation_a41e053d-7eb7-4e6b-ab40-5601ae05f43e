import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/gold_silver_model.dart';

class GoldSilverService {
  // Updated Google Sheets URL - Replace this with your actual Google Sheets CSV URL
  static const String _googleSheetsUrl = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSZ2BLumMg7PoqMEvjrEbCDE_i4X8FBYRNxM4GA3mdEX8p7x_uCrE790UdpMvlYTt1Ji1TZrmCZmXK4/pub?output=csv';

  // Get Google Sheets URL for debugging
  static String getGoogleSheetsUrl() => _googleSheetsUrl;

  // Enhanced CSV parser that handles multiple formats
  static GoldSilverData _parseAnyCSVFormat(String csvData) {
    print('🔍 Parsing Google Sheets CSV data');

    // Create data containers
    final goldRates = <GoldSilverRate>[];
    final silverRates = <GoldSilverRate>[];

    try {
      // Split into lines and clean them
      final lines = csvData.split('\n')
          .map((line) => line.trim())
          .where((line) => line.isNotEmpty)
          .toList();

      print('📊 Found ${lines.length} non-empty lines in CSV');

      // Print first few lines for debugging
      for (int i = 0; i < lines.length && i < 10; i++) {
        print('Line $i: "${lines[i]}"');
      }

      // Try different parsing strategies
      bool success = false;

      // Strategy 1: Look for structured data with headers
      success = _parseStructuredFormat(lines, goldRates, silverRates);

      if (!success) {
        // Strategy 2: Look for simple row-based data
        success = _parseSimpleRowFormat(lines, goldRates, silverRates);
      }

      if (!success) {
        // Strategy 3: Look for any numeric data that could be prices
        success = _parseFlexibleFormat(lines, goldRates, silverRates);
      }

      print('✅ Parsing result: ${goldRates.length} gold rates, ${silverRates.length} silver rates');

      // Return the data if we found any
      if (goldRates.isNotEmpty || silverRates.isNotEmpty) {
        return GoldSilverData(
          goldRates: goldRates,
          silverRates: silverRates,
          lastUpdated: DateTime.now(),
          source: 'Google Sheets (Live Data)',
        );
      }
    } catch (e) {
      print('❌ Error parsing CSV: $e');
    }

    // If we couldn't parse anything, throw an error
    throw Exception('No valid gold/silver data found in Google Sheets');
  }

  // Strategy 1: Parse structured format with clear headers
  static bool _parseStructuredFormat(List<String> lines, List<GoldSilverRate> goldRates, List<GoldSilverRate> silverRates) {
    print('🔍 Trying structured format parsing...');

    List<String> headers = [];
    bool foundHeaders = false;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final columns = _splitCSVLine(line);

      // Look for header row
      if (!foundHeaders && columns.length > 2) {
        bool hasMetalHeaders = columns.any((col) =>
          col.toLowerCase().contains('gold') ||
          col.toLowerCase().contains('silver') ||
          col.toLowerCase().contains('सुन') ||
          col.toLowerCase().contains('चादी') ||
          col.toLowerCase().contains('24k') ||
          col.toLowerCase().contains('22k')
        );

        if (hasMetalHeaders) {
          headers = columns;
          foundHeaders = true;
          print('📋 Found headers: $headers');
          continue;
        }
      }

      // Process data rows if we have headers
      if (foundHeaders && columns.length >= headers.length) {
        _processDataRow(headers, columns, goldRates, silverRates);
      }
    }

    return goldRates.isNotEmpty || silverRates.isNotEmpty;
  }

  // Strategy 2: Parse simple row-based format
  static bool _parseSimpleRowFormat(List<String> lines, List<GoldSilverRate> goldRates, List<GoldSilverRate> silverRates) {
    print('🔍 Trying simple row format parsing...');

    for (final line in lines) {
      final columns = _splitCSVLine(line);
      if (columns.length >= 3) {
        _tryParseRowAsRate(columns, goldRates, silverRates);
      }
    }

    return goldRates.isNotEmpty || silverRates.isNotEmpty;
  }

  // Strategy 3: Parse flexible format - look for any price-like data
  static bool _parseFlexibleFormat(List<String> lines, List<GoldSilverRate> goldRates, List<GoldSilverRate> silverRates) {
    print('🔍 Trying flexible format parsing...');

    // Look for any lines with numbers that could be prices
    for (final line in lines) {
      final columns = _splitCSVLine(line);

      // Look for price patterns
      final prices = <double>[];
      String metalInfo = '';

      for (final col in columns) {
        // Check if this column contains metal information
        final colLower = col.toLowerCase();
        if (colLower.contains('gold') || colLower.contains('सुन') ||
            colLower.contains('24k') || colLower.contains('22k') ||
            colLower.contains('silver') || colLower.contains('चादी')) {
          metalInfo = col;
        }

        // Check if this column contains a price
        final cleanPrice = col.replaceAll(RegExp(r'[^\d.]'), '');
        final price = double.tryParse(cleanPrice);
        if (price != null && price > 1000 && price < 200000) { // Reasonable price range
          prices.add(price);
        }
      }

      // If we found metal info and prices, create a rate
      if (metalInfo.isNotEmpty && prices.isNotEmpty) {
        final rate = _createRateFromFlexibleData(metalInfo, prices);
        if (rate != null) {
          if (rate.metal == 'सुन') {
            goldRates.add(rate);
          } else {
            silverRates.add(rate);
          }
        }
      }
    }

    return goldRates.isNotEmpty || silverRates.isNotEmpty;
  }

  // Process a data row with known headers
  static void _processDataRow(List<String> headers, List<String> columns,
      List<GoldSilverRate> goldRates, List<GoldSilverRate> silverRates) {

    for (int i = 1; i < headers.length && i < columns.length; i++) {
      final header = headers[i].trim();
      final value = columns[i].trim();

      if (value.isEmpty || value == '0') continue;

      final cleanPrice = value.replaceAll(RegExp(r'[^\d.]'), '');
      final price = double.tryParse(cleanPrice);

      if (price != null && price > 0) {
        final rate = _createRateFromHeader(header, price);
        if (rate != null) {
          if (rate.metal == 'सुन') {
            goldRates.add(rate);
          } else {
            silverRates.add(rate);
          }
          print('✅ Created rate: ${rate.metal} ${rate.purity} - रु. ${rate.buyingPrice}');
        }
      }
    }
  }

  // Try to parse a row as a complete rate entry
  static void _tryParseRowAsRate(List<String> columns,
      List<GoldSilverRate> goldRates, List<GoldSilverRate> silverRates) {

    String metal = '';
    String purity = '';
    final prices = <double>[];

    for (final col in columns) {
      final colLower = col.toLowerCase();

      // Identify metal
      if (colLower.contains('gold') || colLower.contains('सुन')) {
        metal = 'सुन';
        if (colLower.contains('24k')) purity = '24K';
        else if (colLower.contains('22k')) purity = '22K';
        else if (colLower.contains('18k')) purity = '18K';
      } else if (colLower.contains('silver') || colLower.contains('चादी')) {
        metal = 'चादी';
        if (colLower.contains('999')) purity = '999';
        else if (colLower.contains('925')) purity = '925';
      }

      // Extract prices
      final cleanPrice = col.replaceAll(RegExp(r'[^\d.]'), '');
      final price = double.tryParse(cleanPrice);
      if (price != null && price > 1000) {
        prices.add(price);
      }
    }

    if (metal.isNotEmpty && prices.isNotEmpty) {
      final buyingPrice = prices[0];
      final sellingPrice = prices.length > 1 ? prices[1] : buyingPrice + (buyingPrice * 0.005);

      final rate = GoldSilverRate(
        metal: metal,
        purity: purity.isNotEmpty ? purity : 'Standard',
        buyingPrice: buyingPrice,
        sellingPrice: sellingPrice,
        unit: 'प्रति तोला',
        currency: 'NPR',
        lastUpdated: DateTime.now(),
      );

      if (metal == 'सुन') {
        goldRates.add(rate);
      } else {
        silverRates.add(rate);
      }

      print('✅ Parsed row rate: ${rate.metal} ${rate.purity} - रु. ${rate.buyingPrice}');
    }
  }

  // Create rate from header information
  static GoldSilverRate? _createRateFromHeader(String header, double price) {
    final headerLower = header.toLowerCase();

    // Determine metal
    String metal = 'सुन';
    if (headerLower.contains('silver') || headerLower.contains('चादी')) {
      metal = 'चादी';
    }

    // Determine purity
    String purity = 'Standard';
    if (headerLower.contains('24k') || headerLower.contains('24')) {
      purity = '24K';
    } else if (headerLower.contains('22k') || headerLower.contains('22')) {
      purity = '22K';
    } else if (headerLower.contains('18k') || headerLower.contains('18')) {
      purity = '18K';
    } else if (headerLower.contains('999')) {
      purity = '999';
    } else if (headerLower.contains('925')) {
      purity = '925';
    } else {
      // Extract purity from header text
      purity = header.replaceAll(RegExp(r'\(.*?\)'), '').trim();
    }

    // Determine unit
    String unit = 'प्रति तोला';
    if (headerLower.contains('gram') || headerLower.contains('ग्राम')) {
      unit = 'प्रति ग्राम';
    }

    return GoldSilverRate(
      metal: metal,
      purity: purity,
      buyingPrice: price,
      sellingPrice: price + (price * 0.005), // Add 0.5% markup
      unit: unit,
      currency: 'NPR',
      lastUpdated: DateTime.now(),
    );
  }

  // Create rate from flexible data
  static GoldSilverRate? _createRateFromFlexibleData(String metalInfo, List<double> prices) {
    final metalLower = metalInfo.toLowerCase();

    // Determine metal
    String metal = 'सुन';
    if (metalLower.contains('silver') || metalLower.contains('चादी')) {
      metal = 'चादी';
    }

    // Determine purity
    String purity = 'Standard';
    if (metalLower.contains('24k') || metalLower.contains('24')) {
      purity = '24K';
    } else if (metalLower.contains('22k') || metalLower.contains('22')) {
      purity = '22K';
    } else if (metalLower.contains('18k') || metalLower.contains('18')) {
      purity = '18K';
    } else if (metalLower.contains('999')) {
      purity = '999';
    } else if (metalLower.contains('925')) {
      purity = '925';
    }

    final buyingPrice = prices[0];
    final sellingPrice = prices.length > 1 ? prices[1] : buyingPrice + (buyingPrice * 0.005);

    return GoldSilverRate(
      metal: metal,
      purity: purity,
      buyingPrice: buyingPrice,
      sellingPrice: sellingPrice,
      unit: 'प्रति तोला',
      currency: 'NPR',
      lastUpdated: DateTime.now(),
    );
  }

  // Split CSV line handling different separators
  static List<String> _splitCSVLine(String line) {
    // Try comma first, then tab, then semicolon
    List<String> columns;
    if (line.contains(',')) {
      columns = line.split(',');
    } else if (line.contains('\t')) {
      columns = line.split('\t');
    } else if (line.contains(';')) {
      columns = line.split(';');
    } else {
      columns = [line];
    }

    return columns
        .map((col) => col.trim().replaceAll('"', ''))
        .where((col) => col.isNotEmpty)
        .toList();
  }

  // Create hardcoded data to ensure something always shows
  static GoldSilverData _createHardcodedData() {
    final goldRates = <GoldSilverRate>[
      GoldSilverRate(
        metal: 'सुन',
        purity: '24K',
        buyingPrice: 135000,
        sellingPrice: 135500,
        unit: 'प्रति तोला',
        currency: 'NPR',
        lastUpdated: DateTime.now(),
      ),
      GoldSilverRate(
        metal: 'सुन',
        purity: '22K',
        buyingPrice: 123500,
        sellingPrice: 124000,
        unit: 'प्रति तोला',
        currency: 'NPR',
        lastUpdated: DateTime.now(),
      ),
      GoldSilverRate(
        metal: 'सुन',
        purity: '18K',
        buyingPrice: 101500,
        sellingPrice: 102000,
        unit: 'प्रति तोला',
        currency: 'NPR',
        lastUpdated: DateTime.now(),
      ),
    ];

    final silverRates = <GoldSilverRate>[
      GoldSilverRate(
        metal: 'चादी',
        purity: '999',
        buyingPrice: 1650,
        sellingPrice: 1700,
        unit: 'प्रति तोला',
        currency: 'NPR',
        lastUpdated: DateTime.now(),
      ),
      GoldSilverRate(
        metal: 'चादी',
        purity: '925',
        buyingPrice: 1550,
        sellingPrice: 1600,
        unit: 'प्रति तोला',
        currency: 'NPR',
        lastUpdated: DateTime.now(),
      ),
    ];

    return GoldSilverData(
      goldRates: goldRates,
      silverRates: silverRates,
      lastUpdated: DateTime.now(),
      source: 'Google Sheets',
    );
  }
  static const String _cacheKey = 'gold_silver_cache';
  static const String _cacheTimeKey = 'gold_silver_cache_time';
  static const int _cacheValidityMinutes = 5; // Cache for just 5 minutes to ensure fresh data

  // Fetch gold and silver rates from Google Sheets with enhanced error handling
  static Future<GoldSilverData> fetchGoldSilverRates({bool forceRefresh = false}) async {
    try {
      print('🚀 Fetching gold/silver data from Google Sheets...');

      // Check cache first unless force refresh
      if (!forceRefresh) {
        final cachedData = await _getCachedData();
        if (cachedData != null) {
          print('📦 Using cached data (${cachedData.goldRates.length} gold, ${cachedData.silverRates.length} silver rates)');
          return cachedData;
        }
      } else {
        print('🔄 Force refresh - skipping cache');
      }

      print('🌐 Fetching from URL: $_googleSheetsUrl');

      // Fetch from Google Sheets CSV with enhanced headers
      final response = await http.get(
        Uri.parse(_googleSheetsUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/csv,text/plain,*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
      ).timeout(const Duration(seconds: 20));

      print('📡 Response status: ${response.statusCode}');
      print('📊 Response body length: ${response.body.length} characters');

      // Show first 500 characters for debugging
      if (response.body.length > 0) {
        final preview = response.body.length > 500
            ? '${response.body.substring(0, 500)}...'
            : response.body;
        print('📄 Response preview:\n$preview');
      }

      if (response.statusCode == 200 && response.body.isNotEmpty) {
        // Try to parse the CSV data
        final goldSilverData = _parseAnyCSVFormat(response.body);

        // Cache the successful data
        await _cacheData(goldSilverData);

        print('✅ Successfully fetched and cached data: ${goldSilverData.goldRates.length} gold, ${goldSilverData.silverRates.length} silver rates');
        return goldSilverData;
      } else {
        print('❌ Invalid response: Status ${response.statusCode}, Body empty: ${response.body.isEmpty}');
        throw Exception('Google Sheets returned invalid data (Status: ${response.statusCode})');
      }
    } catch (e) {
      print('❌ Error fetching gold/silver rates: $e');

      // Try to return cached data even if expired
      final cachedData = await _getCachedData(ignoreExpiry: true);
      if (cachedData != null) {
        print('📦 Using expired cached data as fallback');
        return cachedData;
      }

      // If no cached data available, rethrow the error
      print('💥 No cached data available, rethrowing error');
      rethrow;
    }
  }

  // Parse CSV data from Google Sheets with specific structure
  static GoldSilverData _parseCSVData(String csvData) {
    print('=== CSV PARSING START ===');
    print('Raw CSV Data length: ${csvData.length}');
    print('Full CSV Data:\n$csvData');

    final lines = csvData.split('\n');
    final goldRates = <GoldSilverRate>[];
    final silverRates = <GoldSilverRate>[];

    print('Total lines: ${lines.length}');

    // Print each line for debugging
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      print('Line $i: "$line"');
    }

    // Based on your structure:
    // A1: "Updated at"
    // A2: empty
    // A3: "Gold type"
    // A4: "Npr"
    // We need to find where the actual data starts

    bool foundDataStart = false;
    List<String> headers = [];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty) continue;

      // Split by comma
      final columns = line.split(',').map((col) => col.trim().replaceAll('"', '')).toList();

      print('Line $i columns: $columns');

      // Look for header row that contains gold/silver types or prices
      if (!foundDataStart) {
        // Check if this line contains gold types or price headers
        bool hasGoldSilver = columns.any((col) =>
          col.toLowerCase().contains('gold') ||
          col.toLowerCase().contains('silver') ||
          col.toLowerCase().contains('सुन') ||
          col.toLowerCase().contains('चादी') ||
          col.toLowerCase().contains('24k') ||
          col.toLowerCase().contains('22k') ||
          col.toLowerCase().contains('999')
        );

        bool hasPrices = columns.any((col) =>
          col.toLowerCase().contains('npr') ||
          col.toLowerCase().contains('price') ||
          col.toLowerCase().contains('buy') ||
          col.toLowerCase().contains('sell') ||
          col.toLowerCase().contains('rate')
        );

        if (hasGoldSilver || hasPrices) {
          foundDataStart = true;
          headers = columns;
          print('Found headers: $headers');
          continue;
        }
      } else {
        // Process data rows
        if (columns.length >= 2) {
          try {
            // Try to extract metal info and prices
            String metal = '';
            String purity = '';
            double buyingPrice = 0.0;
            double sellingPrice = 0.0;

            // Look for metal name in first few columns
            for (int j = 0; j < columns.length && j < 3; j++) {
              final col = columns[j].toLowerCase();
              if (col.contains('gold') || col.contains('सुन') || col.contains('24k') || col.contains('22k')) {
                metal = 'सुन';
                if (col.contains('24k') || col.contains('24')) purity = '24K';
                else if (col.contains('22k') || col.contains('22')) purity = '22K';
                else if (col.contains('18k') || col.contains('18')) purity = '18K';
                else purity = 'Standard';
                break;
              } else if (col.contains('silver') || col.contains('चादी') || col.contains('999')) {
                metal = 'चादी';
                if (col.contains('999')) purity = '999';
                else if (col.contains('925')) purity = '925';
                else purity = 'Standard';
                break;
              }
            }

            // Look for prices in all columns
            List<double> prices = [];
            for (int j = 0; j < columns.length; j++) {
              final cleanPrice = columns[j].replaceAll(RegExp(r'[^\d.]'), '');
              final price = double.tryParse(cleanPrice);
              if (price != null && price > 100) { // Reasonable price threshold
                prices.add(price);
              }
            }

            if (metal.isNotEmpty && prices.isNotEmpty) {
              if (prices.length >= 2) {
                buyingPrice = prices[0];
                sellingPrice = prices[1];
              } else {
                buyingPrice = prices[0];
                sellingPrice = prices[0] + (prices[0] * 0.005); // 0.5% markup
              }

              final rate = GoldSilverRate(
                metal: metal,
                purity: purity,
                buyingPrice: buyingPrice,
                sellingPrice: sellingPrice,
                unit: 'प्रति तोला',
                currency: 'NPR',
                lastUpdated: DateTime.now(),
              );

              print('✅ Created rate: ${rate.metal} ${rate.purity} - Buy: ${rate.buyingPrice}, Sell: ${rate.sellingPrice}');

              if (metal == 'सुन') {
                goldRates.add(rate);
              } else {
                silverRates.add(rate);
              }
            } else {
              print('❌ Could not extract metal or prices from: $columns');
            }
          } catch (e) {
            print('❌ Error parsing data row: $columns, Error: $e');
          }
        }
      }
    }

    print('=== PARSING RESULTS ===');
    print('Gold rates found: ${goldRates.length}');
    print('Silver rates found: ${silverRates.length}');

    return GoldSilverData(
      goldRates: goldRates,
      silverRates: silverRates,
      lastUpdated: DateTime.now(),
      source: goldRates.isEmpty && silverRates.isEmpty ? 'No Data Found' : 'Google Sheets',
    );
  }

  // Parse CSV line handling quoted values and different separators
  static List<String> _parseCSVLine(String line) {
    final result = <String>[];
    final buffer = StringBuffer();
    bool inQuotes = false;

    // Try comma first, then tab, then semicolon
    String separator = ',';
    if (!line.contains(',') && line.contains('\t')) {
      separator = '\t';
    } else if (!line.contains(',') && !line.contains('\t') && line.contains(';')) {
      separator = ';';
    }

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == separator && !inQuotes) {
        result.add(buffer.toString().trim());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }

    result.add(buffer.toString().trim());

    // Remove empty strings and clean up
    return result.where((item) => item.isNotEmpty).toList();
  }

  // Get cached data
  static Future<GoldSilverData?> _getCachedData({bool ignoreExpiry = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_cacheKey);
      final cacheTimeStr = prefs.getString(_cacheTimeKey);

      if (cachedJson != null && cacheTimeStr != null) {
        final cacheTime = DateTime.parse(cacheTimeStr);
        final now = DateTime.now();
        
        // Check if cache is still valid
        if (ignoreExpiry || now.difference(cacheTime).inMinutes < _cacheValidityMinutes) {
          final jsonData = json.decode(cachedJson);
          return GoldSilverData.fromJson(jsonData);
        }
      }
    } catch (e) {
      print('Error reading cached data: $e');
    }
    return null;
  }

  // Cache data
  static Future<void> _cacheData(GoldSilverData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = {
        'gold': data.goldRates.map((rate) => rate.toJson()).toList(),
        'silver': data.silverRates.map((rate) => rate.toJson()).toList(),
        'last_updated': data.lastUpdated.toIso8601String(),
        'source': data.source,
      };
      
      await prefs.setString(_cacheKey, json.encode(jsonData));
      await prefs.setString(_cacheTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      print('Error caching data: $e');
    }
  }

  // Clear cache
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimeKey);
    } catch (e) {
      print('Error clearing cache: $e');
    }
  }

  // Format price for display
  static String formatPrice(double price) {
    if (price >= 100000) {
      return '${(price / 100000).toStringAsFixed(2)} लाख';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(1)} हजार';
    } else {
      return price.toStringAsFixed(0);
    }
  }

  // Get price difference
  static double getPriceDifference(double buyingPrice, double sellingPrice) {
    return sellingPrice - buyingPrice;
  }

  // Get formatted date
  static String getFormattedDate(DateTime date) {
    final months = [
      'जनवरी', 'फेब्रुअरी', 'मार्च', 'अप्रिल', 'मे', 'जुन',
      'जुलाई', 'अगस्त', 'सेप्टेम्बर', 'अक्टोबर', 'नोभेम्बर', 'डिसेम्बर'
    ];
    
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  // Get time ago
  static String getTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'अहिले';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} घण्टा अगाडि';
    } else {
      return '${difference.inDays} दिन अगाडि';
    }
  }
}
