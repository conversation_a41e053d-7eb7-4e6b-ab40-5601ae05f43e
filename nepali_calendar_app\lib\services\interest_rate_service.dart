import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/interest_rate_model.dart';

class InterestRateService {
  static const String _baseUrl = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vT6tBZCrpE2F2uYu9cC4Pr06RxIG54ZlDbGtBjDx6OMjZMa-p-iQcryJk1Jf4YqkNFFdtWREkKi8KbZ/pub?output=csv';
  static const String _cacheKey = 'interest_rates_cache';
  static const String _cacheTimeKey = 'interest_rates_cache_time';
  static const Duration _cacheValidDuration = Duration(hours: 6); // Cache for 6 hours

  static Future<InterestRateData> getInterestRates({bool forceRefresh = false}) async {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        final cachedData = await _getCachedData();
        if (cachedData != null) {
          return cachedData;
        }
      }

      // Fetch fresh data from Google Sheets
      final response = await http.get(
        Uri.parse(_baseUrl),
        headers: {
          'Accept': 'text/csv',
          'User-Agent': 'Mozilla/5.0 (compatible; NepaliCalendarApp/1.0)',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final rates = _parseCsvData(response.body);
        final data = InterestRateData(
          rates: rates,
          lastUpdated: DateTime.now(),
          isFromCache: false,
        );

        // Cache the fresh data
        await _cacheData(data);
        return data;
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      // If fresh fetch fails, try to return cached data
      final cachedData = await _getCachedData(ignoreExpiry: true);
      if (cachedData != null) {
        return InterestRateData(
          rates: cachedData.rates,
          lastUpdated: cachedData.lastUpdated,
          isFromCache: true,
        );
      }
      throw Exception('No data available: $e');
    }
  }

  static List<BankInterestRate> _parseCsvData(String csvData) {
    final List<BankInterestRate> rates = [];
    final lines = csvData.split('\n');
    
    // Skip header row and process data rows
    for (int i = 1; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty) continue;
      
      // Parse CSV row (handle quoted fields)
      final row = _parseCsvRow(line);
      if (row.isNotEmpty && row[0].isNotEmpty) {
        try {
          final rate = BankInterestRate.fromCsvRow(row);
          if (rate.bankName.isNotEmpty) {
            rates.add(rate);
          }
        } catch (e) {
          // Skip invalid rows
          continue;
        }
      }
    }
    
    return rates;
  }

  static List<String> _parseCsvRow(String row) {
    final List<String> fields = [];
    bool inQuotes = false;
    String currentField = '';
    
    for (int i = 0; i < row.length; i++) {
      final char = row[i];
      
      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == ',' && !inQuotes) {
        fields.add(currentField.trim());
        currentField = '';
      } else {
        currentField += char;
      }
    }
    
    // Add the last field
    fields.add(currentField.trim());
    
    return fields;
  }

  static Future<InterestRateData?> _getCachedData({bool ignoreExpiry = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_cacheKey);
      final cacheTimeStr = prefs.getString(_cacheTimeKey);
      
      if (cachedJson != null && cacheTimeStr != null) {
        final cacheTime = DateTime.parse(cacheTimeStr);
        final now = DateTime.now();
        
        // Check if cache is still valid
        if (ignoreExpiry || now.difference(cacheTime) < _cacheValidDuration) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          final rates = jsonList.map((json) => BankInterestRate.fromJson(json)).toList();
          
          return InterestRateData(
            rates: rates,
            lastUpdated: cacheTime,
            isFromCache: true,
          );
        }
      }
    } catch (e) {
      // Cache read failed, return null
    }
    
    return null;
  }

  static Future<void> _cacheData(InterestRateData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = data.rates.map((rate) => rate.toJson()).toList();
      
      await prefs.setString(_cacheKey, json.encode(jsonList));
      await prefs.setString(_cacheTimeKey, data.lastUpdated.toIso8601String());
    } catch (e) {
      // Cache write failed, continue without caching
    }
  }

  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimeKey);
    } catch (e) {
      // Cache clear failed
    }
  }

  static String getGoogleSheetsUrl() {
    return _baseUrl.replaceAll('/pub?output=csv', '/edit');
  }
}
