import '../models/mantra_model.dart';

class MantraService {
  // All mantras with local audio files
  static final List<Mantra> _mantras = [
    Mantra(
      id: 1,
      titleNepali: 'गायत्री मन्त्र',
      titleEnglish: 'Gay<PERSON>ri Mantra',
      titleSanskrit: 'ॐ भूर्भुवः स्वः तत्सवितुर्वरेण्यं भर्गो देवस्य धीमहि धियो यो नः प्रचोदयात्',
      description: 'सबैभन्दा शक्तिशाली र पवित्र वैदिक मन्त्र',
      audioAssetPath: 'Mantra Audio/1 - Gayattri Mantra.mp3',
      benefits: 'बुद्धि र ज्ञानको विकास, मानसिक शान्ति, आध्यात्मिक उन्नति',
      category: 'वैदिक मन्त्र',
      iconEmoji: '🕉️',
    ),
    Mantra(
      id: 2,
      titleNepali: 'ॐ गं गणपतये नमः',
      titleEnglish: 'Om G<PERSON>',
      titleSanskrit: 'ॐ गं गणपतये नमः',
      description: 'गणेश भगवानको पूजा र आराधनाको मन्त्र',
      audioAssetPath: 'Mantra Audio/2 - O<PERSON> gadgadpate namo nnama.mp3',
      benefits: 'विघ्न हटाउने, नयाँ काम सुरु गर्दा सफलता',
      category: 'गणेश मन्त्र',
      iconEmoji: '🐘',
    ),
    Mantra(
      id: 3,
      titleNepali: 'ॐ नमः शिवाय',
      titleEnglish: 'Om Namah Shivaya',
      titleSanskrit: 'ॐ नमः शिवाय',
      description: 'भगवान शिवको पञ्चाक्षर मन्त्र',
      audioAssetPath: 'Mantra Audio/3 - Om Nama Shibaya.mp3',
      benefits: 'मानसिक शान्ति, आत्मिक शुद्धता, मोक्ष प्राप्ति',
      category: 'शिव मन्त्र',
      iconEmoji: '🔱',
    ),
    Mantra(
      id: 4,
      titleNepali: 'ॐ जय लक्ष्मी माता',
      titleEnglish: 'Om Jaya Laxmi Mata',
      titleSanskrit: 'ॐ जय लक्ष्मी माता',
      description: 'धन र समृद्धिकी देवी लक्ष्मीको आरती',
      audioAssetPath: 'Mantra Audio/4 - Om Jaya Laxmi Mata.mp3',
      benefits: 'धन प्राप्ति, समृद्धि वृद्धि, आर्थिक स्थिरता',
      category: 'लक्ष्मी मन्त्र',
      iconEmoji: '💰',
    ),
    Mantra(
      id: 5,
      titleNepali: 'सरस्वती माता',
      titleEnglish: 'Saraswati Mata',
      titleSanskrit: 'या कुन्देन्दुतुषारहारधवला या शुभ्रवस्त्रावृता',
      description: 'ज्ञान र विद्याकी देवी सरस्वतीको मन्त्र',
      audioAssetPath: 'Mantra Audio/5 - Sarswoti Mata.mp3',
      benefits: 'ज्ञान वृद्धि, विद्या प्राप्ति, बुद्धि विकास',
      category: 'सरस्वती मन्त्र',
      iconEmoji: '📚',
    ),
    Mantra(
      id: 6,
      titleNepali: 'महामृत्युञ्जय मन्त्र',
      titleEnglish: 'Mahamrityunjaya Mantra',
      titleSanskrit: 'ॐ त्र्यम्बकं यजामहे सुगन्धिं पुष्टिवर्धनम्',
      description: 'मृत्युको भयबाट मुक्ति दिलाउने महान् मन्त्र',
      audioAssetPath: 'Mantra Audio/6 - Mahamritunnjaya Mantra.mp3',
      benefits: 'स्वास्थ्य सुधार, दीर्घायु, रोग निवारण',
      category: 'शिव मन्त्र',
      iconEmoji: '🙏',
    ),
  ];

  // Get all mantras
  static List<Mantra> getAllMantras() {
    return _mantras;
  }

  // Get mantra by ID
  static Mantra? getMantra(int id) {
    try {
      return _mantras.firstWhere((mantra) => mantra.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get mantras by category
  static List<Mantra> getMantrasByCategory(String category) {
    return _mantras.where((mantra) => mantra.category == category).toList();
  }

  // Get all categories
  static List<String> getAllCategories() {
    return _mantras.map((mantra) => mantra.category).toSet().toList();
  }

  // Get audio asset path for a specific mantra
  static String getAudioAssetPath(int id) {
    final mantra = getMantra(id);
    return mantra?.audioAssetPath ?? _mantras.first.audioAssetPath;
  }

  // Get mantra summary
  static String getMantraSummary(int id) {
    final mantra = getMantra(id);
    return mantra?.benefits ?? 'मन्त्र जपको फाइदाहरू';
  }

  // Get mantra icon emoji
  static String getMantraIcon(int id) {
    final mantra = getMantra(id);
    return mantra?.iconEmoji ?? '🕉️';
  }
}
