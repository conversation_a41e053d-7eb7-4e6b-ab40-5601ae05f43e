import '../models/mantra_model.dart';

class MantraService {
  // YouTube video IDs from your mantra audio links
  static final List<String> _mantraVideoIds = [
    'SarlTxrAbIY', // Mantra 1
    'Mfi_VunJ0Sk', // Mantra 2
    'ccBcAWE_lIY', // Mantra 3
    'G1ST3i9a3lQ', // Mantra 4
    'o6kJBsbJURM', // Mantra 5
  ];

  // All mantras with audio content
  static final List<Mantra> _mantras = [
    Mantra(
      id: 1,
      titleNepali: 'गायत्री मन्त्र',
      titleEnglish: 'Gayatri Mantra',
      titleSanskrit: 'गायत्री मन्त्र',
      description: 'सबैभन्दा शक्तिशाली र पवित्र मन्त्र',
      youtubeVideoId: _mantraVideoIds[0],
      thumbnailUrl: 'https://img.youtube.com/vi/${_mantraVideoIds[0]}/maxresdefault.jpg',
      duration: '10:30',
      benefits: 'बुद्धि र ज्ञानको विकास, मानसिक शान्ति',
      category: 'वैदिक मन्त्र',
    ),
    Mantra(
      id: 2,
      titleNepali: 'टप १९ हिन्दू मन्त्र',
      titleEnglish: 'Top 19 Hindu Mantra',
      titleSanskrit: 'शीर्ष १९ हिन्दू मन्त्र',
      description: '१९ वटा शक्तिशाली हिन्दू मन्त्रहरूको संग्रह',
      youtubeVideoId: _mantraVideoIds[1],
      thumbnailUrl: 'https://img.youtube.com/vi/${_mantraVideoIds[1]}/maxresdefault.jpg',
      duration: '45:30',
      benefits: 'सम्पूर्ण आध्यात्मिक लाभ, मानसिक शान्ति',
      category: 'मन्त्र संग्रह',
    ),
    Mantra(
      id: 3,
      titleNepali: 'ॐ नमः शिवाय',
      titleEnglish: 'Om Namah Shivaya',
      titleSanskrit: 'ॐ नमः शिवाय',
      description: 'भगवान शिवको पञ्चाक्षर मन्त्र',
      youtubeVideoId: _mantraVideoIds[2],
      thumbnailUrl: 'https://img.youtube.com/vi/${_mantraVideoIds[2]}/maxresdefault.jpg',
      duration: '20:15',
      benefits: 'आध्यात्मिक उन्नति, मानसिक शुद्धता',
      category: 'शिव मन्त्र',
    ),
    Mantra(
      id: 4,
      titleNepali: 'लक्ष्मी आरती भजन',
      titleEnglish: 'Laxmi Aarti Bhajan',
      titleSanskrit: 'श्री लक्ष्मी आरती',
      description: 'धन र समृद्धिकी देवी लक्ष्मीको आरती',
      youtubeVideoId: _mantraVideoIds[3],
      thumbnailUrl: 'https://img.youtube.com/vi/${_mantraVideoIds[3]}/maxresdefault.jpg',
      duration: '12:15',
      benefits: 'धन प्राप्ति, समृद्धि वृद्धि, आर्थिक स्थिरता',
      category: 'आरती भजन',
    ),
    Mantra(
      id: 5,
      titleNepali: 'हनुमान चालीसा',
      titleEnglish: 'Hanuman Chalisa',
      titleSanskrit: 'श्री हनुमान चालीसा',
      description: 'बजरंगबलीको चालीसा',
      youtubeVideoId: _mantraVideoIds[4],
      thumbnailUrl: 'https://img.youtube.com/vi/${_mantraVideoIds[4]}/maxresdefault.jpg',
      duration: '22:30',
      benefits: 'शक्ति वृद्धि, भय निवारण, संकट मोचन',
      category: 'हनुमान चालीसा',
    ),
  ];

  // Get all mantras
  static List<Mantra> getAllMantras() {
    return _mantras;
  }

  // Get mantra by ID
  static Mantra? getMantra(int id) {
    try {
      return _mantras.firstWhere((mantra) => mantra.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get mantras by category
  static List<Mantra> getMantrasByCategory(String category) {
    return _mantras.where((mantra) => mantra.category == category).toList();
  }

  // Get all categories
  static List<String> getAllCategories() {
    return _mantras.map((mantra) => mantra.category).toSet().toList();
  }

  // Get video ID for a specific mantra
  static String getVideoIdForMantra(int id) {
    if (id >= 1 && id <= _mantraVideoIds.length) {
      return _mantraVideoIds[id - 1];
    }
    return _mantraVideoIds[0]; // Fallback to first mantra
  }

  // Get thumbnail URL
  static String getThumbnailUrl(String videoId) {
    return 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg';
  }

  // Get mantra summary
  static String getMantraSummary(int id) {
    final mantra = getMantra(id);
    return mantra?.benefits ?? 'मन्त्र जपको फाइदाहरू';
  }
}
