import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/mobile_price_model.dart';

class MobilePriceService {
  static const String _cachePrefix = 'mobile_price_cache_';
  static const String _lastUpdatePrefix = 'mobile_price_last_update_';
  static const Duration _cacheExpiry = Duration(hours: 6); // Cache for 6 hours for faster loading

  // Mobile brand configurations with direct CSV URLs for faster loading
  static final List<MobileBrandInfo> _mobileBrands = [
    MobileBrandInfo(
      name: 'Poco',
      nepaliName: 'पोको',
      logo: '📱',
      sheetId: 'https://docs.google.com/spreadsheets/d/e/2PACX-1vQwGt_oshFw0xqs7iNKGH2q1_4bugI2p-rzuJIxSSL8UN1YKVuc2IC5fDNlxYBnRwj5ha9R8SIZkrNI/pub?output=csv',
      primaryColor: const Color(0xFFFFD700), // Gold
      secondaryColor: const Color(0xFFFF6B35), // Orange
    ),
    MobileBrandInfo(
      name: 'OnePlus',
      nepaliName: 'वनप्लस',
      logo: '🔴',
      sheetId: 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTz1Rn1kkrz9T4Uhie6sR32bIqUXtATvHvnEdyET8F4WiCTx1H5XVnnrp8wc9N4j83eAY70frX4J4vQ/pub?output=csv',
      primaryColor: const Color(0xFFEB1C23), // OnePlus Red
      secondaryColor: const Color(0xFF000000), // Black
    ),
    MobileBrandInfo(
      name: 'Samsung',
      nepaliName: 'स्यामसंग',
      logo: '📲',
      sheetId: 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSza6rKp2lcowF-MaymHYMOJH6BSEbyTNCIdfh5YHiCcr24kyEfE5U3W7balpFeNXd-_GGyLU6k6M-y/pub?output=csv',
      primaryColor: const Color(0xFF1428A0), // Samsung Blue
      secondaryColor: const Color(0xFF00D2FF), // Light Blue
    ),
    MobileBrandInfo(
      name: 'Redmi',
      nepaliName: 'रेडमी',
      logo: '🔥',
      sheetId: 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRtdkcN57Il3FfhacIUoyfV3G898rsMcGFen2TWbpbbO4nQi7MuOKDqcQVnu85VejcGq2PQA6Uq2aI2/pub?output=csv',
      primaryColor: const Color(0xFFFF6900), // Redmi Orange
      secondaryColor: const Color(0xFFFF0000), // Red
    ),
    MobileBrandInfo(
      name: 'iPhone',
      nepaliName: 'आईफोन',
      logo: '🍎',
      sheetId: 'https://docs.google.com/spreadsheets/d/e/2PACX-1vQ4DNZ-juXAFuSkEU6jFhcynkIThMoUCq9NsyK4nKSbj0UV_aSljhxYTUWg9erqA2O9JDJYTiLYLnSQ/pub?output=csv',
      primaryColor: const Color(0xFF000000), // Apple Black
      secondaryColor: const Color(0xFF666666), // Dark Gray
    ),
  ];

  // Get all mobile brands
  static List<MobileBrandInfo> getAllBrands() {
    return _mobileBrands;
  }

  // Get brand info by name
  static MobileBrandInfo? getBrandInfo(String brandName) {
    try {
      return _mobileBrands.firstWhere(
        (brand) => brand.name.toLowerCase() == brandName.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  // Fetch mobile prices for a specific brand
  static Future<MobileBrandData?> fetchMobilePrices(String brandName, {bool forceRefresh = false}) async {
    try {
      final brandInfo = getBrandInfo(brandName);
      if (brandInfo == null) {
        if (kDebugMode) {
          print('❌ Brand not found: $brandName');
        }
        return null;
      }

      // Always try cache first for instant loading
      final cachedData = await _loadFromCache(brandName);
      if (cachedData != null && !forceRefresh) {
        if (kDebugMode) {
          print('⚡ Using cached data for instant loading: $brandName');
        }
        return cachedData;
      }

      // If no cache or force refresh, try fallback data first for instant display
      if (!forceRefresh) {
        if (kDebugMode) {
          print('⚡ Using fallback data for instant loading: $brandName');
        }
        return MobileBrandData(
          brandName: brandInfo.name,
          brandLogo: brandInfo.logo,
          mobiles: _getFallbackData(brandInfo),
          lastUpdated: DateTime.now().toString(),
        );
      }

      // Only fetch from network if force refresh
      final connectivityResult = await Connectivity().checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (!isOnline) {
        // No internet, return cache or fallback
        return cachedData ?? MobileBrandData(
          brandName: brandInfo.name,
          brandLogo: brandInfo.logo,
          mobiles: _getFallbackData(brandInfo),
          lastUpdated: DateTime.now().toString(),
        );
      }

      // Online: Fetch fresh data
      if (kDebugMode) {
        print('🌐 Fetching fresh data for $brandName');
      }

      // Use direct CSV URL for faster loading
      final url = brandInfo.sheetId; // Now contains full CSV URL
      final response = await http.get(Uri.parse(url)).timeout(
        const Duration(seconds: 5), // Very short timeout for faster response
      );

      if (response.statusCode == 200) {
        final csvData = response.body;

        // Validate CSV data
        if (csvData.isEmpty || csvData.length < 50) {
          if (kDebugMode) {
            print('❌ Empty or too short CSV data for $brandName (length: ${csvData.length})');
          }
          // Return fallback data instead of cache
          return MobileBrandData(
            brandName: brandInfo.name,
            brandLogo: brandInfo.logo,
            mobiles: _getFallbackData(brandInfo),
            lastUpdated: DateTime.now().toString(),
          );
        }

        // Check if it's actually CSV data
        if (!csvData.contains(',') && !csvData.contains('\n')) {
          if (kDebugMode) {
            print('❌ Invalid CSV format for $brandName');
            print('📊 Data preview: ${csvData.substring(0, csvData.length > 200 ? 200 : csvData.length)}');
          }
          // Return fallback data instead of cache
          return MobileBrandData(
            brandName: brandInfo.name,
            brandLogo: brandInfo.logo,
            mobiles: _getFallbackData(brandInfo),
            lastUpdated: DateTime.now().toString(),
          );
        }

        final mobileData = _parseCsvData(csvData, brandInfo);

        // Validate parsed data - fallback data is already added in _parseCsvData
        if (mobileData.mobiles.isEmpty) {
          if (kDebugMode) {
            print('❌ No valid mobile data parsed for $brandName, using fallback');
          }
          // Return fallback data
          return MobileBrandData(
            brandName: brandInfo.name,
            brandLogo: brandInfo.logo,
            mobiles: _getFallbackData(brandInfo),
            lastUpdated: DateTime.now().toString(),
          );
        }

        // Save to cache
        await _saveToCache(brandName, mobileData);

        if (kDebugMode) {
          print('✅ Successfully fetched ${mobileData.mobiles.length} mobiles for $brandName');
        }

        return mobileData;
      } else {
        if (kDebugMode) {
          print('❌ HTTP Error ${response.statusCode} for $brandName');
        }
        // Return cached data as fallback
        return await _loadFromCache(brandName);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching mobile prices for $brandName: $e');
      }
      // Return cached data as fallback
      return await _loadFromCache(brandName);
    }
  }

  // Parse CSV data into mobile models with improved error handling
  static MobileBrandData _parseCsvData(String csvData, MobileBrandInfo brandInfo) {
    final lines = csvData.split('\n');
    final mobiles = <MobilePriceModel>[];

    if (kDebugMode) {
      print('📊 Parsing ${lines.length} lines for ${brandInfo.name}');
      print('📊 First few lines of CSV:');
      for (int i = 0; i < (lines.length > 5 ? 5 : lines.length); i++) {
        print('Line $i: ${lines[i]}');
      }
    }

    // Try to find the header row and data rows
    int dataStartIndex = 0;
    for (int i = 0; i < lines.length && i < 5; i++) {
      final line = lines[i].trim().toLowerCase();
      if (line.contains('model') || line.contains('phone') || line.contains('price')) {
        dataStartIndex = i + 1;
        break;
      }
    }

    // Parse data rows
    for (int i = dataStartIndex; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty || line.length < 5) continue; // Skip very short lines

      try {
        final columns = _parseCsvLine(line);

        if (kDebugMode && i < dataStartIndex + 3) {
          print('Parsing line $i: $columns');
        }

        // Ensure we have at least model and price
        if (columns.length >= 2 && columns[0].trim().isNotEmpty && columns[1].trim().isNotEmpty) {
          final model = columns[0].trim();
          final price = columns[1].trim();

          // Skip if model or price looks like header text or empty
          if (model.toLowerCase().contains('model') ||
              model.toLowerCase().contains('phone') ||
              model.toLowerCase().contains('list') ||
              price.toLowerCase().contains('price') ||
              price.toLowerCase().contains('nepal') ||
              price.toLowerCase().contains('buying') ||
              model.startsWith('*') ||
              price.startsWith('*') ||
              model.isEmpty ||
              price.isEmpty) {
            continue;
          }

          // Clean up the price and extract lowest price
          String cleanPrice = MobilePriceModel.cleanPriceString(price);

          mobiles.add(MobilePriceModel(
            model: model,
            price: cleanPrice,
            storage: columns.length > 2 ? columns[2].trim() : '',
            priceValue: MobilePriceModel.extractPriceValue(price),
          ));
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Error parsing line $i: $e');
        }
        continue; // Skip problematic lines
      }
    }

    if (kDebugMode) {
      print('✅ Successfully parsed ${mobiles.length} mobiles for ${brandInfo.name}');
      if (mobiles.isNotEmpty) {
        print('📱 Sample mobile: ${mobiles.first.model} - ${mobiles.first.price}');
      }
    }

    // If no mobiles found, add some fallback data
    if (mobiles.isEmpty) {
      mobiles.addAll(_getFallbackData(brandInfo));
    }

    // Remove duplicates and keep lowest price for same model
    final uniqueMobiles = <String, MobilePriceModel>{};
    for (final mobile in mobiles) {
      final key = mobile.model.toLowerCase().trim();
      if (!uniqueMobiles.containsKey(key) ||
          mobile.priceValue < uniqueMobiles[key]!.priceValue) {
        uniqueMobiles[key] = mobile;
      }
    }

    return MobileBrandData(
      brandName: brandInfo.name,
      brandLogo: brandInfo.logo,
      mobiles: uniqueMobiles.values.toList()..sort((a, b) => a.priceValue.compareTo(b.priceValue)),
      lastUpdated: DateTime.now().toString(),
    );
  }

  // Improved CSV line parsing with better handling of quotes and special characters
  static List<String> _parseCsvLine(String line) {
    final result = <String>[];
    final buffer = StringBuffer();
    bool inQuotes = false;

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      if (char == '"') {
        // Handle escaped quotes
        if (i + 1 < line.length && line[i + 1] == '"') {
          buffer.write('"');
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char == ',' && !inQuotes) {
        result.add(buffer.toString().trim());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }

    // Add the last field
    result.add(buffer.toString().trim());

    // Clean up empty fields and remove quotes
    return result.map((field) {
      field = field.trim();
      if (field.startsWith('"') && field.endsWith('"') && field.length > 1) {
        field = field.substring(1, field.length - 1);
      }
      return field;
    }).toList();
  }

  // Cache management methods
  static Future<void> _saveToCache(String brandName, MobileBrandData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data.toJson());
      await prefs.setString('$_cachePrefix$brandName', jsonString);
      await prefs.setInt('$_lastUpdatePrefix$brandName', DateTime.now().millisecondsSinceEpoch);
      
      if (kDebugMode) {
        print('💾 Cached data for $brandName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving cache for $brandName: $e');
      }
    }
  }

  static Future<MobileBrandData?> _loadFromCache(String brandName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('$_cachePrefix$brandName');
      
      if (jsonString != null) {
        final json = jsonDecode(jsonString);
        if (kDebugMode) {
          print('📱 Loaded cached data for $brandName');
        }
        return MobileBrandData.fromJson(json);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading cache for $brandName: $e');
      }
    }
    return null;
  }

  static Future<bool> _isCacheValid(String brandName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdate = prefs.getInt('$_lastUpdatePrefix$brandName');
      
      if (lastUpdate != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - lastUpdate;
        return cacheAge < _cacheExpiry.inMilliseconds;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking cache validity for $brandName: $e');
      }
    }
    return false;
  }

  // Clear cache for a specific brand
  static Future<void> clearCache(String brandName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_cachePrefix$brandName');
      await prefs.remove('$_lastUpdatePrefix$brandName');
      
      if (kDebugMode) {
        print('🗑️ Cleared cache for $brandName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing cache for $brandName: $e');
      }
    }
  }

  // Clear all mobile price caches
  static Future<void> clearAllCache() async {
    try {
      for (final brand in _mobileBrands) {
        await clearCache(brand.name);
      }

      if (kDebugMode) {
        print('🗑️ Cleared all mobile price caches');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing all caches: $e');
      }
    }
  }

  // Fallback data when CSV parsing fails
  static List<MobilePriceModel> _getFallbackData(MobileBrandInfo brandInfo) {
    switch (brandInfo.name.toLowerCase()) {
      case 'poco':
        return [
          MobilePriceModel(model: 'Poco M6', price: 'Rs. 15,999', storage: '4+128GB', priceValue: 15999),
          MobilePriceModel(model: 'Poco C65', price: 'Rs. 18,999', storage: '6+128GB', priceValue: 18999),
          MobilePriceModel(model: 'Poco M6 Pro', price: 'Rs. 25,999', storage: '6+128GB', priceValue: 25999),
          MobilePriceModel(model: 'Poco X6', price: 'Rs. 35,999', storage: '8+256GB', priceValue: 35999),
          MobilePriceModel(model: 'Poco X6 Pro', price: 'Rs. 45,999', storage: '8+256GB', priceValue: 45999),
          MobilePriceModel(model: 'Poco F6', price: 'Rs. 55,999', storage: '12+256GB', priceValue: 55999),
        ];
      case 'redmi':
        return [
          MobilePriceModel(model: 'Redmi A3', price: 'Rs. 12,999', storage: '3+64GB', priceValue: 12999),
          MobilePriceModel(model: 'Redmi 14C', price: 'Rs. 16,999', storage: '4+64GB', priceValue: 16999),
          MobilePriceModel(model: 'Redmi 13C', price: 'Rs. 19,999', storage: '4+128GB', priceValue: 19999),
          MobilePriceModel(model: 'Redmi Note 13', price: 'Rs. 28,999', storage: '6+128GB', priceValue: 28999),
          MobilePriceModel(model: 'Redmi Note 13 Pro', price: 'Rs. 35,999', storage: '8+128GB', priceValue: 35999),
          MobilePriceModel(model: 'Redmi Note 13 Pro+', price: 'Rs. 42,999', storage: '8+256GB', priceValue: 42999),
        ];
      case 'samsung':
        return [
          MobilePriceModel(model: 'Samsung Galaxy F15', price: 'Rs. 22,999', storage: '4+128GB', priceValue: 22999),
          MobilePriceModel(model: 'Samsung Galaxy A15', price: 'Rs. 25,999', storage: '4+128GB', priceValue: 25999),
          MobilePriceModel(model: 'Samsung Galaxy A25', price: 'Rs. 35,999', storage: '6+128GB', priceValue: 35999),
          MobilePriceModel(model: 'Samsung Galaxy M35', price: 'Rs. 38,999', storage: '6+128GB', priceValue: 38999),
          MobilePriceModel(model: 'Samsung Galaxy A35', price: 'Rs. 45,999', storage: '8+128GB', priceValue: 45999),
          MobilePriceModel(model: 'Samsung Galaxy A55', price: 'Rs. 65,999', storage: '8+256GB', priceValue: 65999),
        ];
      case 'oneplus':
        return [
          MobilePriceModel(model: 'OnePlus Nord CE 3', price: 'Rs. 35,999', storage: '8+128GB', priceValue: 35999),
          MobilePriceModel(model: 'OnePlus Nord CE 4', price: 'Rs. 45,999', storage: '8+128GB', priceValue: 45999),
          MobilePriceModel(model: 'OnePlus Nord 4', price: 'Rs. 55,999', storage: '8+256GB', priceValue: 55999),
          MobilePriceModel(model: 'OnePlus 11R', price: 'Rs. 65,999', storage: '8+128GB', priceValue: 65999),
          MobilePriceModel(model: 'OnePlus 12R', price: 'Rs. 75,999', storage: '12+256GB', priceValue: 75999),
          MobilePriceModel(model: 'OnePlus 12', price: 'Rs. 89,999', storage: '12+256GB', priceValue: 89999),
        ];
      case 'iphone':
        return [
          MobilePriceModel(model: 'iPhone SE', price: 'Rs. 65,999', storage: '64GB', priceValue: 65999),
          MobilePriceModel(model: 'iPhone 13', price: 'Rs. 89,999', storage: '128GB', priceValue: 89999),
          MobilePriceModel(model: 'iPhone 14', price: 'Rs. 1,19,999', storage: '128GB', priceValue: 119999),
          MobilePriceModel(model: 'iPhone 14 Plus', price: 'Rs. 1,39,999', storage: '128GB', priceValue: 139999),
          MobilePriceModel(model: 'iPhone 15', price: 'Rs. 1,49,999', storage: '128GB', priceValue: 149999),
          MobilePriceModel(model: 'iPhone 15 Pro', price: 'Rs. 1,79,999', storage: '128GB', priceValue: 179999),
        ];
      default:
        return [
          MobilePriceModel(model: '${brandInfo.name} Model 1', price: 'Rs. 25,999', storage: '6+128GB', priceValue: 25999),
          MobilePriceModel(model: '${brandInfo.name} Model 2', price: 'Rs. 35,999', storage: '8+128GB', priceValue: 35999),
        ];
    }
  }
}
