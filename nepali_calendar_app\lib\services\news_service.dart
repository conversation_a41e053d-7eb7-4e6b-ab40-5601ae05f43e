import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/news_article.dart';

class NewsService {
  // Cache configuration
  static const String _cacheKeyPrefix = 'news_cache_';
  static const String _cacheTimestampKey = 'news_cache_timestamp_';
  static const Duration _cacheExpiry = Duration(minutes: 30); // Cache for 30 minutes

  // Based on GhimirePlan/Nepali_News_WebScraper repository
  static final Map<String, Map<String, String>> _nepaliSources = {
    'कान्तिपुर': {
      'website': 'https://ekantipur.com',
      'logo': '📰',
    },
    'नागरिक': {
      'website': 'https://nagariknews.nagariknetwork.com',
      'logo': '📺',
    },
    'अन्नपूर्ण': {
      'website': 'https://annapurnapost.com',
      'logo': '🏔️',
    },
    'सेतोपाटी': {
      'website': 'https://setopati.com',
      'logo': '⚪',
    },
    'रत्नपार्क': {
      'website': 'https://ratopati.com',
      'logo': '🔴',
    },
    'गोरखापत्र': {
      'website': 'https://gorkhapatraonline.com',
      'logo': '📜',
    },
    'हिमालयन': {
      'website': 'https://thehimalayantimes.com',
      'logo': '🏔️',
    },
  };

  static Future<List<NewsArticle>> getNewsBySource(String source) async {
    try {
      // Try to get from cache first
      final cachedNews = await _getCachedNews(source);
      if (cachedNews != null && cachedNews.isNotEmpty) {
        print('📱 Using cached news for $source (${cachedNews.length} articles)');
        return cachedNews;
      }

      // If no cache or expired, fetch from website
      final freshNews = await _scrapeNewsFromWebsite(source);

      // Cache the fresh news
      if (freshNews.isNotEmpty) {
        await _cacheNews(source, freshNews);
      }

      return freshNews;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching news for $source: $e');
      }

      // Try to return cached news even if expired as fallback
      final cachedNews = await _getCachedNews(source, ignoreExpiry: true);
      if (cachedNews != null && cachedNews.isNotEmpty) {
        print('📱 Using expired cached news for $source as fallback');
        return cachedNews;
      }

      return _getFallbackNews(source);
    }
  }

  static Future<List<NewsArticle>> _scrapeNewsFromWebsite(String source) async {
    final sourceInfo = _nepaliSources[source];
    if (sourceInfo == null) return _getFallbackNews(source);

    try {
      final response = await http.get(
        Uri.parse(sourceInfo['website']!),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'ne,en;q=0.9',
          'Cache-Control': 'no-cache',
        },
      ).timeout(const Duration(seconds: 8)); // Reduced timeout for faster response

      if (response.statusCode != 200) {
        return _getFallbackNews(source);
      }

      final document = html_parser.parse(response.body);
      final articles = <NewsArticle>[];

      // Generic news extraction that works for most Nepali news sites
      final newsSelectors = [
        'article, .post, .news-item, .story',
        '.media, .news-list-item, .post-item',
        '.normal, .entry, .content-item'
      ];

      for (final selector in newsSelectors) {
        final elements = document.querySelectorAll(selector);
        if (elements.isNotEmpty) {
          for (final element in elements.take(8)) { // Reduced from 12 to 8 for faster processing
            final article = _extractArticle(element, source);
            if (article != null && article.title.length > 10) {
              articles.add(article);
            }
          }
          if (articles.length >= 6) break; // Stop after getting 6 good articles
        }
      }

      return articles.isNotEmpty ? articles : _getFallbackNews(source);
    } catch (e) {
      if (kDebugMode) print('Scraping error for $source: $e');
      return _getFallbackNews(source);
    }
  }

  static NewsArticle? _extractArticle(dynamic element, String source) {
    try {
      // Extract title
      final titleSelectors = ['h1 a', 'h2 a', 'h3 a', '.title a', 'a[href*="news"], a[href*="post"]'];
      String title = '';
      String url = '';

      for (final selector in titleSelectors) {
        final titleElement = element.querySelector(selector);
        if (titleElement != null) {
          title = titleElement.text.trim();
          url = titleElement.attributes['href'] ?? '';
          if (title.isNotEmpty && title.length > 10) break;
        }
      }

      if (title.isEmpty || title.length < 10) return null;

      // Extract description
      final descElement = element.querySelector('p, .excerpt, .description, .summary');
      final description = descElement?.text.trim() ?? title;

      // Extract image
      final imageElement = element.querySelector('img');
      String imageUrl = imageElement?.attributes['src'] ?? '';
      if (imageUrl.isNotEmpty && !imageUrl.startsWith('http')) {
        final baseUrl = _nepaliSources[source]!['website']!;
        imageUrl = imageUrl.startsWith('/') ? '$baseUrl$imageUrl' : '$baseUrl/$imageUrl';
      }

      // Fix article URL
      if (url.isNotEmpty && !url.startsWith('http')) {
        final baseUrl = _nepaliSources[source]!['website']!;
        url = url.startsWith('/') ? '$baseUrl$url' : '$baseUrl/$url';
      }

      return NewsArticle(
        title: title,
        description: description.length > title.length ? description : title,
        url: url.isNotEmpty ? url : _nepaliSources[source]!['website']!,
        imageUrl: imageUrl,
        source: source,
        publishedAt: DateTime.now().subtract(Duration(minutes: DateTime.now().minute)),
        category: _categorizeNews(title),
      );
    } catch (e) {
      return null;
    }
  }

  static String _categorizeNews(String title) {
    final titleLower = title.toLowerCase();

    if (titleLower.contains('अर्थ') || titleLower.contains('बजेट') || titleLower.contains('बैंक')) {
      return 'अर्थ';
    } else if (titleLower.contains('राजनीति') || titleLower.contains('सरकार') || titleLower.contains('मन्त्री')) {
      return 'राजनीति';
    } else if (titleLower.contains('खेल') || titleLower.contains('फुटबल') || titleLower.contains('क्रिकेट')) {
      return 'खेलकुद';
    } else if (titleLower.contains('शिक्षा') || titleLower.contains('विद्यालय')) {
      return 'शिक्षा';
    } else if (titleLower.contains('स्वास्थ्य') || titleLower.contains('अस्पताल')) {
      return 'स्वास्थ्य';
    } else if (titleLower.contains('पर्यटन') || titleLower.contains('पर्यटक')) {
      return 'पर्यटन';
    }
    return 'समाज';
  }

  static List<NewsArticle> _getFallbackNews(String source) {
    final now = DateTime.now();
    return [
      NewsArticle(
        title: 'नेपालमा नयाँ आर्थिक नीति घोषणा',
        description: 'सरकारले नयाँ आर्थिक वर्षका लागि महत्वपूर्ण आर्थिक नीतिहरू घोषणा गरेको छ।',
        url: _nepaliSources[source]!['website']!,
        imageUrl: '',
        source: source,
        publishedAt: now.subtract(const Duration(hours: 1)),
        category: 'अर्थ',
      ),
      NewsArticle(
        title: 'काठमाडौंमा नयाँ सडक निर्माण सुरु',
        description: 'राजधानी काठमाडौंमा यातायात सुधारका लागि नयाँ सडक निर्माण कार्य सुरु भएको छ।',
        url: _nepaliSources[source]!['website']!,
        imageUrl: '',
        source: source,
        publishedAt: now.subtract(const Duration(hours: 2)),
        category: 'समाज',
      ),
      NewsArticle(
        title: 'नेपाली फुटबल टिमको नयाँ उपलब्धि',
        description: 'राष्ट्रिय फुटबल टिमले अन्तर्राष्ट्रिय प्रतियोगितामा उत्कृष्ट प्रदर्शन गरेको छ।',
        url: _nepaliSources[source]!['website']!,
        imageUrl: '',
        source: source,
        publishedAt: now.subtract(const Duration(hours: 3)),
        category: 'खेलकुद',
      ),
    ];
  }

  static Future<List<NewsArticle>> getAllNews() async {
    try {
      List<NewsArticle> allNews = [];
      
      for (String source in _nepaliSources.keys) {
        final sourceNews = await getNewsBySource(source);
        allNews.addAll(sourceNews);
      }
      
      // Sort by published date (newest first)
      allNews.sort((a, b) => b.publishedAt.compareTo(a.publishedAt));
      
      return allNews;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching all news: $e');
      }
      return [];
    }
  }

  static List<String> getSources() {
    return _nepaliSources.keys.toList();
  }

  static Map<String, String> getSourceInfo(String source) {
    return _nepaliSources[source] ?? {};
  }

  static List<String> getCategories() {
    return ['सबै', 'अर्थ', 'राजनीति', 'समाज', 'खेलकुद', 'शिक्षा', 'स्वास्थ्य', 'पर्यटन', 'प्रविधि'];
  }
}
