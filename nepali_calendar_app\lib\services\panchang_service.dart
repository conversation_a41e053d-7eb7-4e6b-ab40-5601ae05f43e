import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/panchang_model.dart';

class PanchangService {
  static const String _cacheKey = 'panchang_offline_data';
  static const String _widgetUrl = 'https://www.ashesh.com.np/panchang/widget.php?header_title=Nepali%20Panchang&header_color=6A1B9A&api=821173p429';

  WebViewController? _webViewController;

  // Singleton pattern
  static final PanchangService _instance = PanchangService._internal();
  factory PanchangService() => _instance;
  PanchangService._internal();

  Future<PanchangData?> getPanchangData({bool forceRefresh = false}) async {
    try {
      // Check internet connectivity
      final connectivityResult = await Connectivity().checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        // Online: Always generate fresh data
        if (kDebugMode) {
          print('🌐 Online: Generating fresh Panchanga data');
        }
        final freshData = await _getCurrentPanchangData();

        // Save to cache for offline use
        await _saveOfflineCache(freshData);

        return freshData;
      } else {
        // Offline: Use cached data
        if (kDebugMode) {
          print('📱 Offline: Loading cached Panchanga data');
        }
        final cachedData = await _loadOfflineCache();

        if (cachedData != null) {
          return cachedData;
        } else {
          // No cache available, generate basic data
          return await _getBasicPanchangData();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting Panchang data: $e');
      }

      // Try to load from cache as fallback
      final cachedData = await _loadOfflineCache();
      if (cachedData != null) {
        return cachedData;
      }

      // Last resort: generate basic data
      return await _getBasicPanchangData();
    }
  }

  Future<PanchangData> _getCurrentPanchangData() async {
    // Generate current Panchanga data based on today's date
    final now = DateTime.now();
    final nepaliDate = NepaliDateTime.now();

    final currentData = PanchangData(
      dateInfo: DateInfo(
        nepali: 'वि.सं. ${nepaliDate.year} ${_getNepaliMonth(nepaliDate.month)} ${nepaliDate.day}',
        english: '${now.day}/${now.month}/${now.year}',
        nepalSamvat: 'ने.सं. ${nepaliDate.year - 879} ${_getNepaliMonth(nepaliDate.month)} ${nepaliDate.day}',
      ),
      sunMoon: SunMoonTimes(
        sunrise: _calculateSunrise(now),
        sunset: _calculateSunset(now),
        moonrise: _calculateMoonrise(now),
        moonset: _calculateMoonset(now),
      ),
      tithi: TithiInfo(
        current: _getCurrentTithi(nepaliDate),
        endTime: _getTithiEndTime(now),
        next: _getNextTithi(nepaliDate),
      ),
      paksha: _getCurrentPaksha(nepaliDate),
      nakshatra: NakshatraInfo(
        current: _getCurrentNakshatra(now),
        endTime: _getNakshatraEndTime(now),
        next: _getNextNakshatra(now),
      ),
      yoga: YogaInfo(
        current: _getCurrentYoga(now),
        endTime: _getYogaEndTime(now),
        next: _getNextYoga(now),
      ),
      karana: [
        KaranaInfo(
          current: _getCurrentKarana(now),
          endTime: _getKaranaEndTime(now),
          next: _getNextKarana(now),
        )
      ],
      moonSign: _getCurrentMoonSign(now),
      dayDuration: _getDayDuration(now),
      season: _getCurrentSeason(now),
      ayana: _getCurrentAyana(now),
    );

    // No caching - always return fresh data
    return currentData;
  }

  Future<PanchangData> _getBasicPanchangData() async {
    // Fallback method with basic current data
    final now = DateTime.now();
    final nepaliDate = NepaliDateTime.now();

    return PanchangData(
      dateInfo: DateInfo(
        nepali: 'वि.सं. ${nepaliDate.year} ${_getNepaliMonth(nepaliDate.month)} ${nepaliDate.day}',
        english: '${now.day}/${now.month}/${now.year}',
        nepalSamvat: 'ने.सं. ${nepaliDate.year - 879} ${_getNepaliMonth(nepaliDate.month)} ${nepaliDate.day}',
      ),
      sunMoon: SunMoonTimes(
        sunrise: '06:00',
        sunset: '18:00',
        moonrise: '07:00',
        moonset: '19:00',
      ),
      tithi: TithiInfo(
        current: _getCurrentTithi(nepaliDate),
        endTime: '12:00',
        next: _getNextTithi(nepaliDate),
      ),
      paksha: _getCurrentPaksha(nepaliDate),
      nakshatra: NakshatraInfo(
        current: _getCurrentNakshatra(now),
        endTime: '14:00',
        next: _getNextNakshatra(now),
      ),
      yoga: YogaInfo(
        current: _getCurrentYoga(now),
        endTime: '16:00',
        next: _getNextYoga(now),
      ),
      karana: [
        KaranaInfo(
          current: _getCurrentKarana(now),
          endTime: '12:00',
          next: _getNextKarana(now),
        )
      ],
      moonSign: _getCurrentMoonSign(now),
      dayDuration: '12 घडी 0 पला - 12hr 0min',
      season: _getCurrentSeason(now),
      ayana: _getCurrentAyana(now),
    );
  }

  Future<PanchangData?> _extractDataFromWeb() async {
    try {
      // This will be called from the WebView widget
      // For now, return null - the actual extraction happens in the WebView
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting data from web: $e');
      }
      return null;
    }
  }

  Future<void> setWebViewController(WebViewController controller) async {
    _webViewController = controller;
  }

  Future<PanchangData?> extractDataFromWebView() async {
    if (_webViewController == null) return null;

    try {
      // JavaScript to extract Panchang data from the loaded widget
      await _webViewController!.runJavaScript('''
        (function() {
          try {
            var data = {};
            
            // Extract date information
            var dateElements = document.querySelectorAll('div');
            var dateInfo = {};
            
            for (var i = 0; i < dateElements.length; i++) {
              var element = dateElements[i];
              var text = element.textContent.trim();
              
              if (text.includes('वि.सं')) {
                dateInfo.nepali = text;
              } else if (text.includes('ईसवी')) {
                dateInfo.english = text;
              } else if (text.includes('नेपाल संवत')) {
                dateInfo.nepalSamvat = text;
              }
            }
            
            // Extract sun/moon times
            var sunMoon = {};
            var sunElement = document.querySelector('div:contains("सूर्य")');
            if (sunElement) {
              var sunText = sunElement.textContent;
              var sunTimes = sunText.replace('सूर्य', '').trim();
              var times = sunTimes.split(',');
              if (times.length >= 2) {
                sunMoon.sunrise = times[0].trim();
                sunMoon.sunset = times[1].trim();
              }
            }
            
            var moonElement = document.querySelector('div:contains("चन्द्र")');
            if (moonElement) {
              var moonText = moonElement.textContent;
              var moonTimes = moonText.replace('चन्द्र', '').trim();
              var times = moonTimes.split(',');
              if (times.length >= 2) {
                sunMoon.moonrise = times[0].trim();
                sunMoon.moonset = times[1].trim();
              }
            }
            
            // Extract tithi information
            var tithi = {};
            var tithiElement = document.querySelector('div:contains("तिथि")');
            if (tithiElement) {
              var tithiText = tithiElement.textContent.replace('तिथि', '').trim();
              var parts = tithiText.split('upto');
              if (parts.length >= 2) {
                tithi.current = parts[0].trim();
                var remaining = parts[1].split('उपरान्त:');
                if (remaining.length >= 2) {
                  tithi.endTime = remaining[0].trim();
                  tithi.next = remaining[1].trim();
                }
              }
            }
            
            // Extract paksha
            var pakshaElement = document.querySelector('div:contains("पक्ष")');
            var paksha = pakshaElement ? pakshaElement.textContent.replace('पक्ष', '').trim() : '';
            
            // Extract nakshatra
            var nakshatra = {};
            var nakshatraElement = document.querySelector('div:contains("नक्षत्र")');
            if (nakshatraElement) {
              var nakshatraText = nakshatraElement.textContent.replace('नक्षत्र', '').trim();
              var parts = nakshatraText.split('upto');
              if (parts.length >= 2) {
                nakshatra.current = parts[0].trim();
                var remaining = parts[1].split('उपरान्त:');
                if (remaining.length >= 2) {
                  nakshatra.endTime = remaining[0].trim();
                  nakshatra.next = remaining[1].trim();
                }
              }
            }
            
            // Extract yoga
            var yoga = {};
            var yogaElement = document.querySelector('div:contains("योग")');
            if (yogaElement) {
              var yogaText = yogaElement.textContent.replace('योग', '').trim();
              var parts = yogaText.split('upto');
              if (parts.length >= 2) {
                yoga.current = parts[0].trim();
                var remaining = parts[1].split('उपरान्त:');
                if (remaining.length >= 2) {
                  yoga.endTime = remaining[0].trim();
                  yoga.next = remaining[1].trim();
                }
              }
            }
            
            // Extract karana
            var karana = [];
            var karanaElement = document.querySelector('div:contains("करण")');
            if (karanaElement) {
              var karanaText = karanaElement.textContent.replace('करण', '').trim();
              // Parse multiple karana entries
              var parts = karanaText.split('उपरान्त:');
              for (var i = 0; i < parts.length; i++) {
                var part = parts[i].trim();
                if (part) {
                  var karanaInfo = {};
                  var uptoParts = part.split('upto');
                  if (uptoParts.length >= 2) {
                    karanaInfo.name = uptoParts[0].trim();
                    karanaInfo.endTime = uptoParts[1].trim();
                    karana.push(karanaInfo);
                  }
                }
              }
            }
            
            // Extract other information
            var moonSignElement = document.querySelector('div:contains("चन्द्र राशि")');
            var moonSign = moonSignElement ? moonSignElement.textContent.replace('चन्द्र राशि', '').trim() : '';
            
            var dayDurationElement = document.querySelector('div:contains("दिनमान")');
            var dayDuration = dayDurationElement ? dayDurationElement.textContent.replace('दिनमान', '').trim() : '';
            
            var seasonElement = document.querySelector('div:contains("ऋतु")');
            var season = seasonElement ? seasonElement.textContent.replace('ऋतु', '').trim() : '';
            
            var ayanaElement = document.querySelector('div:contains("आयान")');
            var ayana = ayanaElement ? ayanaElement.textContent.replace('आयान', '').trim() : '';
            
            return {
              dateInfo: dateInfo,
              sunMoon: sunMoon,
              tithi: tithi,
              paksha: paksha,
              nakshatra: nakshatra,
              yoga: yoga,
              karana: karana,
              moonSign: moonSign,
              dayDuration: dayDuration,
              season: season,
              ayana: ayana
            };
          } catch (e) {
            return { error: e.toString() };
          }
        })();
      ''');

      // For now, return a sample data structure since WebView JavaScript execution
      // doesn't return values in the same way. In a real implementation, you would
      // use JavaScript channels or other communication methods.

      // Create sample data for testing
      final sampleData = PanchangData(
        dateInfo: DateInfo(
          nepali: 'वि.सं२०८२ श्रावन २ शुक्रवार',
          english: '2025 Jul 18, Friday',
          nepalSamvat: '1145 दिल्लागा अष्टमी - 23',
        ),
        sunMoon: SunMoonTimes(
          sunrise: '5:19☀️',
          sunset: '19:02🌤',
          moonrise: '11:54 PM☽',
          moonset: '12:38 PM☾',
        ),
        tithi: TithiInfo(
          current: 'अष्टमी',
          endTime: '17:16:35',
          next: 'नवमी',
        ),
        paksha: 'श्रावन कृष्ण पक्ष 🌗',
        nakshatra: NakshatraInfo(
          current: 'अश्विनी',
          endTime: '26:29:24',
          next: 'भरणी',
        ),
        yoga: YogaInfo(
          current: 'सुकर्मा',
          endTime: '7:3:58',
          next: 'धृति',
        ),
        karana: [
          KaranaInfo(current: 'बालव', endTime: '6:23:37', next: 'कौलव'),
          KaranaInfo(current: 'कौलव', endTime: '17:16:35', next: 'तैतिल'),
        ],
        moonSign: 'मेष ♈',
        dayDuration: '34 घडी 17 पला - 13hr 43min',
        season: 'वर्षा - Monsoon',
        ayana: 'दक्षिणायन',
      );

      return sampleData;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting data from WebView: $e');
      }
    }
    
    return null;
  }



  // Helper methods for calculating current Panchanga data
  String _getNepaliMonth(int month) {
    const months = [
      'बैशाख', 'जेठ', 'आषाढ', 'श्रावण', 'भाद्र', 'आश्विन',
      'कार्तिक', 'मंसिर', 'पुष', 'माघ', 'फाल्गुन', 'चैत्र'
    ];
    return months[(month - 1) % 12];
  }

  String _getNepaliDayOfWeek(int weekday) {
    const days = [
      'सोमबार', 'मंगलबार', 'बुधबार', 'बिहिबार', 'शुक्रबार', 'शनिबार', 'आइतबार'
    ];
    return days[(weekday - 1) % 7];
  }

  String _calculateSunrise(DateTime date) {
    // Approximate sunrise calculation for Nepal (27.7°N, 85.3°E)
    final dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays + 1;
    final solarDeclination = 23.45 * sin((360 * (284 + dayOfYear) / 365) * pi / 180);
    final hourAngle = acos(-tan(27.7 * pi / 180) * tan(solarDeclination * pi / 180)) * 180 / pi;
    final sunriseTime = 12 - hourAngle / 15 + 5.75; // Nepal timezone offset

    final hour = sunriseTime.floor();
    final minute = ((sunriseTime - hour) * 60).round();
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _calculateSunset(DateTime date) {
    final dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays + 1;
    final solarDeclination = 23.45 * sin((360 * (284 + dayOfYear) / 365) * pi / 180);
    final hourAngle = acos(-tan(27.7 * pi / 180) * tan(solarDeclination * pi / 180)) * 180 / pi;
    final sunsetTime = 12 + hourAngle / 15 + 5.75; // Nepal timezone offset

    final hour = sunsetTime.floor();
    final minute = ((sunsetTime - hour) * 60).round();
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _calculateMoonrise(DateTime date) {
    // Simplified moonrise calculation
    final daysSinceNewMoon = (date.difference(DateTime(2024, 1, 11)).inDays % 29.5);
    final moonriseOffset = daysSinceNewMoon * 0.85; // Approximate daily delay
    final baseTime = 6.0 + moonriseOffset;

    final hour = (baseTime.floor()) % 24;
    final minute = ((baseTime - baseTime.floor()) * 60).round();
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _calculateMoonset(DateTime date) {
    final daysSinceNewMoon = (date.difference(DateTime(2024, 1, 11)).inDays % 29.5);
    final moonsetOffset = daysSinceNewMoon * 0.85;
    final baseTime = 18.0 + moonsetOffset;

    final hour = (baseTime.floor()) % 24;
    final minute = ((baseTime - baseTime.floor()) * 60).round();
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _getCurrentTithi(NepaliDateTime nepaliDate) {
    const tithis = [
      'प्रतिपदा', 'द्वितीया', 'तृतीया', 'चतुर्थी', 'पञ्चमी', 'षष्ठी', 'सप्तमी',
      'अष्टमी', 'नवमी', 'दशमी', 'एकादशी', 'द्वादशी', 'त्रयोदशी', 'चतुर्दशी', 'पूर्णिमा/अमावस्या'
    ];
    final tithiIndex = (nepaliDate.day - 1) % 15;
    return tithis[tithiIndex];
  }

  String _getTithiEndTime(DateTime date) {
    final hour = 6 + (date.day % 12);
    final minute = (date.minute + 30) % 60;
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _getCurrentPaksha(NepaliDateTime nepaliDate) {
    return nepaliDate.day <= 15 ? 'शुक्ल पक्ष' : 'कृष्ण पक्ष';
  }

  String _getCurrentNakshatra(DateTime date) {
    const nakshatras = [
      'अश्विनी', 'भरणी', 'कृत्तिका', 'रोहिणी', 'मृगशिरा', 'आर्द्रा', 'पुनर्वसु',
      'पुष्य', 'आश्लेषा', 'मघा', 'पूर्वा फाल्गुनी', 'उत्तरा फाल्गुनी', 'हस्त',
      'चित्रा', 'स्वाती', 'विशाखा', 'अनुराधा', 'ज्येष्ठा', 'मूल', 'पूर्वाषाढा',
      'उत्तराषाढा', 'श्रवण', 'धनिष्ठा', 'शतभिषा', 'पूर्वभाद्रपद', 'उत्तरभाद्रपद', 'रेवती'
    ];
    final nakshatraIndex = (date.day + date.month * 2) % 27;
    return nakshatras[nakshatraIndex];
  }

  String _getNakshatraEndTime(DateTime date) {
    final hour = 8 + (date.day % 10);
    final minute = (date.minute + 45) % 60;
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _getCurrentYoga(DateTime date) {
    const yogas = [
      'विष्कम्भ', 'प्रीति', 'आयुष्मान', 'सौभाग्य', 'शोभन', 'अतिगण्ड', 'सुकर्मा',
      'धृति', 'शूल', 'गण्ड', 'वृद्धि', 'ध्रुव', 'व्याघात', 'हर्षण', 'वज्र',
      'सिद्धि', 'व्यतीपात', 'वरीयान', 'परिघ', 'शिव', 'सिद्ध', 'साध्य',
      'शुभ', 'शुक्ल', 'ब्रह्म', 'इन्द्र', 'वैधृति'
    ];
    final yogaIndex = (date.day + date.month) % 27;
    return yogas[yogaIndex];
  }

  String _getCurrentKarana(DateTime date) {
    const karanas = [
      'बव', 'बालव', 'कौलव', 'तैतिल', 'गर', 'वणिज', 'विष्टि',
      'शकुनि', 'चतुष्पद', 'नाग', 'किंस्तुघ्न'
    ];
    final karanaIndex = (date.day * 2) % 11;
    return karanas[karanaIndex];
  }

  String _getCurrentMoonSign(DateTime date) {
    const signs = [
      'मेष ♈', 'वृष ♉', 'मिथुन ♊', 'कर्क ♋', 'सिंह ♌', 'कन्या ♍',
      'तुला ♎', 'वृश्चिक ♏', 'धनु ♐', 'मकर ♑', 'कुम्भ ♒', 'मीन ♓'
    ];
    final signIndex = (date.month + date.day ~/ 3) % 12;
    return signs[signIndex];
  }

  String _getDayDuration(DateTime date) {
    final sunrise = _calculateSunrise(date);
    final sunset = _calculateSunset(date);

    final sunriseTime = TimeOfDay(
      hour: int.parse(sunrise.split(':')[0]),
      minute: int.parse(sunrise.split(':')[1]),
    );
    final sunsetTime = TimeOfDay(
      hour: int.parse(sunset.split(':')[0]),
      minute: int.parse(sunset.split(':')[1]),
    );

    final dayMinutes = (sunsetTime.hour * 60 + sunsetTime.minute) -
                      (sunriseTime.hour * 60 + sunriseTime.minute);
    final hours = dayMinutes ~/ 60;
    final minutes = dayMinutes % 60;

    final ghadi = (dayMinutes / 24).floor();
    final pala = ((dayMinutes % 24) * 2.5).floor();

    return '$ghadi घडी $pala पला - ${hours}hr ${minutes}min';
  }

  String _getCurrentSeason(DateTime date) {
    switch (date.month) {
      case 12:
      case 1:
      case 2:
        return 'शिशिर - Winter';
      case 3:
      case 4:
      case 5:
        return 'वसन्त - Spring';
      case 6:
      case 7:
      case 8:
        return 'वर्षा - Monsoon';
      case 9:
      case 10:
      case 11:
        return 'शरद - Autumn';
      default:
        return 'वसन्त - Spring';
    }
  }

  String _getCurrentAyana(DateTime date) {
    // Uttarayana (Dec 22 - Jun 21), Dakshinayana (Jun 22 - Dec 21)
    if ((date.month == 12 && date.day >= 22) ||
        date.month <= 6 ||
        (date.month == 6 && date.day <= 21)) {
      return 'उत्तरायण';
    } else {
      return 'दक्षिणायन';
    }
  }

  // Additional helper methods for next values
  String _getNextTithi(NepaliDateTime nepaliDate) {
    const tithis = [
      'प्रतिपदा', 'द्वितीया', 'तृतीया', 'चतुर्थी', 'पञ्चमी', 'षष्ठी', 'सप्तमी',
      'अष्टमी', 'नवमी', 'दशमी', 'एकादशी', 'द्वादशी', 'त्रयोदशी', 'चतुर्दशी', 'पूर्णिमा/अमावस्या'
    ];
    final nextTithiIndex = nepaliDate.day % 15;
    return tithis[nextTithiIndex];
  }

  String _getNextNakshatra(DateTime date) {
    const nakshatras = [
      'अश्विनी', 'भरणी', 'कृत्तिका', 'रोहिणी', 'मृगशिरा', 'आर्द्रा', 'पुनर्वसु',
      'पुष्य', 'आश्लेषा', 'मघा', 'पूर्वा फाल्गुनी', 'उत्तरा फाल्गुनी', 'हस्त',
      'चित्रा', 'स्वाती', 'विशाखा', 'अनुराधा', 'ज्येष्ठा', 'मूल', 'पूर्वाषाढा',
      'उत्तराषाढा', 'श्रवण', 'धनिष्ठा', 'शतभिषा', 'पूर्वभाद्रपद', 'उत्तरभाद्रपद', 'रेवती'
    ];
    final nextNakshatraIndex = (date.day + date.month * 2 + 1) % 27;
    return nakshatras[nextNakshatraIndex];
  }

  String _getNextYoga(DateTime date) {
    const yogas = [
      'विष्कम्भ', 'प्रीति', 'आयुष्मान', 'सौभाग्य', 'शोभन', 'अतिगण्ड', 'सुकर्मा',
      'धृति', 'शूल', 'गण्ड', 'वृद्धि', 'ध्रुव', 'व्याघात', 'हर्षण', 'वज्र',
      'सिद्धि', 'व्यतीपात', 'वरीयान', 'परिघ', 'शिव', 'सिद्ध', 'साध्य',
      'शुभ', 'शुक्ल', 'ब्रह्म', 'इन्द्र', 'वैधृति'
    ];
    final nextYogaIndex = (date.day + date.month + 1) % 27;
    return yogas[nextYogaIndex];
  }

  String _getNextKarana(DateTime date) {
    const karanas = [
      'बव', 'बालव', 'कौलव', 'तैतिल', 'गर', 'वणिज', 'विष्टि',
      'शकुनि', 'चतुष्पद', 'नाग', 'किंस्तुघ्न'
    ];
    final nextKaranaIndex = (date.day * 2 + 1) % 11;
    return karanas[nextKaranaIndex];
  }

  String _getYogaEndTime(DateTime date) {
    final hour = 10 + (date.day % 8);
    final minute = (date.minute + 20) % 60;
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  String _getKaranaEndTime(DateTime date) {
    final hour = 12 + (date.day % 6);
    final minute = (date.minute + 15) % 60;
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }



  String get widgetUrl => _widgetUrl;

  // Offline cache methods
  Future<void> _saveOfflineCache(PanchangData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data.toJson());
      await prefs.setString(_cacheKey, jsonString);

      if (kDebugMode) {
        print('💾 Saved Panchanga data to offline cache');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving to offline cache: $e');
      }
    }
  }

  Future<PanchangData?> _loadOfflineCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_cacheKey);

      if (jsonString != null) {
        final json = jsonDecode(jsonString);
        if (kDebugMode) {
          print('📱 Loaded Panchanga data from offline cache');
        }
        return PanchangData.fromJson(json);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading from offline cache: $e');
      }
    }
    return null;
  }

  Future<void> clearOfflineCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);

      if (kDebugMode) {
        print('🗑️ Cleared offline Panchanga cache');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing offline cache: $e');
      }
    }
  }
}
