import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/radio_station.dart';

class RadioService {
  static const String _favoritesKey = 'favorite_radio_stations';
  static List<RadioStation> allRadioStations = [];
  
  // Load stations exactly like the GitHub repository
  static Future<void> loadStations() async {
    final remoteUri = Uri.parse(
        'https://cdn.jsdelivr.net/gh/2shrestha22/radio/assets/radio_list.json');
    String data;
    
    if (kDebugMode) {
      // always load from assets in debug mode.
      try {
        data = await rootBundle.loadString("assets/radio_list.json");
      } catch (e) {
        // If assets file doesn't exist, get from remote
        data = await _getRemoteData(remoteUri);
      }
    } else {
      // else get data from github repository
      try {
        data = await http
            .get(remoteUri)
            .timeout(
              const Duration(seconds: 5),
              onTimeout: () => throw const SocketException('Timeout'),
            )
            .then((value) => value.body);
      } catch (_) {
        try {
          data = await rootBundle.loadString("assets/radio_list.json");
        } catch (e) {
          // Fallback to hardcoded data
          data = _getFallbackData();
        }
      }
    }
    
    allRadioStations = (jsonDecode(data) as List)
        .map((e) => RadioStation.fromJson(e))
        .toList();
  }

  static Future<String> _getRemoteData(Uri uri) async {
    try {
      final response = await http.get(uri).timeout(const Duration(seconds: 5));
      return response.body;
    } catch (e) {
      return _getFallbackData();
    }
  }

  static String _getFallbackData() {
    // Return the exact JSON data from GitHub repository
    return '''[
  {
    "id": "Y_OOEauq-U3AB9GcRyuee",
    "name": "Ujyaalo Radio Network",
    "streamUrl": "https://stream-151.zeno.fm/h527zwd11uquv",
    "frequency": 90.0,
    "address": "Ujyaalo Ghar (Behind Central Zoo), Lalitpur - 4, Shanti Chowk, Jawalakhel",
    "province": 3
  },
  {
    "id": "PpdCGSEQ44Ox4uLzlgcin",
    "name": "Radio Kantipur",
    "streamUrl": "https://radio-broadcast.ekantipur.com/stream",
    "frequency": 96.1,
    "address": "Subidhanagar,Tinkune, Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "63DAluhRgl-PcYeixk5eY",
    "name": "Radio Nepal",
    "streamUrl": "http://stream1.radionepal.gov.np/live",
    "frequency": 100.0,
    "address": "Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "8tR2HH2NMt-h-pyog459v",
    "name": "BBC Nepali",
    "streamUrl": "https://a.files.bbci.co.uk/media/live/manifesto/audio/simulcast/hls/nonuk/sbr_low/ak/bbc_nepali_radio.m3u8",
    "frequency": 103.0,
    "address": "Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "XIQtxSR27snR6vkvJD2Al",
    "name": "Kalika FM",
    "streamUrl": "https://streaming.softnep.net:10828/;stream.nsv&type=mp3&volume=70",
    "frequency": 95.2,
    "address": "Bharatpur-10, Chitwan",
    "province": 3
  },
  {
    "id": "hJ2I94Z16tp5n9OmAxva0",
    "name": "Nepali Radio Network",
    "streamUrl": "https://astream.nepalipatro.com.np:8239/index.html",
    "frequency": 99.1,
    "address": "Baneshwor Height Pipalbot Marga, Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "E_k0M6NUzwIBU4i6is8Bz",
    "name": "Image FM",
    "streamUrl": "https://radio-stream-relay-us.hamropatro.com/radio/8020/radio.mp3",
    "frequency": 97.9,
    "address": "Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "rlPmocaRrVIWIH1tG3H6l",
    "name": "Radio Audio",
    "streamUrl": "https://stream-161.zeno.fm/2w81t82wx3duv",
    "frequency": 106.3,
    "address": "Comedy Height Narayaneshor Marg-10, Baneshwor KTM",
    "province": 3
  },
  {
    "id": "p0qy74VnrANpCmab4YJ_i",
    "name": "Swadesh FM",
    "streamUrl": "https://stream-43.zeno.fm/xm2babvnephvv",
    "frequency": 93.2,
    "address": "Basantapur, Madi, Chitwan, Nepal",
    "province": 3
  },
  {
    "id": "WL4IaKwba1BpHxWdjZvuW",
    "name": "Radio Candid",
    "streamUrl": "https://radio-stream-relay-np.hamropatro.com/radio/8030/radio.mp3",
    "frequency": 92.7,
    "address": "Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "oJtKkBrW6ZxwZ6mpOQa-d",
    "name": "Image News",
    "streamUrl": "https://radio-stream-relay-np.hamropatro.com/radio/8050/radio.mp3",
    "frequency": 103.6,
    "address": "Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "gmJrziudHDkXK1NYJg11K",
    "name": "Hits FM",
    "streamUrl": "https://usa15.fastcast4u.com/proxy/hitsfm912?mp=/1",
    "frequency": 91.2,
    "address": "New Baneshwar, Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "XoF2YnA5lSASjlAKxAxVA",
    "name": "Radio Sagarmatha",
    "streamUrl": "http://**************:8004",
    "frequency": 102.4,
    "address": "Lalitpur, Nepal",
    "province": 3
  },
  {
    "id": "fjdlTdyDb17-suIFY-a6V",
    "name": "Radio Annapurna Nepal",
    "streamUrl": "http://stream.prixa.net:8000/annapurna",
    "frequency": 94.0,
    "address": "Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "I1Jug5DZ_H02zOkR_vTJb",
    "name": "Synergy FM",
    "streamUrl": "https://live.itech.host:3880/stream",
    "frequency": 91.6,
    "address": "Bharatpur-10 Chitwan",
    "province": 3
  },
  {
    "id": "-f9nyHjiHr1hhgEgGde-U",
    "name": "Vijaya FM",
    "streamUrl": "https://live.itech.host:3320/stream",
    "frequency": 101.6,
    "address": "Gaindakot-8, Nawalparasi, Nepal",
    "province": 5
  },
  {
    "id": "d1PMbBlcy71dyFmIUSBMK",
    "name": "Radio Janata",
    "streamUrl": "http://streaming.hamropatro.com:8895",
    "frequency": 89.1,
    "address": "Janata Network Minbhawan Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "MDWPpV0SVpL9orCbeQL7K",
    "name": "Citizen FM",
    "streamUrl": "https://streaming.webhostnepal.com:7032/;",
    "frequency": 97.5,
    "address": "MI Complex, Jamal, Kathmandu",
    "province": 3
  },
  {
    "id": "6g5aK_OCZNmsr2OfE88-P",
    "name": "Music City - Nonstop Music",
    "streamUrl": "http://streaming.hamropatro.com:8739",
    "frequency": null,
    "address": null
  },
  {
    "id": "hzQLwASZSbR9xc_zT0tMh",
    "name": "SaptaKoshi FM 90.0 MHz",
    "streamUrl": "https://live.itech.host:4535/stream",
    "frequency": 90.0,
    "address": "Ithari-8, Sunsari, Nepal",
    "province": 1
  },
  {
    "id": "GGsFIWQfHG6qQjc4BKeE3",
    "name": "Radio Kathmandu",
    "streamUrl": "http://**************:8088/;",
    "frequency": 92.1,
    "address": "Chabahil, Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "w-xqQkq10wg-Fyxshi5C4",
    "name": "Nepaliko Radio",
    "streamUrl": "http://*************:9888/",
    "frequency": 88.8,
    "address": "Koteshwor, Kathmandu, Nepal",
    "province": 3
  },
  {
    "id": "7937eooqXAT_2U436bJ2v",
    "name": "Classic FM",
    "streamUrl": "http://streaming.hamropatro.com:8783",
    "frequency": 101.2,
    "address": "Jawalakhel, Lalitpur, Kathmandu",
    "province": 3
  },
  {
    "id": "f9JnqjfBuwpTHfylTbGQJ",
    "name": "Radio Annapurna",
    "streamUrl": "https://live.itech.host:8969/stream",
    "frequency": 93.4,
    "address": "Gairapatnan, Pokhara",
    "province": 4
  },
  {
    "id": "YDG1c7F7Lpx9I0NvNsqYN",
    "name": "Radio Barahi",
    "streamUrl": "http://streaming.softnep.net:8083/;",
    "frequency": 99.2,
    "address": "Rastrabank Chowk, Pokhara",
    "province": 4
  }
]''';
  }

  // Get all radio stations
  static List<RadioStation> getAllRadioStations() {
    return allRadioStations;
  }

  // Get working radio stations only
  static List<RadioStation> getWorkingRadioStations() {
    return allRadioStations.where((station) => !station.streamUrl.isEmpty).toList();
  }

  // Favorite stations management
  static Future<List<String>> getFavoriteStationIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_favoritesKey) ?? [];
    } catch (e) {
      return [];
    }
  }

  static Future<void> addToFavorites(String stationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteStationIds();
      if (!favorites.contains(stationId)) {
        favorites.add(stationId);
        await prefs.setStringList(_favoritesKey, favorites);
      }
    } catch (e) {
      // Silent error handling
    }
  }

  static Future<void> removeFromFavorites(String stationId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await getFavoriteStationIds();
      favorites.remove(stationId);
      await prefs.setStringList(_favoritesKey, favorites);
    } catch (e) {
      // Silent error handling
    }
  }

  static Future<List<RadioStation>> getFavoriteStations() async {
    final favoriteIds = await getFavoriteStationIds();
    final allStations = getWorkingRadioStations();
    return allStations.where((station) => favoriteIds.contains(station.id)).toList();
  }

  static Future<bool> isFavorite(String stationId) async {
    final favorites = await getFavoriteStationIds();
    return favorites.contains(stationId);
  }
}
