import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/rasifal_model.dart';

class RasifalService {
  // API endpoint from the GitHub repo
  static const String _baseUrl = 'https://rashifal-api.vercel.app/api';
  
  // Zodiac signs with their API names
  static final List<ZodiacSign> _zodiacSigns = [
    ZodiacSign(nepaliName: 'मेष', englishName: 'Aries', emoji: '♈', apiName: 'mesh'),
    ZodiacSign(nepaliName: 'वृष', englishName: 'Taurus', emoji: '♉', apiName: 'brish'),
    ZodiacSign(nepaliName: 'मिथुन', englishName: 'Gemini', emoji: '♊', apiName: 'mithun'),
    ZodiacSign(nepaliName: 'कर्कट', englishName: 'Cancer', emoji: '♋', apiName: 'karkat'),
    ZodiacSign(nepaliName: 'सिंह', englishName: 'Leo', emoji: '♌', apiName: 'singh'),
    ZodiacSign(nepaliName: 'कन्या', englishName: 'Virgo', emoji: '♍', apiName: 'kanya'),
    ZodiacSign(nepaliName: 'तुला', englishName: 'Libra', emoji: '♎', apiName: 'tula'),
    ZodiacSign(nepaliName: 'वृश्चिक', englishName: 'Scorpio', emoji: '♏', apiName: 'brischik'),
    ZodiacSign(nepaliName: 'धनु', englishName: 'Sagittarius', emoji: '♐', apiName: 'dhanu'),
    ZodiacSign(nepaliName: 'मकर', englishName: 'Capricorn', emoji: '♑', apiName: 'makar'),
    ZodiacSign(nepaliName: 'कुम्भ', englishName: 'Aquarius', emoji: '♒', apiName: 'kumbha'),
    ZodiacSign(nepaliName: 'मीन', englishName: 'Pisces', emoji: '♓', apiName: 'meen'),
  ];

  // Get all zodiac signs
  static List<ZodiacSign> getAllZodiacSigns() {
    return _zodiacSigns;
  }

  // Get zodiac sign by name
  static ZodiacSign? getZodiacSign(String name) {
    try {
      return _zodiacSigns.firstWhere(
        (sign) => sign.nepaliName == name || sign.englishName == name || sign.apiName == name,
      );
    } catch (e) {
      return null;
    }
  }

  // Get horoscope for a specific sign
  static Future<RasifalModel?> getRasifal(String signName) async {
    try {
      final zodiacSign = getZodiacSign(signName);
      if (zodiacSign == null) {
        return null;
      }

      // Try to get from API first
      final apiResult = await _getFromAPI(zodiacSign.apiName);
      if (apiResult != null) {
        return RasifalModel(
          sign: zodiacSign.nepaliName,
          horoscope: apiResult,
          date: _getCurrentDate(),
        );
      }

      // Fallback to generated content
      return _generateFallbackRasifal(zodiacSign);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting Rasifal: $e');
      }
      
      // Return fallback content
      final zodiacSign = getZodiacSign(signName);
      if (zodiacSign != null) {
        return _generateFallbackRasifal(zodiacSign);
      }
      return null;
    }
  }

  // Try to get from the API
  static Future<String?> _getFromAPI(String apiName) async {
    try {
      final url = '$_baseUrl?sign=$apiName';
      final response = await http.get(Uri.parse(url)).timeout(
        const Duration(seconds: 10),
      );

      if (response.statusCode == 200) {
        final data = response.body;
        if (data.isNotEmpty && !data.contains('error') && !data.contains('Error')) {
          return data.trim();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('API call failed: $e');
      }
    }
    return null;
  }

  // Generate fallback horoscope content
  static RasifalModel _generateFallbackRasifal(ZodiacSign zodiacSign) {
    final horoscopes = _getHoroscopeTemplates(zodiacSign.nepaliName);
    final random = Random();
    final selectedHoroscope = horoscopes[random.nextInt(horoscopes.length)];

    return RasifalModel(
      sign: zodiacSign.nepaliName,
      horoscope: selectedHoroscope,
      date: _getCurrentDate(),
    );
  }

  // Get current date
  static String _getCurrentDate() {
    final now = DateTime.now();
    return '${now.year}/${now.month.toString().padLeft(2, '0')}/${now.day.toString().padLeft(2, '0')}';
  }

  // Get horoscope templates for each sign
  static List<String> _getHoroscopeTemplates(String signName) {
    final commonTemplates = [
      'आजको दिन तपाईंको लागि शुभ छ। नयाँ अवसरहरू आउन सक्छन्।',
      'आर्थिक मामिलामा सावधानी अपनाउनुहोस्। स्वास्थ्यमा ध्यान दिनुहोस्।',
      'पारिवारिक सम्बन्धमा सुधार आउने संकेत छ। काममा सफलता मिल्नेछ।',
      'आजको दिन मिश्रित फल दिने छ। धैर्य राख्नुहोस्।',
      'नयाँ योजनाहरू बनाउने उत्तम समय हो। सकारात्मक सोच राख्नुहोस्।',
    ];

    // Sign-specific templates
    switch (signName) {
      case 'मेष':
        return [
          'आजको दिन तपाईंको साहस र उत्साहले काम आउनेछ। नेतृत्व गुणको प्रयोग गर्नुहोस्।',
          'व्यापारिक क्षेत्रमा राम्रो प्रगति हुनेछ। स्वास्थ्यमा केही समस्या आउन सक्छ।',
          ...commonTemplates,
        ];
      case 'वृष':
        return [
          'आर्थिक स्थिति सुधार हुनेछ। पारिवारिक खुशीको वातावरण रहनेछ।',
          'धैर्य र स्थिरताले तपाईंलाई सफलता दिलाउनेछ। प्रेम सम्बन्धमा मधुरता आउनेछ।',
          ...commonTemplates,
        ];
      case 'मिथुन':
        return [
          'संचार र सम्पर्कको क्षेत्रमा राम्रो दिन हुनेछ। नयाँ मित्रता बन्न सक्छ।',
          'बौद्धिक कामहरूमा सफलता मिल्नेछ। यात्राको योजना बन्न सक्छ।',
          ...commonTemplates,
        ];
      case 'कर्कट':
        return [
          'घर परिवारको मामिलामा खुशीको खबर आउनेछ। भावनात्मक सन्तुलन राख्नुहोस्।',
          'माता र महिलाहरूको साथ पाउनुहुनेछ। खानपानमा सावधानी अपनाउनुहोस्।',
          ...commonTemplates,
        ];
      case 'सिंह':
        return [
          'आजको दिन तपाईंको व्यक्तित्वले सबैलाई प्रभावित पार्नेछ। सम्मान पाउनुहुनेछ।',
          'रचनात्मक कामहरूमा सफलता मिल्नेछ। आत्मविश्वास बढ्नेछ।',
          ...commonTemplates,
        ];
      case 'कन्या':
        return [
          'विस्तृत योजना र व्यवस्थापनले राम्रो नतिजा दिनेछ। स्वास्थ्यमा सुधार हुनेछ।',
          'सेवा र सहयोगको कामले पुण्य कमाउनेछ। अध्ययनमा राम्रो प्रगति हुनेछ।',
          ...commonTemplates,
        ];
      case 'तुला':
        return [
          'सम्बन्ध र साझेदारीमा सन्तुलन कायम राख्नुहोस्। न्याय र निष्पक्षताले जित्नुहुनेछ।',
          'कलात्मक कामहरूमा सफलता मिल्नेछ। सुन्दरताको कदर गर्नुहोस्।',
          ...commonTemplates,
        ];
      case 'वृश्चिक':
        return [
          'गहिरो अनुसन्धान र खोजले नयाँ सत्य प्रकट गर्नेछ। रहस्यमय कुराहरू खुल्नेछ।',
          'परिवर्तन र रूपान्तरणको समय आएको छ। साहसका साथ अगाडि बढ्नुहोस्।',
          ...commonTemplates,
        ];
      case 'धनु':
        return [
          'ज्ञान र दर्शनको खोजमा सफलता मिल्नेछ। लामो यात्राको योजना बन्न सक्छ।',
          'शिक्षा र गुरुको मार्गदर्शनले फाइदा पुर्याउनेछ। आशावादी बन्नुहोस्।',
          ...commonTemplates,
        ];
      case 'मकर':
        return [
          'कडा मेहनत र अनुशासनले सफलताको शिखरमा पुर्याउनेछ। धैर्य राख्नुहोस्।',
          'करियर र प्रतिष्ठामा वृद्धि हुनेछ। जिम्मेवारी बढ्न सक्छ।',
          ...commonTemplates,
        ];
      case 'कुम्भ':
        return [
          'नवाचार र आविष्कारले नयाँ बाटो देखाउनेछ। समुदायिक काममा सहयोग गर्नुहोस्।',
          'मित्रता र सामाजिक सम्पर्कले फाइदा पुर्याउनेछ। भविष्यको योजना बनाउनुहोस्।',
          ...commonTemplates,
        ];
      case 'मीन':
        return [
          'कल्पना र सपनाले वास्तविकताको रूप लिनेछ। आध्यात्मिक उन्नति हुनेछ।',
          'दया र करुणाले मन शान्त राख्नेछ। कलात्मक प्रतिभाको विकास हुनेछ।',
          ...commonTemplates,
        ];
      default:
        return commonTemplates;
    }
  }
}
