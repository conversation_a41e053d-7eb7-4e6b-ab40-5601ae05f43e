import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/share_market.dart';

class ShareMarketService {
  // Data source URL - Auto-updates when data changes
  static const String sheetUrl = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRAwAz0EFptGEVc6zbrDUC2hSNNzPpvQ3_RpHMCUbWAtFt_ZnvIysMATyJRu9I0EoULo8DWymJJzdRF/pub?output=csv';
  
  static const String cacheKey = 'share_market_cache';
  static const String cacheTimeKey = 'share_market_cache_time';
  static const int cacheValidityMinutes = 15; // Cache for 15 minutes

  static Future<List<ShareMarket>> getShareMarketData({bool forceRefresh = false}) async {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        final cachedData = await _getCachedData();
        if (cachedData != null && cachedData.isNotEmpty) {
          return cachedData;
        }
      }

      // Always try to fetch fresh data from data source
      final response = await http.get(
        Uri.parse(sheetUrl),
        headers: {
          'Accept': 'text/csv; charset=utf-8',
          'Accept-Charset': 'utf-8',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        // Parse data with proper UTF-8 decoding
        String csvData = utf8.decode(response.bodyBytes).trim();
        List<ShareMarket> shares = [];

        // Split by lines and process each line
        List<String> lines = csvData.split('\n');

        // Skip header line and process data
        for (int i = 1; i < lines.length; i++) {
          String line = lines[i].trim();
          if (line.isEmpty || line.split(',').length < 7) continue;

          try {
            // Split by comma - CSV format
            List<String> parts = line.split(',');
            if (parts.length >= 7) {
              String symbol = parts[0].trim().replaceAll('"', '');
              String ltp = parts[1].trim();
              String changePercent = parts[2].trim();
              String open = parts[3].trim();
              String high = parts[4].trim();
              String low = parts[5].trim();
              String quantity = parts[6].trim();

              // Skip empty symbols or invalid data
              if (symbol.isNotEmpty && ltp.isNotEmpty) {
                shares.add(ShareMarket(
                  symbol: symbol,
                  ltp: ltp,
                  changePercent: changePercent,
                  open: open,
                  high: high,
                  low: low,
                  quantity: quantity,
                ));
              }
            }
          } catch (e) {
            // Skip invalid lines
            continue;
          }
        }

        // If parsing fails, use fallback data
        if (shares.length < 50) {
          shares = _getFallbackData();
        }

        // Cache the data
        await _cacheShareMarketData(shares);

        return shares;
      } else {
        // API failed - return cached data if available
        final cachedData = await _getCachedData();
        if (cachedData != null && cachedData.isNotEmpty) {
          return cachedData;
        }
        return _getFallbackData();
      }
    } catch (e) {
      // Network error - return cached data if available
      final cachedData = await _getCachedData();
      if (cachedData != null && cachedData.isNotEmpty) {
        return cachedData;
      }
      return _getFallbackData();
    }
  }

  static Future<void> _cacheShareMarketData(List<ShareMarket> shares) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = shares.map((s) => {
        'symbol': s.symbol,
        'ltp': s.ltp,
        'changePercent': s.changePercent,
        'open': s.open,
        'high': s.high,
        'low': s.low,
        'quantity': s.quantity,
      }).toList();
      
      final jsonString = json.encode(jsonData);
      await prefs.setString(cacheKey, jsonString);
      await prefs.setInt(cacheTimeKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // Silent error handling
    }
  }

  static Future<List<ShareMarket>?> _getCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(cacheKey);
      final cacheTime = prefs.getInt(cacheTimeKey);
      
      if (cachedJson != null && cacheTime != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        final cacheAge = now - cacheTime;
        final maxAge = cacheValidityMinutes * 60 * 1000; // Convert to milliseconds
        
        if (cacheAge < maxAge) {
          final List<dynamic> jsonList = json.decode(cachedJson);
          return jsonList.map((json) => ShareMarket(
            symbol: json['symbol'] ?? '',
            ltp: json['ltp'] ?? '',
            changePercent: json['changePercent'] ?? '',
            open: json['open'] ?? '',
            high: json['high'] ?? '',
            low: json['low'] ?? '',
            quantity: json['quantity'] ?? '',
          )).toList();
        }
      }
    } catch (e) {
      // Silent error handling
    }
    return null;
  }

  // Fallback data for offline use
  static List<ShareMarket> _getFallbackData() {
    return [
      // Sample NEPSE data
      ShareMarket(symbol: 'NABIL', ltp: '548.66', changePercent: '1.35', open: '554.00', high: '542.20', low: '545.00', quantity: '192,877'),
      ShareMarket(symbol: 'ADBL', ltp: '331.60', changePercent: '1.37', open: '333.50', high: '328.00', low: '328.00', quantity: '187,049'),
      ShareMarket(symbol: 'EBL', ltp: '730.96', changePercent: '4.19', open: '735.00', high: '695.00', low: '702.00', quantity: '231,952'),
      ShareMarket(symbol: 'KBL', ltp: '221.77', changePercent: '0.91', open: '226.00', high: '220.10', low: '224.00', quantity: '684,458'),
      ShareMarket(symbol: 'NBL', ltp: '299.67', changePercent: '2.82', open: '302.00', high: '295.00', low: '297.20', quantity: '695,151'),
      ShareMarket(symbol: 'SANIMA', ltp: '374.37', changePercent: '-0.11', open: '380.00', high: '372.00', low: '377.00', quantity: '699,852'),
      ShareMarket(symbol: 'MBL', ltp: '261.69', changePercent: '0.88', open: '268.40', high: '260.20', low: '263.90', quantity: '313,401'),
      ShareMarket(symbol: 'PRVU', ltp: '227.46', changePercent: '2.51', open: '228.40', high: '222.50', low: '223.00', quantity: '424,438'),
      ShareMarket(symbol: 'GBIME', ltp: '264.56', changePercent: '1.92', open: '278.90', high: '260.00', low: '262.00', quantity: '556,423'),
      ShareMarket(symbol: 'CZBIL', ltp: '230.69', changePercent: '1.06', open: '234.00', high: '227.00', low: '230.90', quantity: '219,237'),
    ];
  }

  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(cacheKey);
      await prefs.remove(cacheTimeKey);
    } catch (e) {
      // Silent error handling
    }
  }
}
