import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/simple_vegetable.dart';

class SimpleVegetableService {
  // Data source URL - Auto-updates when data changes
  static const String sheetUrl = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vQHTWjgPqTnicuFCnSCwIhlAvhJMRsh51QZxooJtSszHKiSHnLBFxfzSJjaiAr2YNxI6z16PI1LwboO/pub?output=csv';

  static const String cacheKey = 'vegetable_data_cache';
  static const String cacheTimeKey = 'vegetable_cache_time';
  static const int cacheValidityMinutes = 15; // Cache for 15 minutes

  static Future<List<SimpleVegetable>> getVegetables({bool forceRefresh = false}) async {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        final cachedData = await _getCachedData();
        if (cachedData != null && cachedData.isNotEmpty) {
          return cachedData;
        }
      }

      // Always try to fetch fresh data from data source

      final response = await http.get(
        Uri.parse(sheetUrl),
        headers: {
          'Accept': 'text/csv; charset=utf-8',
          'Accept-Charset': 'utf-8',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        // Parse data with proper UTF-8 decoding and BOM handling
        List<int> bytes = response.bodyBytes;

        // Remove BOM if present
        if (bytes.length >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF) {
          bytes = bytes.sublist(3);
        }

        String csvData = utf8.decode(bytes, allowMalformed: false).trim();
        List<SimpleVegetable> vegetables = [];

        // Split by lines and process each line
        List<String> lines = csvData.split('\n');

        for (String line in lines) {
          line = line.trim();
          if (line.isNotEmpty &&
              !line.startsWith('कृषि उपज') &&
              !line.startsWith('If you have any question') &&
              !line.contains('<EMAIL>') &&
              line.contains(',')) {

            try {
              // Split by comma - CSV format with proper quote handling
              List<String> parts = _parseCSVLine(line);

              if (parts.length >= 4) {
                String fullName = parts[0].trim();
                String minPrice = parts[1].trim();
                String maxPrice = parts[2].trim();
                String avgPrice = parts[3].trim();

                // Clean quotes and normalize text
                fullName = _cleanText(fullName);
                minPrice = _cleanText(minPrice);
                maxPrice = _cleanText(maxPrice);
                avgPrice = _cleanText(avgPrice);

                if (fullName.isNotEmpty && minPrice.isNotEmpty && maxPrice.isNotEmpty && avgPrice.isNotEmpty) {
                  vegetables.add(SimpleVegetable(
                    name: fullName,
                    price: '$minPrice,$maxPrice,$avgPrice',
                    unit: '',
                    category: 'सबै',
                    emoji: '',
                  ));
                }
              }
            } catch (e) {
              // Skip problematic lines
              continue;
            }
          }
        }

        // If parsing fails, use fallback data
        if (vegetables.length < 90) {
          vegetables = _getFallbackData();
        }

        // Cache the fresh data
        await _cacheData(vegetables);

        return vegetables;
      } else {
        // API failed - return cached data if available
        final cachedData = await _getCachedData();
        if (cachedData != null && cachedData.isNotEmpty) {
          return cachedData;
        }
        return _getFallbackData();
      }
    } catch (e) {
      // Network error - return cached data if available
      final cachedData = await _getCachedData();
      if (cachedData != null && cachedData.isNotEmpty) {
        return cachedData;
      }
      return _getFallbackData();
    }
  }

  // Cache management
  static Future<void> _cacheData(List<SimpleVegetable> vegetables) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(vegetables.map((v) => {
        'name': v.name,
        'price': v.price,
        'unit': v.unit,
        'category': v.category,
        'emoji': v.emoji,
      }).toList());

      await prefs.setString(cacheKey, jsonString);
      await prefs.setInt(cacheTimeKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // Silent error handling
    }
  }

  static Future<List<SimpleVegetable>?> _getCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(cacheKey);
      final cacheTime = prefs.getInt(cacheTimeKey);

      if (cachedJson != null && cacheTime != null) {
        final cacheAge = DateTime.now().millisecondsSinceEpoch - cacheTime;
        final cacheValidityMs = cacheValidityMinutes * 60 * 1000;

        if (cacheAge < cacheValidityMs) {
          final List<dynamic> jsonData = json.decode(cachedJson);
          return jsonData.map((item) => SimpleVegetable(
            name: item['name'],
            price: item['price'],
            unit: item['unit'],
            category: item['category'],
            emoji: item['emoji'],
          )).toList();
        }
      }
    } catch (e) {
      print('Error reading cached vegetable data: $e');
    }
    return null;
  }

  // Fallback data for offline use
  static List<SimpleVegetable> _getFallbackData() {
    return [
      // Fallback vegetable data
      SimpleVegetable(name: 'गोलभेडा ठूलो(नेपाली) (के.जी.)', price: 'रू ६०,रू ७०,रू ६५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'गोलभेडा सानो(लोकल) (के.जी.)', price: 'रू २२,रू ३०,रू २५.६७', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'गोलभेडा सानो(टनेल) (के जी)', price: 'रू ३३,रू ४४,रू ३९.२०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'आलु रातो (के.जी.)', price: 'रू ४२,रू ४५,रू ४३.५०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'आलु रातो(भारतीय) (के जी)', price: 'रू ३६,रू ४०,रू ३८.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'आलु रातो(मुडे) (केजी)', price: 'रू ३६,रू ३९,रू ३७.५०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'आलु सेतो (के.जी.)', price: 'रू ३६,रू ३८,रू ३७.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'प्याज सुकेको (भारतीय) (के.जी.)', price: 'रू ४७,रू ४८,रू ४७.७५', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'गाजर(लोकल) (के.जी.)', price: 'रू ९०,रू १००,रू ९५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'गाजर(तराई) (केजी)', price: 'रू ८०,रू ९०,रू ८५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'बन्दा(लोकल) (के.जी.)', price: 'रू २०,रू ३०,रू २६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'काउली स्थानिय (के.जी.)', price: 'रू ४०,रू ५५,रू ५१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'काउली तराई (के.जी.)', price: 'रू ३०,रू ४०,रू ३५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'मूला रातो (के.जी.)', price: 'रू ३५,रू ४५,रू ४१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'सेतो मूला(हाइब्रीड) (केजी)', price: 'रू १५,रू २५,रू १९.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'भन्टा लाम्चो (के.जी.)', price: 'रू २५,रू ३५,रू ३०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'भन्टा डल्लो (के.जी.)', price: 'रू ३५,रू ४५,रू ४२.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'बोडी(तने) (के.जी.)', price: 'रू १५,रू २५,रू १९.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'मकै बोडी (केजी)', price: 'रू ४५,रू ५०,रू ४७.५०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'मटरकोशा (के.जी.)', price: 'रू १८०,रू २००,रू १९०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'घिउ सिमी(लोकल) (के.जी.)', price: 'रू ३०,रू ४०,रू ३६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'घिउ सिमी(हाइब्रीड) (केजी)', price: 'रू ४०,रू ५०,रू ४६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'घिउ सिमी(राजमा) (केजी)', price: 'रू १००,रू १२०,रू ११०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'टाटे सिमी (के.जी.)', price: 'रू ६०,रू ७०,रू ६५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'भटमासकोशा (के.जी.)', price: 'रू ९०,रू १३०,रू ११०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'तितो करेला (के.जी.)', price: 'रू १०,रू २०,रू १७.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'लौका (के.जी.)', price: 'रू १५,रू २५,रू २१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'परवर(तराई) (केजी)', price: 'रू ३०,रू ३५,रू ३२.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'चिचिण्डो (के.जी.)', price: 'रू २०,रू ३०,रू २५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'घिरौला (के.जी.)', price: 'रू ३०,रू ४०,रू ३६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'झिगूनी (के.जी.)', price: 'रू ४०,रू ४५,रू ४१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'फर्सी पाकेको (के.जी.)', price: 'रू ४०,रू ५०,रू ४५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'फर्सी हरियो(लाम्चो) (के.जी.)', price: 'रू १५,रू २५,रू २१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'हरियो फर्सी(डल्लो) (केजी)', price: 'रू २५,रू ३५,रू ३१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'भिण्डी (के.जी.)', price: 'रू २५,रू ३५,रू ३१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'सखरखण्ड (के.जी.)', price: 'रू ७०,रू ८०,रू ७५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'पिंडालू (के.जी.)', price: 'रू ६०,रू ७०,रू ६५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'स्कूस (के.जी.)', price: 'रू ३०,रू ४०,रू ३४.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'रायो साग (के.जी.)', price: 'रू ७०,रू ८०,रू ७६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'पालूगो साग (के.जी.)', price: 'रू ८०,रू ९०,रू ८५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'तोरीको साग (के.जी.)', price: 'रू ७०,रू ८०,रू ७५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'प्याज हरियो (के.जी.)', price: 'रू १४०,रू १६०,रू १५०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'च्याउ(कन्य) (के.जी.)', price: 'रू १२०,रू १५०,रू १३५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'च्याउ(डल्ले) (के जी)', price: 'रू ४००,रू ४५०,रू ४२५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'कुरीलो (के.जी.)', price: 'रू १८०,रू २५०,रू २१०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'न्यूरो (के.जी.)', price: 'रू ८०,रू ९०,रू ८५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'ब्रोकाउली (के.जी.)', price: 'रू ६०,रू ७०,रू ६६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'चुकुन्दर (के.जी.)', price: 'रू ६५,रू ७५,रू ७०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'सजिवन (के.जी.)', price: 'रू २००,रू २२०,रू २१०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'रातो बन्दा (के.जी.)', price: 'रू ३००,रू ३३०,रू ३१७.५०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'जिरीको साग (के.जी.)', price: 'रू ८०,रू १००,रू ९०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'सेलरी (के.जी.)', price: 'रू १००,रू १२०,रू ११०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'पार्सले (के.जी.)', price: 'रू १०००,रू १२००,रू १,१००.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'सौफको साग (के.जी.)', price: 'रू १८०,रू २००,रू १९०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'पुदीना (के.जी.)', price: 'रू १५०,रू २००,रू १७५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'इमली (के.जी.)', price: 'रू १९०,रू २००,रू १९५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'तामा (के.जी.)', price: 'रू १००,रू १२०,रू ११०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'तोफु (के.जी.)', price: 'रू १००,रू १२०,रू ११०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'गुन्दुक (के.जी.)', price: 'रू ३००,रू ३५०,रू ३२५.००', unit: '', category: 'सबै', emoji: ''),

      // Fruits
      SimpleVegetable(name: 'स्याउ(झोले) (के.जी.)', price: 'रू २२०,रू २५०,रू २३५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'स्याउ(फूजी) (के जी)', price: 'रू ३००,रू ३४०,रू ३२०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'केरा (दर्जन)', price: 'रू १२५,रू १३५,रू १३०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'कागती (के.जी.)', price: 'रू १००,रू १५०,रू १२६.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'अनार (के.जी.)', price: 'रू ३००,रू ३५०,रू ३२२.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'आँप(मालदह) (के.जी.)', price: 'रू ११५,रू १३०,रू १२२.५०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'आँप(दसहरी) (केजी)', price: 'रू १२०,रू १३०,रू १२५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'तरबुजा(हरियो) (के.जी.)', price: 'रू १००,रू ११०,रू १०५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'मौसम (के.जी.)', price: 'रू १३०,रू १६०,रू १४५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'जुनार (के.जी.)', price: 'रू २२०,रू २५०,रू २३५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'भुई कटहर (प्रति गोटा)', price: 'रू १४०,रू १६०,रू १५०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'काक्रो(लोकल) (के.जी.)', price: 'रू ४०,रू ५०,रू ४५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'काक्रो(हाइब्रीड) (के जी)', price: 'रू १०,रू २०,रू १५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'रुख कटहर (के.जी.)', price: 'रू ३५,रू ४५,रू ४१.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'नासपाती(लोकल) (के.जी.)', price: 'रू ३०,रू ४०,रू ३३.७५', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'मेवा(भारतीय) (केजी)', price: 'रू ९०,रू १००,रू ९५.००', unit: '', category: 'सबै', emoji: ''),

      // Spices and seasonings
      SimpleVegetable(name: 'अदुवा (के.जी.)', price: 'रू ८०,रू १००,रू ९०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'खु्र्सानी सुकेको (के.जी.)', price: 'रू ३००,रू ३५०,रू ३२५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'खु्र्सानी हरियो (के.जी.)', price: 'रू ४०,रू ४५,रू ४२.५०', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'खुर्सानी हरियो(बुलेट) (के जी)', price: 'रू २५,रू ३५,रू ३०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'खुर्सानी हरियो(माछे) (के जी)', price: 'रू ७०,रू ८०,रू ७५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'खुर्सानी हरियो(अकबरे) (के जी)', price: 'रू १५०,रू २२०,रू १९८.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'भेडे खु्र्सानी (के.जी.)', price: 'रू ८०,रू ९५,रू ८८.७५', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'लसुन हरियो (के.जी.)', price: 'रू १२०,रू १५०,रू १३५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'हरियो धनिया (के.जी.)', price: 'रू १५०,रू २००,रू १७५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'लसुन सुकेको चाइनिज (के.जी.)', price: 'रू १८०,रू २००,रू १९०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'लसुन सुकेको नेपाली (के.जी.)', price: 'रू १४०,रू १६०,रू १५०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'छ्यापी सुकेको (के.जी.)', price: 'रू १६०,रू १८०,रू १७०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'छ्यापी हरियो (के.जी.)', price: 'रू १२०,रू १३०,रू १२५.००', unit: '', category: 'सबै', emoji: ''),

      // Fish and Mushrooms
      SimpleVegetable(name: 'ताजा माछा(रहु) (के जी)', price: 'रू ३१०,रू ३३०,रू ३२०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'ताजा माछा(बचुवा) (के जी)', price: 'रू ३००,रू ३२०,रू ३१०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'ताजा माछा(छडी) (के जी)', price: 'रू २२०,रू २५०,रू २३५.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'राजा च्याउ (के.जी.)', price: 'रू २८०,रू ३००,रू २९०.००', unit: '', category: 'सबै', emoji: ''),
      SimpleVegetable(name: 'सिताके च्याउ (के.जी.)', price: 'रू ८००,रू ९००,रू ८५०.००', unit: '', category: 'सबै', emoji: ''),
    ];
  }

  // Helper method to determine category based on vegetable name
  static String _getCategory(String name) {
    name = name.toLowerCase();

    if (name.contains('साग') || name.contains('पालूगो') || name.contains('रायो') ||
        name.contains('तोरी') || name.contains('बन्दा') || name.contains('धनिया') ||
        name.contains('पुदीना') || name.contains('सेलरी') || name.contains('पार्सले') ||
        name.contains('जिरी') || name.contains('सौफ')) {
      return 'साग';
    } else if (name.contains('खु्र्सानी') || name.contains('अदुवा') || name.contains('लसुन') ||
               name.contains('छ्यापी') || name.contains('भेडे') || name.contains('धनिया') ||
               name.contains('हरियो धनिया')) {
      return 'मसला';
    } else if (name.contains('स्याउ') || name.contains('केरा') || name.contains('कागती') ||
               name.contains('आँप') || name.contains('अनार') || name.contains('तरबुजा') ||
               name.contains('मौसम') || name.contains('जुनार') || name.contains('कटहर') ||
               name.contains('नासपाती') || name.contains('मेवा') || name.contains('काक्रो')) {
      return 'फलफूल';
    } else if (name.contains('माछा') || name.contains('रहु') || name.contains('बचुवा') ||
               name.contains('छडी')) {
      return 'माछा';
    } else {
      return 'तरकारी';
    }
  }

  // Helper method to get emoji based on vegetable name
  static String _getEmoji(String name) {
    name = name.toLowerCase();

    if (name.contains('गोलभेडा')) return '🍅';
    if (name.contains('आलु')) return '🥔';
    if (name.contains('प्याज')) return '🧅';
    if (name.contains('गाजर')) return '🥕';
    if (name.contains('काउली')) return '🥦';
    if (name.contains('भन्टा')) return '🍆';
    if (name.contains('खु्र्सानी')) return '🌶️';
    if (name.contains('अदुवा')) return '🫚';
    if (name.contains('लसुन')) return '🧄';
    if (name.contains('स्याउ')) return '🍎';
    if (name.contains('केरा')) return '🍌';
    if (name.contains('कागती')) return '🍋';
    if (name.contains('आँप')) return '🥭';
    if (name.contains('अनार')) return '🍎';
    if (name.contains('साग') || name.contains('बन्दा') || name.contains('पालूगो')) return '🥬';
    if (name.contains('च्याउ')) return '🍄';
    if (name.contains('मूला')) return '🥬';
    if (name.contains('सिमी')) return '🫘';
    if (name.contains('करेला')) return '🥒';
    if (name.contains('भिण्डी')) return '🌶️';
    if (name.contains('तरबुजा')) return '🍉';
    if (name.contains('जुनार') || name.contains('मौसम')) return '🍊';
    if (name.contains('अंगूर')) return '🍇';
    if (name.contains('माछा')) return '🐟';
    if (name.contains('लौका')) return '🥒';
    if (name.contains('परवर')) return '🥒';
    if (name.contains('फर्सी')) return '🥒';
    if (name.contains('सखरखण्ड')) return '🍠';

    // Default emoji for unknown vegetables
    return '🥬';
  }

  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(cacheKey);
      await prefs.remove(cacheTimeKey);
    } catch (e) {
      // Silent error handling
    }
  }

  // Helper method to parse CSV line with proper quote handling
  static List<String> _parseCSVLine(String line) {
    List<String> result = [];
    StringBuffer current = StringBuffer();
    bool inQuotes = false;

    for (int i = 0; i < line.length; i++) {
      String char = line[i];

      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == ',' && !inQuotes) {
        result.add(current.toString());
        current.clear();
      } else {
        current.write(char);
      }
    }

    result.add(current.toString());
    return result;
  }

  // Helper method to clean text and handle encoding issues
  static String _cleanText(String text) {
    if (text.isEmpty) return text;

    // Remove quotes
    text = text.replaceAll('"', '');

    // Remove any control characters
    text = text.replaceAll(RegExp(r'[\x00-\x1F\x7F]'), '');

    // Normalize whitespace
    text = text.replaceAll(RegExp(r'\s+'), ' ').trim();

    return text;
  }

  // Keep original Nepali numbers and text
  static String _convertDevanagariToEnglish(String nepaliText) {
    // Don't convert - keep original Nepali text and numbers
    return nepaliText;
  }

  // Convert English numbers to Nepali numbers for proper display
  static String _convertToNepaliNumbers(String text) {
    Map<String, String> englishToNepali = {
      '0': '०', '1': '१', '2': '२', '3': '३', '4': '४',
      '5': '५', '6': '६', '7': '७', '8': '८', '9': '९'
    };

    String result = text;
    englishToNepali.forEach((english, nepali) {
      result = result.replaceAll(english, nepali);
    });

    return result;
  }
}
