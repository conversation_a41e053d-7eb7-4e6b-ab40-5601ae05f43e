import '../models/unit_conversion_model.dart';
import 'dart:math';

class UnitConversionService {
  
  // Convert between any two units of the same type
  static ConversionResult convert(double value, Unit fromUnit, Unit toUnit) {
    // Convert to base unit first, then to target unit
    double baseValue = value * fromUnit.conversionFactor;
    double convertedValue = baseValue / toUnit.conversionFactor;
    
    // Round to avoid floating point precision issues
    convertedValue = _roundToPrecision(convertedValue, 8);
    
    String? municipalFormat;
    
    // Generate municipal format for Nepali area units
    if (toUnit.name == 'Ropani' || fromUnit.name == 'Ropani') {
      municipalFormat = _generateMunicipalFormat(baseValue);
    }
    
    return ConversionResult(
      value: convertedValue,
      unit: toUnit,
      municipalFormat: municipalFormat,
    );
  }
  
  // Convert multiple units at once
  static List<ConversionResult> convertToMultiple(
    double value, 
    Unit fromUnit, 
    List<Unit> toUnits
  ) {
    return toUnits.map((toUnit) => convert(value, fromUnit, toUnit)).toList();
  }
  
  // Get all possible conversions for a unit type
  static List<ConversionResult> getAllConversions(
    double value, 
    Unit fromUnit, 
    UnitType unitType
  ) {
    return unitType.units
        .where((unit) => unit.name != fromUnit.name)
        .map((toUnit) => convert(value, fromUnit, toUnit))
        .toList();
  }
  
  // Generate Nepali municipal format (Ropani-Ana-Paisa-Dam)
  static String _generateMunicipalFormat(double squareMeters) {
    // Convert to Ropani first
    double ropaniValue = squareMeters / 508.73704704;
    
    // Extract Ropani
    int ropani = ropaniValue.floor();
    double remainder = ropaniValue - ropani;
    
    // Extract Ana (16 Ana = 1 Ropani)
    double anaValue = remainder * 16;
    int ana = anaValue.floor();
    remainder = anaValue - ana;
    
    // Extract Paisa (4 Paisa = 1 Ana)
    double paisaValue = remainder * 4;
    int paisa = paisaValue.floor();
    remainder = paisaValue - paisa;
    
    // Extract Dam (4 Dam = 1 Paisa)
    double damValue = remainder * 4;
    int dam = damValue.round();
    
    // Handle overflow
    if (dam >= 4) {
      dam = 0;
      paisa += 1;
    }
    if (paisa >= 4) {
      paisa = 0;
      ana += 1;
    }
    if (ana >= 16) {
      ana = 0;
      ropani += 1;
    }
    
    return '$ropani रो. $ana आ. $paisa पै. $dam दा.';
  }
  
  // Generate Terai format (Bigha-Kattha-Dhur)
  static String _generateTeraiFormat(double squareMeters) {
    // Convert to Bigha first
    double bighaValue = squareMeters / 6772.631616;
    
    // Extract Bigha
    int bigha = bighaValue.floor();
    double remainder = bighaValue - bigha;
    
    // Extract Kattha (20 Kattha = 1 Bigha)
    double katthaValue = remainder * 20;
    int kattha = katthaValue.floor();
    remainder = katthaValue - kattha;
    
    // Extract Dhur (20 Dhur = 1 Kattha)
    double dhurValue = remainder * 20;
    int dhur = dhurValue.round();
    
    // Handle overflow
    if (dhur >= 20) {
      dhur = 0;
      kattha += 1;
    }
    if (kattha >= 20) {
      kattha = 0;
      bigha += 1;
    }
    
    return '$bigha बि. $kattha क. $dhur धु.';
  }
  
  // Convert from municipal format to square meters
  static double convertFromMunicipalFormat(int ropani, int ana, int paisa, int dam) {
    double squareMeters = 0;
    
    // Convert each unit to square meters
    squareMeters += ropani * 508.73704704;  // Ropani to sq.m
    squareMeters += ana * 31.79605175;      // Ana to sq.m
    squareMeters += paisa * 7.949012938;    // Paisa to sq.m
    squareMeters += dam * 1.98725409;       // Dam to sq.m
    
    return squareMeters;
  }
  
  // Convert from Terai format to square meters
  static double convertFromTeraiFormat(int bigha, int kattha, int dhur) {
    double squareMeters = 0;
    
    // Convert each unit to square meters
    squareMeters += bigha * 6772.631616;   // Bigha to sq.m
    squareMeters += kattha * 338.6315808;  // Kattha to sq.m
    squareMeters += dhur * 16.93157904;    // Dhur to sq.m
    
    return squareMeters;
  }
  
  // Break feet into feet and inches format
  static String breakFeet(double feet) {
    int wholeFeet = feet.floor();
    double remainderInches = (feet - wholeFeet) * 12;
    
    int wholeInches = remainderInches.floor();
    double fractionInches = remainderInches - wholeInches;
    
    String fractionStr = '';
    if (fractionInches >= 0.875) {
      fractionStr = '';
      wholeInches += 1;
    } else if (fractionInches >= 0.8125) {
      fractionStr = ' 7/8';
    } else if (fractionInches >= 0.6875) {
      fractionStr = ' 3/4';
    } else if (fractionInches >= 0.5625) {
      fractionStr = ' 5/8';
    } else if (fractionInches >= 0.4375) {
      fractionStr = ' 1/2';
    } else if (fractionInches >= 0.3125) {
      fractionStr = ' 3/8';
    } else if (fractionInches >= 0.1875) {
      fractionStr = ' 1/4';
    } else if (fractionInches >= 0.0625) {
      fractionStr = ' 1/8';
    }
    
    // Handle inch overflow
    if (wholeInches >= 12) {
      wholeFeet += wholeInches ~/ 12;
      wholeInches = wholeInches % 12;
    }
    
    return "$wholeFeet' $wholeInches$fractionStr\"";
  }
  
  // Round to specified precision
  static double _roundToPrecision(double value, int precision) {
    double factor = pow(10, precision).toDouble();
    return (value * factor).round() / factor;
  }
  
  // Get popular conversions for a unit type
  static List<Unit> getPopularUnits(UnitType unitType) {
    switch (unitType.name) {
      case 'Length':
        return unitType.units.where((unit) => 
          ['Meter', 'Feet', 'Inch', 'Kilometer'].contains(unit.name)
        ).toList();
      case 'Area':
        return unitType.units.where((unit) => 
          ['Square Meter', 'Square Feet', 'Ropani', 'Bigha', 'Hectare'].contains(unit.name)
        ).toList();
      case 'Weight':
        return unitType.units.where((unit) => 
          ['Kilogram', 'Gram', 'Pound', 'Mana', 'Ser'].contains(unit.name)
        ).toList();
      case 'Volume':
        return unitType.units.where((unit) => 
          ['Liter', 'Milliliter', 'Gallon', 'Mana'].contains(unit.name)
        ).toList();
      default:
        return unitType.units.take(5).toList();
    }
  }
  
  // Validate input value
  static bool isValidInput(String input) {
    if (input.isEmpty) return false;
    final double? value = double.tryParse(input);
    return value != null && value >= 0;
  }
  
  // Format number for display
  static String formatNumber(double number) {
    if (number == number.toInt()) {
      return number.toInt().toString();
    } else if (number < 0.001) {
      return number.toStringAsExponential(2);
    } else {
      return number.toStringAsFixed(6).replaceAll(RegExp(r'0*$'), '').replaceAll(RegExp(r'\.$'), '');
    }
  }
}
