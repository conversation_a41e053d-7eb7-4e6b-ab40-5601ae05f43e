import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/user_data.dart';

class UserDataService {
  // File-based persistent storage
  static const String _userDataFileName = 'kundali_user_data.json';
  static const String _selectedUserFileName = 'selected_user.txt';

  // Cache for performance
  static List<UserData>? _cachedUserDataList;
  static String? _cachedSelectedUserId;

  // Get application documents directory path
  static Future<String> _getDataPath() async {
    if (kIsWeb) {
      // For web, use a simple approach
      return '';
    }

    try {
      // Use path_provider for proper app directory
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    } catch (e) {
      print('Error getting documents directory: $e');
      // Fallback to application support directory
      try {
        final directory = await getApplicationSupportDirectory();
        return directory.path;
      } catch (e2) {
        print('Error getting support directory: $e2');
        // Final fallback to temporary directory
        final directory = await getTemporaryDirectory();
        return directory.path;
      }
    }
  }

  // Save user data list to file
  static Future<bool> saveUserDataList(List<UserData> userDataList) async {
    try {
      final dataPath = await _getDataPath();
      final file = File('$dataPath/$_userDataFileName');

      final jsonList = userDataList.map((userData) => userData.toJson()).toList();
      final jsonString = json.encode(jsonList);

      await file.writeAsString(jsonString);
      _cachedUserDataList = List.from(userDataList);

      return true;
    } catch (e) {
      print('Error saving user data list: $e');
      // Fallback to memory storage
      _cachedUserDataList = List.from(userDataList);
      return true;
    }
  }

  // Load user data list from file
  static Future<List<UserData>> loadUserDataList() async {
    try {
      // Return cached data if available
      if (_cachedUserDataList != null) {
        return List.from(_cachedUserDataList!);
      }

      final dataPath = await _getDataPath();
      final file = File('$dataPath/$_userDataFileName');

      if (!await file.exists()) {
        _cachedUserDataList = [];
        return [];
      }

      final jsonString = await file.readAsString();
      if (jsonString.isEmpty) {
        _cachedUserDataList = [];
        return [];
      }

      final jsonList = json.decode(jsonString) as List;
      final userDataList = jsonList.map((json) => UserData.fromJson(json)).toList();

      _cachedUserDataList = userDataList;
      return List.from(userDataList);
    } catch (e) {
      print('Error loading user data list: $e');
      _cachedUserDataList = [];
      return [];
    }
  }

  // Add new user data
  static Future<bool> addUserData(UserData userData) async {
    try {
      final userDataList = await loadUserDataList();
      userDataList.add(userData);
      return await saveUserDataList(userDataList);
    } catch (e) {
      print('Error adding user data: $e');
      return false;
    }
  }

  // Update existing user data
  static Future<bool> updateUserData(UserData updatedUserData) async {
    try {
      final userDataList = await loadUserDataList();
      final index = userDataList.indexWhere((userData) => userData.id == updatedUserData.id);

      if (index != -1) {
        userDataList[index] = updatedUserData;
        return await saveUserDataList(userDataList);
      }
      return false;
    } catch (e) {
      print('Error updating user data: $e');
      return false;
    }
  }

  // Delete user data
  static Future<bool> deleteUserData(String userId) async {
    try {
      final userDataList = await loadUserDataList();
      userDataList.removeWhere((userData) => userData.id == userId);

      // Clear selected user if it's the one being deleted
      final selectedUserId = await _getSelectedUserId();
      if (selectedUserId == userId) {
        await clearSelectedUser();
      }

      return await saveUserDataList(userDataList);
    } catch (e) {
      print('Error deleting user data: $e');
      return false;
    }
  }

  // Get user data by ID
  static Future<UserData?> getUserDataById(String userId) async {
    try {
      final userDataList = await loadUserDataList();
      return userDataList.firstWhere(
        (userData) => userData.id == userId,
        orElse: () => throw Exception('User not found'),
      );
    } catch (e) {
      print('Error getting user data by ID: $e');
      return null;
    }
  }

  // Set selected user
  static Future<bool> setSelectedUser(String userId) async {
    try {
      final dataPath = await _getDataPath();
      final file = File('$dataPath/$_selectedUserFileName');

      await file.writeAsString(userId);
      _cachedSelectedUserId = userId;

      return true;
    } catch (e) {
      print('Error setting selected user: $e');
      // Fallback to memory
      _cachedSelectedUserId = userId;
      return true;
    }
  }

  // Get selected user ID from file
  static Future<String?> _getSelectedUserId() async {
    try {
      // Return cached data if available
      if (_cachedSelectedUserId != null) {
        return _cachedSelectedUserId;
      }

      final dataPath = await _getDataPath();
      final file = File('$dataPath/$_selectedUserFileName');

      if (!await file.exists()) {
        return null;
      }

      final userId = await file.readAsString();
      _cachedSelectedUserId = userId.isEmpty ? null : userId;

      return _cachedSelectedUserId;
    } catch (e) {
      print('Error getting selected user ID: $e');
      return null;
    }
  }

  // Get selected user
  static Future<UserData?> getSelectedUser() async {
    try {
      final selectedUserId = await _getSelectedUserId();

      if (selectedUserId == null) {
        return null;
      }

      return await getUserDataById(selectedUserId);
    } catch (e) {
      print('Error getting selected user: $e');
      return null;
    }
  }

  // Clear selected user
  static Future<bool> clearSelectedUser() async {
    try {
      final dataPath = await _getDataPath();
      final file = File('$dataPath/$_selectedUserFileName');

      if (await file.exists()) {
        await file.delete();
      }

      _cachedSelectedUserId = null;
      return true;
    } catch (e) {
      print('Error clearing selected user: $e');
      _cachedSelectedUserId = null;
      return true;
    }
  }

  // Clear all user data
  static Future<bool> clearAllUserData() async {
    try {
      final dataPath = await _getDataPath();

      // Delete user data file
      final userDataFile = File('$dataPath/$_userDataFileName');
      if (await userDataFile.exists()) {
        await userDataFile.delete();
      }

      // Delete selected user file
      final selectedUserFile = File('$dataPath/$_selectedUserFileName');
      if (await selectedUserFile.exists()) {
        await selectedUserFile.delete();
      }

      // Clear cache
      _cachedUserDataList = [];
      _cachedSelectedUserId = null;

      return true;
    } catch (e) {
      print('Error clearing all user data: $e');
      // Clear cache anyway
      _cachedUserDataList = [];
      _cachedSelectedUserId = null;
      return true;
    }
  }

  // Get user count
  static Future<int> getUserCount() async {
    try {
      final userDataList = await loadUserDataList();
      return userDataList.length;
    } catch (e) {
      print('Error getting user count: $e');
      return 0;
    }
  }

  // Check if user exists by name
  static Future<bool> userExistsByName(String name) async {
    try {
      final userDataList = await loadUserDataList();
      return userDataList.any((userData) =>
        userData.name.toLowerCase() == name.toLowerCase());
    } catch (e) {
      print('Error checking if user exists: $e');
      return false;
    }
  }

  // Get users by district
  static Future<List<UserData>> getUsersByDistrict(String district) async {
    try {
      final userDataList = await loadUserDataList();
      return userDataList.where((userData) => userData.district == district).toList();
    } catch (e) {
      print('Error getting users by district: $e');
      return [];
    }
  }

  // Search users by name
  static Future<List<UserData>> searchUsersByName(String searchTerm) async {
    try {
      final userDataList = await loadUserDataList();
      final lowerSearchTerm = searchTerm.toLowerCase();
      return userDataList.where((userData) =>
        userData.name.toLowerCase().contains(lowerSearchTerm)).toList();
    } catch (e) {
      print('Error searching users by name: $e');
      return [];
    }
  }

  // Get recently added users (last 5)
  static Future<List<UserData>> getRecentUsers({int limit = 5}) async {
    try {
      final userDataList = await loadUserDataList();
      final sortedList = List<UserData>.from(userDataList);
      sortedList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return sortedList.take(limit).toList();
    } catch (e) {
      print('Error getting recent users: $e');
      return [];
    }
  }

  // Export user data as JSON string
  static Future<String?> exportUserData() async {
    try {
      final userDataList = await loadUserDataList();
      final exportData = {
        'exportDate': DateTime.now().toIso8601String(),
        'userCount': userDataList.length,
        'users': userDataList.map((userData) => userData.toJson()).toList(),
      };
      return json.encode(exportData);
    } catch (e) {
      print('Error exporting user data: $e');
      return null;
    }
  }

  // Import user data from JSON string
  static Future<bool> importUserData(String jsonString) async {
    try {
      final importData = json.decode(jsonString);
      final usersList = importData['users'] as List;
      final userDataList = usersList.map((json) => UserData.fromJson(json)).toList();
      return await saveUserDataList(userDataList);
    } catch (e) {
      print('Error importing user data: $e');
      return false;
    }
  }
}
