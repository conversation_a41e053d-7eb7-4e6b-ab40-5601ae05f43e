import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/user_data.dart';
import 'github_api_manager.dart';
import 'error_handler_service.dart';

/// Centralized service for all Vedic Astro API calls
/// All current and future features should use this service
class VedicAstroApiService {
  
  /// Make API call with automatic key management and secure error handling
  static Future<dynamic> makeApiCall({
    required String endpoint,
    required UserData user,
    String language = 'ne', // Default to Nepali
    Map<String, String>? additionalParams,
  }) async {
    return await ErrorHandlerService.handleApiCall(() async {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      // Build query parameters
      final params = <String, String>{
        'api_key': apiKey,
        'dob': user.formattedDateForAPI,
        'tob': user.formattedTimeForAPI,
        'lat': user.latitude.toString(),
        'lon': user.longitude.toString(),
        'tz': user.timezoneForAPI,
        'lang': language,
      };

      // Add any additional parameters
      if (additionalParams != null) {
        params.addAll(additionalParams);
      }

      // Build URL
      final uri = Uri.parse('$baseUrl/$endpoint').replace(queryParameters: params);

      // Log for debugging (only in debug mode, without sensitive info)
      if (kDebugMode) {
        print('🌐 Making API call to: $endpoint');
        print('🔑 Using API key: ${apiKey.substring(0, 8)}...');
        print('🔗 Full URL: ${uri.toString().replaceAll(apiKey, '***API_KEY***')}');
      }

      // Make HTTP request with timeout
      final response = await http.get(uri).timeout(const Duration(seconds: 30));

      if (kDebugMode) {
        print('📡 Response Status: ${response.statusCode}');
        print('📡 Response Headers: ${response.headers}');
        print('📡 Response Body (first 500 chars): ${response.body.substring(0, response.body.length > 500 ? 500 : response.body.length)}...');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (kDebugMode) {
          print('📊 Parsed Data Status: ${data['status']}');
          print('📊 Parsed Data Keys: ${data.keys.toList()}');
        }

        if (data['status'] == 200) {
          if (kDebugMode) {
            print('✅ API call successful: $endpoint');
            print('✅ Response type: ${data['response'].runtimeType}');
          }
          return data['response'];
        } else {
          // Log detailed error for debugging
          if (kDebugMode) {
            print('❌ API error status: ${data['status']}');
            print('❌ API error message: ${data['message'] ?? 'Unknown error'}');
            print('❌ Full error response: $data');
          }
          throw Exception('सेवा अस्थायी रूपमा अनुपलब्ध छ: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        if (kDebugMode) {
          print('❌ HTTP error: ${response.statusCode}');
          print('❌ HTTP error body: ${response.body}');
        }
        throw HttpException('सर्भर समस्या: ${response.statusCode}');
      }
    });
  }

  /// Special API call handler for chart image endpoints that return SVG/image data
  static Future<dynamic> makeChartImageApiCall({
    required String endpoint,
    required UserData user,
    String language = 'ne',
    Map<String, String>? additionalParams,
  }) async {
    return await ErrorHandlerService.handleApiCall(() async {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      // Build query parameters
      final params = <String, String>{
        'api_key': apiKey,
        'dob': user.formattedDateForAPI,
        'tob': user.formattedTimeForAPI,
        'lat': user.latitude.toString(),
        'lon': user.longitude.toString(),
        'tz': user.timezoneForAPI,
        'lang': language,
      };

      // Add any additional parameters
      if (additionalParams != null) {
        params.addAll(additionalParams);
      }

      // Build URL
      final uri = Uri.parse('$baseUrl/$endpoint').replace(queryParameters: params);

      // Log for debugging (only in debug mode, without sensitive info)
      if (kDebugMode) {
        print('🌐 Making Chart Image API call to: $endpoint');
        print('🔑 Using API key: ${apiKey.substring(0, 8)}...');
        print('🔗 Full URL: ${uri.toString().replaceAll(apiKey, '***API_KEY***')}');
      }

      // Make HTTP request with timeout
      final response = await http.get(uri).timeout(const Duration(seconds: 30));

      if (kDebugMode) {
        print('📡 Chart Image Response Status: ${response.statusCode}');
        print('📡 Chart Image Response Headers: ${response.headers}');
        print('📡 Chart Image Response Body Length: ${response.body.length}');
        print('📡 Chart Image Response Body (first 200 chars): ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');
      }

      if (response.statusCode == 200) {
        // For chart image APIs, try to parse as JSON first
        try {
          final data = json.decode(response.body);

          if (kDebugMode) {
            print('📊 Chart Image Parsed as JSON - Status: ${data['status']}');
            print('📊 Chart Image Parsed Data Keys: ${data.keys.toList()}');
          }

          if (data['status'] == 200) {
            if (kDebugMode) {
              print('✅ Chart Image API call successful: $endpoint');
              print('✅ Chart Image Response type: ${data['response'].runtimeType}');
            }
            return data['response'];
          } else {
            if (kDebugMode) {
              print('❌ Chart Image API error status: ${data['status']}');
              print('❌ Chart Image API error message: ${data['message'] ?? 'Unknown error'}');
            }
            throw Exception('चार्ट लोड गर्न सकिएन: ${data['message'] ?? 'Unknown error'}');
          }
        } catch (e) {
          // If JSON parsing fails, assume it's raw SVG/image data
          if (kDebugMode) {
            print('📊 Chart Image Response is not JSON, treating as raw data');
            print('📊 Raw data length: ${response.body.length}');
          }
          return response.body;
        }
      } else {
        if (kDebugMode) {
          print('❌ Chart Image HTTP error: ${response.statusCode}');
          print('❌ Chart Image HTTP error body: ${response.body}');
        }
        throw HttpException('चार्ट सर्भर समस्या: ${response.statusCode}');
      }
    });
  }

  /// Mangal Dosh Analysis
  static Future<Map<String, dynamic>?> getMangalDosh(UserData user) async {
    return await makeApiCall(
      endpoint: 'dosha/mangal-dosh',
      user: user,
    );
  }
  
  /// Manglik Dosh Analysis
  static Future<Map<String, dynamic>?> getManglikDosh(UserData user) async {
    return await makeApiCall(
      endpoint: 'dosha/manglik-dosh',
      user: user,
    );
  }
  
  /// Kaalsarp Dosh Analysis
  static Future<Map<String, dynamic>?> getKaalsarpDosh(UserData user) async {
    return await makeApiCall(
      endpoint: 'dosha/kaalsarp-dosh',
      user: user,
    );
  }

  /// Papasamaya Analysis
  static Future<Map<String, dynamic>?> getPapasamaya(UserData user) async {
    return await makeApiCall(
      endpoint: 'dosha/papasamaya',
      user: user,
    );
  }

  /// Pitra Dosh Analysis
  static Future<Map<String, dynamic>?> getPitraDosh(UserData user) async {
    return await makeApiCall(
      endpoint: 'dosha/pitra-dosh',
      user: user,
    );
  }

  /// Mahadasha Analysis
  static Future<Map<String, dynamic>?> getMahadasha(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/maha-dasha',
      user: user,
    );
  }

  /// Mahadasha Predictions
  static Future<Map<String, dynamic>?> getMahadashaPredictions(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/maha-dasha-predictions',
      user: user,
    );
  }

  /// Antardasha Analysis
  static Future<Map<String, dynamic>?> getAntardasha(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/antar-dasha',
      user: user,
    );
  }

  /// Char Dasha Current
  static Future<Map<String, dynamic>?> getCharDashaCurrent(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/char-dasha-current',
      user: user,
    );
  }

  /// Char Dasha Main
  static Future<Map<String, dynamic>?> getCharDashaMain(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/char-dasha-main',
      user: user,
    );
  }

  /// Char Dasha Sub
  static Future<Map<String, dynamic>?> getCharDashaSub(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/char-dasha-sub',
      user: user,
    );
  }

  /// Current Mahadasha
  static Future<Map<String, dynamic>?> getCurrentMahadasha(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/current-mahadasha',
      user: user,
    );
  }

  /// Current Mahadasha Full
  static Future<Map<String, dynamic>?> getCurrentMahadashaFull(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/current-mahadasha-full',
      user: user,
    );
  }

  /// Paryantar Dasha
  static Future<Map<String, dynamic>?> getParyantarDasha(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/paryantar-dasha',
      user: user,
    );
  }

  /// Yogini Dasha Main
  static Future<Map<String, dynamic>?> getYoginiDashaMain(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/yogini-dasha-main',
      user: user,
    );
  }

  /// Yogini Dasha Sub
  static Future<Map<String, dynamic>?> getYoginiDashaSub(UserData user) async {
    return await makeApiCall(
      endpoint: 'dashas/yogini-dasha-sub',
      user: user,
    );
  }

  /// Find Sun Sign
  static Future<Map<String, dynamic>?> findSunSign(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/find-sun-sign',
      user: user,
    );
  }

  /// Find Moon Sign
  static Future<Map<String, dynamic>?> findMoonSign(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/find-moon-sign',
      user: user,
    );
  }

  /// Find Ascendant
  static Future<Map<String, dynamic>?> findAscendant(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/find-ascendant',
      user: user,
    );
  }

  /// Current Sade Sati
  static Future<Map<String, dynamic>?> getCurrentSadeSati(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/current-sade-sati',
      user: user,
    );
  }

  /// Extended Kundli Details
  static Future<Map<String, dynamic>?> getExtendedKundliDetails(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/extended-kundli-details',
      user: user,
    );
  }

  /// Shad Bala
  static Future<Map<String, dynamic>?> getShadBala(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/shad-bala',
      user: user,
    );
  }

  /// Sade Sati Table
  static Future<dynamic> getSadeSatiTable(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/sade-sati-table',
      user: user,
    );
  }

  /// Friendship Table
  static Future<dynamic> getFriendshipTable(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/friendship',
      user: user,
    );
  }

  /// KP Houses
  static Future<dynamic> getKPHouses(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/kp-houses',
      user: user,
    );
  }

  /// KP Planets
  static Future<dynamic> getKPPlanets(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/kp-planets',
      user: user,
    );
  }

  /// Gem Suggestion
  static Future<dynamic> getGemSuggestion(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/gem-suggestion',
      user: user,
    );
  }

  /// Numero Table
  static Future<dynamic> getNumeroTable(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/numero-table',
      user: user,
      additionalParams: {'name': user.name},
    );
  }

  /// Rudraksh Suggestion
  static Future<dynamic> getRudrakshSuggestion(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/rudraksh-suggestion',
      user: user,
    );
  }

  /// Varshapal Details
  static Future<dynamic> getVarshapalDetails(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/varshapal-details',
      user: user,
    );
  }

  /// Varshapal Month Chart
  static Future<dynamic> getVarshapalMonthChart(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/varshapal-month-chart',
      user: user,
    );
  }

  /// Varshapal Year Chart
  static Future<dynamic> getVarshapalYearChart(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/varshapal-year-chart',
      user: user,
    );
  }

  /// Arutha Padas
  static Future<dynamic> getAruthaPadas(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/arutha-padas',
      user: user,
    );
  }

  /// List of Yogas
  static Future<dynamic> getYogaList(UserData user) async {
    return await makeApiCall(
      endpoint: 'extended-horoscope/yoga-list',
      user: user,
    );
  }

  /// Planet Details
  static Future<dynamic> getPlanetDetails(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/planet-details',
      user: user,
    );
  }

  /// Ascendant Report
  static Future<dynamic> getAscendantReport(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/ascendant-report',
      user: user,
    );
  }

  /// Planet Report
  static Future<dynamic> getPlanetReport(UserData user, String planet) async {
    return await makeApiCall(
      endpoint: 'horoscope/planet-report',
      user: user,
      additionalParams: {'planet': planet},
    );
  }

  /// Personal Characteristics
  static Future<dynamic> getPersonalCharacteristics(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/personal-characteristics',
      user: user,
    );
  }

  /// Divisional Charts
  static Future<dynamic> getDivisionalCharts(UserData user, String div) async {
    return await makeApiCall(
      endpoint: 'horoscope/divisional-charts',
      user: user,
      additionalParams: {
        'div': div,
        'response_type': 'planet_object',
        'transit_date': '11/05/2022',
      },
    );
  }

  /// Chart Image - Special handling for image APIs
  static Future<dynamic> getChartImage(UserData user, String div) async {
    print('🔍 Chart Image API Call - Div: $div');
    return await makeChartImageApiCall(
      endpoint: 'horoscope/chart-image',
      user: user,
      language: 'ne', // Ensure Nepali language
      additionalParams: {
        'div': div,
        'style': 'north',
        'stroke': '4', // Even thicker lines
        'color': '%23000000', // Black color for better visibility
        'font_size': '32', // Larger font for better readability
        'font_style': 'Arial', // Better font for Nepali
        'colorful_planets': 'true',
        'format': 'utf8',
        'size': '800', // Even larger chart size
        'font_weight': 'bold', // Bold text
        'number_font_size': '36', // Bigger numbers
        'number_font_weight': 'bold', // Bold numbers
      },
    );
  }

  /// Ashtakvarga Chart Image - Special handling for image APIs
  static Future<dynamic> getAshtakvargaChartImage(UserData user, String planet) async {
    print('🔍 Ashtakvarga API Call - Planet: $planet');
    return await makeChartImageApiCall(
      endpoint: 'horoscope/ashtakvarga-chart-image',
      user: user,
      language: 'ne', // Ensure Nepali language
      additionalParams: {
        'style': 'south',
        'color': '%23000000', // Black color for better visibility
        'planet': planet,
        'size': '700', // Even larger chart size
        'font_size': '32', // Even larger font for better readability
        'font_style': 'Arial', // Better font for Nepali
        'stroke': '3', // Thicker lines
        'format': 'utf8',
        'font_weight': 'bold', // Bold text
      },
    );
  }

  /// Ashtakvarga
  static Future<dynamic> getAshtakvarga(UserData user, String planet) async {
    return await makeApiCall(
      endpoint: 'horoscope/ashtakvarga',
      user: user,
      additionalParams: {
        'planet': planet,
      },
    );
  }

  /// Binnashtakvarga
  static Future<dynamic> getBinnashtakvarga(UserData user, String planet) async {
    return await makeApiCall(
      endpoint: 'horoscope/binnashtakvarga',
      user: user,
      additionalParams: {
        'planet': planet,
      },
    );
  }

  /// AI 12 Month Prediction
  static Future<dynamic> getAI12MonthPrediction(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/ai-12-month-prediction',
      user: user,
    );
  }

  /// Planetary Aspects
  static Future<dynamic> getPlanetaryAspects(UserData user, String type) async {
    return await makeApiCall(
      endpoint: 'horoscope/planetary-aspects',
      user: user,
      additionalParams: {
        'type': type,
      },
    );
  }

  /// Planets In Houses
  static Future<dynamic> getPlanetsInHouses(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/planets-in-houses',
      user: user,
    );
  }

  /// North Match (Ashtakoot) - Marriage Compatibility
  static Future<dynamic> getNorthMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/ashtakoot');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch North Match data: $e');
    }
  }

  /// North Match with Astro Details - Detailed Marriage Compatibility
  static Future<dynamic> getNorthMatchWithAstroDetails(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/ashtakoot-with-astro-details');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch North Match with Astro Details: $e');
    }
  }

  /// South Match (Dashakoot) - South Indian Marriage Compatibility
  static Future<dynamic> getSouthMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/dashakoot');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch South Match data: $e');
    }
  }

  /// South Match with Astro Details - Detailed South Indian Marriage Compatibility
  static Future<dynamic> getSouthMatchWithAstroDetails(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/dashakoot-with-astro-details');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch South Match with Astro Details: $e');
    }
  }

  /// Aggregate Match - Comprehensive Marriage Compatibility Analysis
  static Future<dynamic> getAggregateMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/aggregate-match');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Aggregate Match data: $e');
    }
  }

  /// Rajju Vedha Match - Specialized Marriage Compatibility Analysis
  static Future<dynamic> getRajjuVedhaMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/rajju-vedha-details');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Rajju Vedha Match data: $e');
    }
  }

  /// Papasamaya Match - Inauspicious Time Marriage Compatibility
  static Future<dynamic> getPapasamayaMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/papasamaya-match');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_dob': '${boyUser.birthDateBS.day.toString().padLeft(2, '0')}/${boyUser.birthDateBS.month.toString().padLeft(2, '0')}/${boyUser.birthDateBS.year}',
        'boy_tob': boyUser.birthTime,
        'boy_tz': '5.5',
        'boy_lat': boyUser.latitude.toString(),
        'boy_lon': boyUser.longitude.toString(),
        'girl_dob': '${girlUser.birthDateBS.day.toString().padLeft(2, '0')}/${girlUser.birthDateBS.month.toString().padLeft(2, '0')}/${girlUser.birthDateBS.year}',
        'girl_tob': girlUser.birthTime,
        'girl_tz': '5.5',
        'girl_lat': girlUser.latitude.toString(),
        'girl_lon': girlUser.longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Papasamaya Match data: $e');
    }
  }

  /// Nakshatra Match - Birth Star Compatibility Analysis
  static Future<dynamic> getNakshatraMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/nakshatra-match');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_star': '11', // Default star number - should be calculated from birth data
        'girl_star': '5', // Default star number - should be calculated from birth data
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Nakshatra Match data: $e');
    }
  }

  /// Western Match - Zodiac Sign Compatibility Analysis
  static Future<dynamic> getWesternMatch(UserData boyUser, UserData girlUser) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/western-match');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'boy_sign': '1', // Default sign number - should be calculated from birth data
        'girl_sign': '1', // Default sign number - should be calculated from birth data
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Western Match data: $e');
    }
  }

  /// Bulk South Match - Multiple Partner Compatibility Analysis
  static Future<dynamic> getBulkSouthMatch(UserData nativeUser, List<UserData> matchUsers) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/matching/bulk-south-match');

      // Prepare match details array - simplified for demo
      final matchDetails = matchUsers.asMap().entries.map((entry) {
        final index = entry.key;
        final user = entry.value;
        return [
          11, // Default star number - should be calculated from birth data
          5,  // Default rasi number - should be calculated from birth data
          20, // Default asc degrees - should be calculated from birth data
          12, // Default moon degrees - should be calculated from birth data
          30, // Default sun degrees - should be calculated from birth data
          index + 1000, // Reference ID
        ];
      }).toList();

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'native_asc_degrees': '12', // Default - should be calculated from birth data
        'native_sun_degrees': '19', // Default - should be calculated from birth data
        'native_moon_degrees': '17', // Default - should be calculated from birth data
        'native_star_no': '26', // Default - should be calculated from birth data
        'native_gender': nativeUser.gender.toLowerCase(),
        'native_rasi_no': '12', // Default - should be calculated from birth data
        'match_details': matchDetails.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Bulk South Match data: $e');
    }
  }

  /// Festivals - Get Festival Information for a Date
  static Future<dynamic> getFestivals(DateTime date, double latitude, double longitude) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      final url = Uri.parse('$baseUrl/panchang/festivals');

      final response = await http.get(url.replace(queryParameters: {
        'api_key': apiKey,
        'date': '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}',
        'tz': '5.5',
        'lat': latitude.toString(),
        'lon': longitude.toString(),
        'lang': 'ne',
      }));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch Festivals data: $e');
    }
  }

  /// Specific Dasha
  static Future<Map<String, dynamic>?> getSpecificDasha(
    UserData user, {
    required String mahadasha,
    required String antardasha,
    required String paryantardasha,
    required String sookshmadasha,
  }) async {
    try {
      // Get working API key from GitHub
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;

      // Build query parameters
      final params = <String, String>{
        'api_key': apiKey,
        'dob': user.formattedDateForAPI,
        'tob': user.formattedTimeForAPI,
        'lat': user.latitude.toString(),
        'lon': user.longitude.toString(),
        'tz': user.timezoneForAPI,
        'md': mahadasha,
        'ad': antardasha,
        'pd': paryantardasha,
        'sd': sookshmadasha,
        'lang': 'ne',
      };

      // Build URL
      final uri = Uri.parse('$baseUrl/dashas/specific-sub-dasha').replace(queryParameters: params);

      print('🌐 Making Specific Dasha API call');
      print('🔑 Using API key: ${apiKey.substring(0, 8)}...');

      // Make HTTP request
      final response = await http.get(uri).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 200) {
          print('✅ Specific Dasha API call successful');
          return data['response'];
        } else {
          print('❌ API error: ${data['message'] ?? 'Unknown error'}');
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        print('❌ HTTP error: ${response.statusCode}');
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Specific Dasha API call failed: $e');
      rethrow;
    }
  }

  /// Pita Dosh Analysis
  static Future<Map<String, dynamic>?> getPitaDosh(UserData user) async {
    return await makeApiCall(
      endpoint: 'dosha/pita-dosh',
      user: user,
    );
  }
  

  
  /// Dasha Periods (Future feature)
  static Future<Map<String, dynamic>?> getDashaPeriods(UserData user, {String dashaType = 'major'}) async {
    return await makeApiCall(
      endpoint: 'horoscope/dasha-periods',
      user: user,
      additionalParams: {'dasha_type': dashaType},
    );
  }
  
  /// Compatibility Analysis (Future feature)
  static Future<Map<String, dynamic>?> getCompatibility(UserData user1, UserData user2) async {
    try {
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;
      
      final params = <String, String>{
        'api_key': apiKey,
        'dob1': user1.formattedDateForAPI,
        'tob1': user1.formattedTimeForAPI,
        'lat1': user1.latitude.toString(),
        'lon1': user1.longitude.toString(),
        'dob2': user2.formattedDateForAPI,
        'tob2': user2.formattedTimeForAPI,
        'lat2': user2.latitude.toString(),
        'lon2': user2.longitude.toString(),
        'tz': user1.timezoneForAPI,
        'lang': 'ne',
      };
      
      final uri = Uri.parse('$baseUrl/compatibility/match-making').replace(queryParameters: params);
      
      print('🌐 Making compatibility API call');
      
      final response = await http.get(uri).timeout(const Duration(seconds: 30));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Compatibility API call failed: $e');
      rethrow;
    }
  }
  
  /// Panchang Details (Future feature)
  static Future<Map<String, dynamic>?> getPanchang(DateTime date, double lat, double lon) async {
    try {
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;
      
      final formattedDate = '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      
      final params = <String, String>{
        'api_key': apiKey,
        'date': formattedDate,
        'lat': lat.toString(),
        'lon': lon.toString(),
        'tz': '5.75', // Nepal timezone
        'lang': 'ne',
      };
      
      final uri = Uri.parse('$baseUrl/panchang/panchang').replace(queryParameters: params);
      
      print('🌐 Making Panchang API call');
      
      final response = await http.get(uri).timeout(const Duration(seconds: 30));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 200) {
          return data['response'];
        } else {
          throw Exception('API Error: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Panchang API call failed: $e');
      rethrow;
    }
  }
  
  /// Gemstone Recommendation (Future feature)
  static Future<Map<String, dynamic>?> getGemstoneRecommendation(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/gemstone-recommendation',
      user: user,
    );
  }
  
  /// Rudraksha Recommendation (Future feature)
  static Future<Map<String, dynamic>?> getRudrakshaRecommendation(UserData user) async {
    return await makeApiCall(
      endpoint: 'horoscope/rudraksha-recommendation',
      user: user,
    );
  }
  
  /// Numerology Analysis (Future feature)
  static Future<Map<String, dynamic>?> getNumerologyAnalysis(UserData user) async {
    return await makeApiCall(
      endpoint: 'numerology/basic-details',
      user: user,
    );
  }
  
  /// Test API connectivity
  static Future<bool> testApiConnectivity() async {
    try {
      final apiKey = await GitHubApiManager.getWorkingApiKey();
      final baseUrl = GitHubApiManager.baseUrl;
      
      // Simple test call
      final testUrl = Uri.parse(
        '$baseUrl/dosha/mangal-dosh'
        '?api_key=$apiKey'
        '&dob=01/01/1990'
        '&tob=12:00'
        '&lat=27.7172'
        '&lon=85.3240'
        '&tz=5.75'
        '&lang=en'
      );
      
      final response = await http.get(testUrl).timeout(const Duration(seconds: 5));
      
      final isWorking = response.statusCode == 200;
      print(isWorking ? '✅ API connectivity test passed' : '❌ API connectivity test failed');
      
      return isWorking;
    } catch (e) {
      print('❌ API connectivity test failed: $e');
      return false;
    }
  }
  
  /// Get API status and configuration
  static Map<String, dynamic> getApiStatus() {
    final githubStatus = GitHubApiManager.getStatus();
    
    return {
      'github_config': githubStatus,
      'supported_endpoints': [
        'dosha/mangal-dosh',
        'dosha/manglik-dosh',
        'dosha/kaalsarp-dosh',
        'dosha/papasamaya',
        'dosha/pitra-dosh',
        'dashas/maha-dasha',
        'dashas/maha-dasha-predictions',
        'dashas/antar-dasha',
        'dashas/char-dasha-current',
        'dashas/char-dasha-main',
        'dashas/char-dasha-sub',
        'dashas/current-mahadasha',
        'dashas/current-mahadasha-full',
        'dashas/paryantar-dasha',
        'dashas/specific-sub-dasha',
        'dashas/yogini-dasha-main',
        'dashas/yogini-dasha-sub',
        'extended-horoscope/find-sun-sign',
        'extended-horoscope/find-moon-sign',
        'extended-horoscope/find-ascendant',
        'extended-horoscope/current-sade-sati',
        'extended-horoscope/extended-kundli-details',
        'extended-horoscope/shad-bala',
        'extended-horoscope/sade-sati-table',
        'horoscope/planet-details',
        'horoscope/chart-image',
        'horoscope/dasha-periods',
        'compatibility/match-making',
        'panchang/panchang',
        'horoscope/gemstone-recommendation',
        'horoscope/rudraksha-recommendation',
        'numerology/basic-details',
      ],
      'default_language': 'ne',
      'fallback_language': 'hi',
    };
  }


}
