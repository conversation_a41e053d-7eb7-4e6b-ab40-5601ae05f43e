import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/weather_model.dart';

class WeatherService {
  static const String _apiKey = '0d8cbc259be84a018ba35846252207';
  static const String _baseUrl = 'http://api.weatherapi.com/v1';
  
  // Get current weather and 3-day forecast for any location
  static Future<WeatherForecast> getWeatherForecast(String location) async {
    try {
      final url = '$_baseUrl/forecast.json?key=$_apiKey&q=$location&days=3&aqi=no&alerts=no';

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NepaliCalendarApp/1.0',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeatherForecast.fromJson(data);
      } else {
        throw Exception('Weather data not found for $location');
      }
    } catch (e) {
      throw Exception('Failed to fetch weather data: $e');
    }
  }

  // Get weather forecast for Nepal city (with Nepal suffix)
  static Future<WeatherForecast> getNepalCityForecast(String cityName) async {
    return getWeatherForecast('$cityName,Nepal');
  }

  // Get current weather for any location
  static Future<WeatherData> getCurrentWeather(String location) async {
    try {
      final url = '$_baseUrl/current.json?key=$_apiKey&q=$location&aqi=no';

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NepaliCalendarApp/1.0',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeatherData.fromJson(data);
      } else {
        throw Exception('Weather data not found for $location');
      }
    } catch (e) {
      throw Exception('Failed to fetch weather data: $e');
    }
  }

  // Search locations by name
  static Future<List<LocationResult>> searchLocations(String query) async {
    try {
      final url = '$_baseUrl/search.json?key=$_apiKey&q=$query';

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NepaliCalendarApp/1.0',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as List;
        return data.map((item) => LocationResult.fromJson(item)).toList();
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  // Get weather for multiple Nepal cities (for homepage widget)
  static Future<List<WeatherData>> getNepalCitiesWeather() async {
    List<WeatherData> weatherList = [];

    // Get weather for major Nepal cities
    final majorCities = ['Kathmandu,Nepal', 'Pokhara,Nepal', 'Biratnagar,Nepal'];

    for (String city in majorCities) {
      try {
        final weather = await getCurrentWeather(city);
        weatherList.add(weather);
      } catch (e) {
        // Skip cities that fail to load
        continue;
      }
    }

    return weatherList;
  }

  // Get weather icon URL from WeatherAPI
  static String getWeatherIconUrl(String iconPath) {
    if (iconPath.startsWith('//')) {
      return 'https:$iconPath';
    } else if (iconPath.startsWith('/')) {
      return 'https://cdn.weatherapi.com$iconPath';
    }
    return iconPath;
  }

  // Get weather condition icon for Flutter
  static String getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return '☀️';
      case 'partly cloudy':
        return '⛅';
      case 'cloudy':
      case 'overcast':
        return '☁️';
      case 'mist':
      case 'fog':
        return '🌫️';
      case 'patchy rain possible':
      case 'light rain':
      case 'moderate rain':
        return '🌧️';
      case 'heavy rain':
        return '⛈️';
      case 'thundery outbreaks possible':
      case 'thunderstorm':
        return '⚡';
      case 'snow':
        return '❄️';
      default:
        return '🌤️';
    }
  }

  // Get background gradient colors based on weather condition
  static List<int> getWeatherColors(String condition) {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return [0xFF87CEEB, 0xFF4682B4, 0xFF1E90FF]; // Blue sky
      case 'partly cloudy':
        return [0xFF87CEEB, 0xFF708090, 0xFF4682B4]; // Mixed blue-gray
      case 'cloudy':
      case 'overcast':
        return [0xFF708090, 0xFF2F4F4F, 0xFF1C1C1C]; // Gray
      case 'mist':
      case 'fog':
        return [0xFFB0C4DE, 0xFF778899, 0xFF2F4F4F]; // Light gray
      case 'patchy rain possible':
      case 'light rain':
      case 'moderate rain':
      case 'heavy rain':
        return [0xFF4682B4, 0xFF2F4F4F, 0xFF191970]; // Dark blue
      case 'thundery outbreaks possible':
      case 'thunderstorm':
        return [0xFF2F2F2F, 0xFF1C1C1C, 0xFF000000]; // Dark
      case 'snow':
        return [0xFFE6E6FA, 0xFFB0C4DE, 0xFF4682B4]; // Light blue
      default:
        return [0xFF1A1A2E, 0xFF16213E, 0xFF0F3460]; // Default dark
    }
  }

  // Check if it's day or night based on current time
  static bool isDayTime() {
    final now = DateTime.now();
    final hour = now.hour;
    return hour >= 6 && hour < 18; // 6 AM to 6 PM is day
  }

  // Get appropriate greeting based on time
  static String getTimeGreeting() {
    final now = DateTime.now();
    final hour = now.hour;
    
    if (hour >= 5 && hour < 12) {
      return 'शुभ बिहान';
    } else if (hour >= 12 && hour < 17) {
      return 'शुभ दिउँसो';
    } else if (hour >= 17 && hour < 21) {
      return 'शुभ साँझ';
    } else {
      return 'शुभ रात्री';
    }
  }

  // Format last updated time in Nepali
  static String formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);
    
    if (difference.inMinutes < 1) {
      return 'अहिले अपडेट भयो';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} मिनेट अगाडि';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} घण्टा अगाडि';
    } else {
      return '${difference.inDays} दिन अगाडि';
    }
  }

  // Validate API response
  static bool isValidResponse(Map<String, dynamic> data) {
    return data.containsKey('location') && 
           data.containsKey('current') && 
           data['location']['country'] == 'Nepal';
  }
}
