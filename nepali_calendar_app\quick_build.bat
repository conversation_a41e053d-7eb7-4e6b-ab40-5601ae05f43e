@echo off
echo ==========================================
echo QUICK APK BUILD - ERROR FIX
echo ==========================================

echo Step 1: Kill processes...
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1

echo Step 2: Clean project...
flutter clean

echo Step 3: Remove gradle cache...
if exist "%USERPROFILE%\.gradle" rmdir /s /q "%USERPROFILE%\.gradle"

echo Step 4: Set environment...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set GRADLE_OPTS=-Xmx1G -XX:MaxMetaspaceSize=512M
set FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
set PUB_HOSTED_URL=https://pub.flutter-io.cn

echo Step 5: Get dependencies...
flutter pub get

echo Step 6: Build APK...
flutter build apk --release

echo ==========================================
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo ✅ SUCCESS! APK built at:
    echo build\app\outputs\flutter-apk\app-release.apk
    dir "build\app\outputs\flutter-apk\app-release.apk"
) else (
    echo ❌ Build failed, trying debug build...
    flutter build apk --debug
    if exist "build\app\outputs\flutter-apk\app-debug.apk" (
        echo ✅ DEBUG APK built at:
        echo build\app\outputs\flutter-apk\app-debug.apk
    )
)
echo ==========================================
pause
