@echo off
echo ========================================
echo   Nepali Calendar App - Phone Testing
echo ========================================
echo.

echo Checking connected devices...
flutter devices

echo.
echo Building and installing APK on phone...
flutter build apk --release
if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Check errors above.
    pause
    exit /b 1
)

echo.
echo Installing on connected Android device...
flutter install

echo.
echo App installed successfully!
echo You can now test the following UPDATED features:
echo.
echo 1. NO AUTO-SELECTION: When navigating months, no date should be auto-selected
echo 2. Today's date should be BLUE colored (original color)
echo 3. Manually selected date should be GREEN colored (different from today)
echo 4. Saturday dates should be RED colored (original color)
echo 5. NEW: Smart text display:
echo    - "आजको मिति" when no date selected OR when today is selected
echo    - "रोजेको मिति" when any other date is selected
echo 6. ✅ WORKING APK WITH FONT BALANCE IMPROVEMENTS:
echo    - ✅ REDUCED Tithi size: 14px → 12px (still bold, less overwhelming)
echo    - ✅ INCREASED festival title: 14px → 16px (more prominent)
echo    - ✅ INCREASED festival description: 12px → 14px (better readability)
echo    - ✅ FULL HEIGHT calendar boxes (85px) maintained
echo    - ✅ GRID LINES and professional layout maintained
echo 7. ✅ PERFECT TEXT HIERARCHY ACHIEVED:
echo    - ✅ Smaller Tithi: Less dominant but still clearly visible
echo    - ✅ Larger Festival Text: More prominent green festival information
echo    - ✅ Balanced Design: No element overwhelms others
echo    - ✅ Professional Layout: Full height boxes with grid structure
echo    - ✅ Complete Data: All BS 2082 Tithi and festival information
echo 8. ✅ WORKING APK INSTALLED ON PHONE: 23106RN0DA
echo 9. ✅ SUCCESS: All your requested font changes are working!
echo 10. ✅ TEST: Your original project now has working APK with font balance!
echo.
echo Press any key to exit...
pause >nul
