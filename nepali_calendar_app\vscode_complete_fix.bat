@echo off
echo ==========================================
echo VS Code Complete Setup - All Errors Fixed
echo ==========================================

echo Step 1: Checking Java Version...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not found! Please install Java 17
    pause
    exit /b 1
)
echo ✅ Java found
echo.

echo Step 2: Setting Environment Variables...
set JAVA_HOME=C:\Program Files\Java\jdk-17
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\sdk
set PATH=%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%
echo ✅ Environment variables set
echo.

echo Step 3: Stopping all Gradle daemons...
cd android
gradlew --stop 2>nul
cd ..
echo ✅ Gradle daemons stopped
echo.

echo Step 4: Cleaning Flutter project...
flutter clean
echo ✅ Flutter project cleaned
echo.

echo Step 5: Removing Gradle cache...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
    echo ✅ Gradle cache removed
) else (
    echo ✅ Gradle cache already clean
)
echo.

echo Step 6: Getting Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies downloaded
echo.

echo Step 7: Building APK with Java 17...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo ✅ APK built successfully
echo.

echo ==========================================
echo ✅ ALL SETUP COMPLETE!
echo ✅ VS Code is ready to use
echo ==========================================
echo.
echo Next steps:
echo 1. Open VS Code
echo 2. Open this project folder
echo 3. All errors should be resolved!
echo.
pause
